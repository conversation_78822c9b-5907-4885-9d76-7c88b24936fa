import legendOptions from '../e-charts/legend';
import tooltipOptions from '../e-charts/tooltip';

const treemapDefaultOptions = {
  dataType: 'static',
  data: {
    columns: ['部门', '产品', '销售额'],
    values: [
      ['第一部门', '产品A', 40],
      ['第一部门', '产品B', 30],
      ['第一部门', '产品C', 10],
      ['第二部门', '产品D', 20],
      ['第二部门', '产品E', 60]
    ]
  },
  config: {
    type: 'treemap',
    tooltip: tooltipOptions,
    legend: legendOptions,
    series: [{
      type: 'treemap',
      data: []
    }]
  },
  dataMapping: {
    treemap: {
      parentField: '部门',
      childField: '产品',
      valueField: '销售额'
    }
  }
};
export default treemapDefaultOptions; 