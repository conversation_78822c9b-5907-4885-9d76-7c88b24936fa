<template>
  <div ref="markdownContainer" class="mk-content">
    <div v-html="state.htmlStr"/>
  </div>
</template>

<script lang="ts" setup>
import MarkdownIt from "markdown-it";
import hljs from "highlight.js";
import mdKatex from "@traptitech/markdown-it-katex";
import "highlight.js/styles/atom-one-dark.css";
import {ref, reactive, watch, onMounted, nextTick} from 'vue';

const markdownContainer = ref(null);
// 保证每一个复制按钮只被绑定一次复制方法
const isCopyEventAttached = ref(false);

const props = defineProps({
  answer: {
    type: String,
    required: true
  },
  type: {
    type: String,
    required: false
  }
})

const mdi = new MarkdownIt({
  html: true,
  linkify: true,
  highlight(code: any, language: any) {
    const validLang = !!(language && hljs.getLanguage(language))
    if (validLang) {
      const lang = language ?? ''
      return highlightBlock(hljs.highlight(lang, code, true).value, lang)
    }
    return highlightBlock(hljs.highlightAuto(code).value, '')
  }
})
mdi.use(mdKatex, {blockClass: 'katexmath-block rounded-md p-[10px]', errorColor: ' #cc0000'})

function highlightBlock(str: any, lang: any) {
  // SVG图标HTML
  // const svgIconHTML = '<image style="margin-right: 4px" src="src/assets/icons/svg/clipboard-white.svg" class="svg-icon"/>';

  // 复制按钮HTML，包含SVG图标
  // const copyButtonHTML = `<button style="background-color: transparent; border: none; color: white; cursor: pointer; display: flex; align-items: center;" class="copy-button">${svgIconHTML}copy</button>`;

  // 代码块头部模板
  // const headerTemplate = `<div class="pre-code-header" style="display: flex; justify-content: space-between; align-items: center; background-color: #343541; padding: 5px 5px 5px 10px; color: white; border-radius: 0.1rem 0.1rem 0rem 0rem"><span class="code-block-header__lang">${lang}</span>${copyButtonHTML}</div>`;
  const headerTemplate = `<div class="pre-code-header" style="display: flex; justify-content: space-between; align-items: center; background-color: #343541; padding: 5px 5px 5px 10px; color: white; border-radius: 0.1rem 0.1rem 0rem 0rem"><span class="code-block-header__lang">${lang}</span></div>`;

  // 完整的代码块模板
  const codeBlockTemplate = `<pre class="pre-code-box">${headerTemplate}<div class="pre-code"><code style="border-radius: 0rem 0rem 0.1rem 0.1rem" class="hljs code-block-body ${lang}">${str}</code></div></pre>`;

  return codeBlockTemplate;
}

const copyToClipboard = (event: any) => {
  const codeBlock = event.target.closest('.pre-code-box').querySelector('.hljs');
  if (codeBlock) {
    navigator.clipboard.writeText(codeBlock.textContent || codeBlock.innerText).then(() => {
      // ElMessage.success('复制成功');
      console.log('复制成功')
    }).catch(() => {
      console.log('复制失败')
      // ElMessage.error('复制失败');
    });
  }
};

// 转义危险标签
function escapeDangerousTags(str: string) {
  if (!str.includes('alert')) {
    return str;
  }
  const dangerousTags = [
    'script', 'img', 'iframe', 'object', 'embed', 'link', 'style', 'base',
    'form', 'input', 'button', 'textarea', 'svg', 'math', 'audio', 'video',
    'source', 'frame', 'frameset', 'applet', 'meta'
  ];

  // 1. 保护 markdown 行内代码、代码块、数学公式
  const protectPatterns = [
    /`[^`]*`/g, // 行内代码
    /```[\s\S]*?```/g, // 多行代码块
    /\$[^$]+\$/g, // 行内公式
    /\$\$[\s\S]+?\$\$/g // 多行公式
  ];
  let protectedMap: Record<string, string> = {};
  let idx = 0;
  let protectedStr = str.replace(
    new RegExp(protectPatterns.map(r => r.source).join('|'), 'g'),
    (match) => {
      const key = `__PROTECTED_${idx++}__`;
      protectedMap[key] = match;
      return key;
    }
  );

  // 2. 过滤危险标签
  const tagPattern = new RegExp(
    `<\\s*(${dangerousTags.join('|')})\\b[^>]*>([\\s\\S]*?)<\\/\\s*\\1\\s*>|<\\s*(${dangerousTags.join('|')})\\b[^>]*/?>`,
    'gi'
  );
  protectedStr = protectedStr.replace(tagPattern, (match) => {
    return '`' + match.replace(/`/g, '\\`') + '`';
  });

  // 3. 还原被保护的内容
  Object.keys(protectedMap).forEach(key => {
    protectedStr = protectedStr.replace(key, protectedMap[key]);
  });

  return protectedStr;
}


const getMdiText = (value: string) => {
  let html = mdi.render(escapeDangerousTags(value.replace(/\\n/g, '\n ').replace(/###/g, '\n###').replace(/ - /g, '\n - '))) as any
  // const res = html.replaceAll('<p>', '')
  return html
}

const state = reactive({
  htmlStr: ''
})


watch(() => props.answer, () => {
  state.htmlStr = getMdiText(props.answer);
}, {immediate: true});

watch(() => props.type, (newVal) => {
  if (newVal == 'textMessage') {
    state.htmlStr = getMdiText(props.answer);
    addFatherCopyEventListener();
  }
}, {immediate: true});

function addFatherCopyEventListener() {
  // 确保父元素存在
  if (!isCopyEventAttached.value && markdownContainer && markdownContainer.value) {
    // 使用事件委托添加监听器
    //@ts-ignore
    markdownContainer.value.addEventListener('click', (event) => {
      if (event.target.classList.contains('copy-button')) {
        copyToClipboard(event);
      }
    });
    isCopyEventAttached.value = true;
  }
}

onMounted(() => {
  state.htmlStr = getMdiText(props.answer) //htmlStr就是已经包含html样式的文本
  nextTick(() => {
    addFatherCopyEventListener();
  });
});

</script>

<style scoped lang="scss">
.chat-messages img{
  width: 200px;
}

.mk-content img{
  width: 200px;
}

.message-content img{
  width: 200px;
}

.mk-content {
  width: 100%;
  font-size: 14px;
  line-height: 24px;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,
  Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei',
  sans-serif;
  overflow-x: auto;
  text-align: left;
  
  :deep(ol) {
    margin: 0;
    padding: 0 0 0 20px !important;
    list-style: decimal !important;

    ul {
      margin: 0;
      padding: 0 0 0 16px !important;
      list-style: circle;
    }
  }

  img {
    width: 100%;
  }

  :deep(table) {
    border: 1px solid #eeeeee;
    th {
      
    }
    th, td {
      padding: 6px;
      text-align: left;
      border-bottom: 1px solid #eeeeee;
      border-right: 1px solid #eeeeee;
    }
    thead {
      background-color: #f5f5f5;
    }
  }

  :deep(.pre-code-header) {
    background: #286dff0d !important;
  }

  :deep(.pre-code) {
    code {
      width: 100%;
      display: block;
      margin-bottom: 10px;
      color: #939393;
      font-size: 13px;
      line-height: 1.5;
      background: #286dff0d;
      padding: 10px 16px;
      border-radius: 6px;
      // 允许非空白字符换行
      white-space: pre-wrap;
      // 旧版浏览器使用，允许长单词换行
      word-wrap: break-word;
      // 允许长单词或 URL 换行
      overflow-wrap: break-word;
    }
  }
}


</style>
