import legendOptions from '../e-charts/legend';
import tooltipOptions from '../e-charts/tooltip';

const lineDefaultOptions = {
  dataType: 'static',
  data: {
    columns: ['日期', '数值'],
    values: [
      ['Mon', 150],
      ['Tue', 230],
      ['Wed', 224],
      ['Thu', 218],
      ['Fri', 135],
      ['Sat', 147],
      ['Sun', 260]
    ]
  },
  config: {
    type: 'line',
    tooltip: tooltipOptions,
    legend: legendOptions,
    xAxis: {
      type: 'category',
      data: []
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        type: 'line',
        data: []
      }
    ]
  },
  dataMapping: {
    xField: '日期',
    yFields: ['数值'],
    seriesNames: ['数值']
  }
};
export default lineDefaultOptions; 