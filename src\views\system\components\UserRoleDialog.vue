<template>
  <a-modal 
    v-model:visible="showDialog"
    title="添加角色" 
    @ok="handleOk" 
    @cancel="handleCancel" 
    :width="700"
    cancelText="取消" 
    okText="确定">
    <div class="role-edit-container">
      <a-form
        layout="inline"
        :model="searchForm"
        @finish="handleFinish"
      >
        <a-form-item label="名称">
          <a-input v-model:value="searchForm.name" placeholder="请输入角色名称">
            <template #prefix><UserOutlined style="color: rgba(0, 0, 0, 0.25)" /></template>
          </a-input>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit" >搜索</a-button>
        </a-form-item>
        <a-form-item @click="resetSearchForm">
          <a-button>重置</a-button>
        </a-form-item>
      </a-form>

      <div class="role-list">
        <a-table
          :columns="columns"
          :data-source="roleList"
          :loading="loading"
          rowKey="id"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag color="green" v-if="record.status === 'enabled'">正常</a-tag>
              <a-tag color="red" v-else>停用</a-tag>
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { h, ref, reactive } from 'vue';
import type { PropType } from 'vue';
import { message } from 'ant-design-vue';
import type { GetRoleListDTO, SaveRoleDTO, AddUserRoleDTO } from '@/types/system';
import type { UnwrapRef } from 'vue';
import type { FormProps } from 'ant-design-vue';
import * as api from '@/api/system';

const props = defineProps({
  userId: {
    type: Number,
  },
  roleIds: {
    type: Array as PropType<(number | string | undefined)[]>,
    default: []
  }
});

const emit = defineEmits([
  'refreshData',
  'refreshDataForCreate'
]);

const showDialog = ref<boolean>(false);

// 表格列定义
const columns = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    customRender: ({ record } : { record: any }) => {
      return h('div', {
        style: {
          display: '-webkit-box',
          WebkitBoxOrient: 'vertical',
          WebkitLineClamp: 2,
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          wordBreak: 'break-all',
        }
      }, record.description);
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
];

interface SearchForm {
  name: string;
}

const searchForm: UnwrapRef<SearchForm> = reactive({
  name: '',
});
const selectedRowKeys = ref<(string | number)[]>([]); // 选中的行键数组

const onSelectChange = (selectedRowKeysValue: string[]) => {
  selectedRowKeys.value = selectedRowKeysValue;
};

const handleFinish: FormProps['onFinish'] = values => {
  fetchRoleList();
};

const resetSearchForm = () => {
  searchForm.name = '';
  fetchRoleList();
};

const roleList = ref<SaveRoleDTO[]>([]);
const loading = ref(false);

const fetchRoleList = async () => {
  loading.value = true;
  try {
    const formData: GetRoleListDTO = {}
    if (searchForm.name) {
      formData.name = searchForm.name;
    }
    const res = await api.getRoleList(formData);
    roleList.value = res.data.filter(item => !props.roleIds.includes(item.id) && item.status === 'enabled');
  } catch (error: any) {
    message.error(error?.message || '获取角色列表失败');
  } finally {
    loading.value = false;
  }
};

const handleCancel = () => {
  searchForm.name = '';
  roleList.value = [];
  selectedRowKeys.value = [];
  showDialog.value = false;
}

const handleOpen = async () => {
  fetchRoleList();
  showDialog.value = true;
}

const handleOk = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择角色');
    return;
  }
  if (props.userId) {
    const fomData :AddUserRoleDTO = {
      user_roles: [],
    }
    selectedRowKeys.value.map((item) => {
      fomData.user_roles.push({
        user_id: Number(props.userId),
        role_id: Number(item),
      })
    });
    await api.addUserRole(fomData);
    message.success('添加角色成功');
    emit('refreshData');
  } else  {
    const selectRoles = roleList.value.filter(item => selectedRowKeys.value.includes(item?.id || ''));
    emit('refreshDataForCreate', selectRoles)
  }
  searchForm.name = '';
  roleList.value = [];
  selectedRowKeys.value = [];
  showDialog.value = false;
}

defineExpose({
  handleOpen,
});

</script>

<style scoped lang="scss">
  .role-list {
    margin-top: 16px;
  }
</style> 