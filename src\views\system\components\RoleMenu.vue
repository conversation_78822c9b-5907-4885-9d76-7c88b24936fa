<template>
  <div class="menu-manage-container">
    <div class="tool-line">
      <div class ="right">
        <a-button type="primary" @click="handleOpenEdit">
          <plus-outlined /> 添加菜单
        </a-button>
        <a-popconfirm
          title="确定要删除菜单吗?"
          ok-text="确定"
          cancel-text="取消"
          :disabled="selectedRowKeys.length === 0"
          @confirm="deleteMenu()"
        >
          <a-button type="primary" danger class="btn-item" :disabled="selectedRowKeys.length === 0" >批量删除</a-button>
        </a-popconfirm>
      </div>
    </div>
    <a-divider>菜单列表</a-divider>
    <div class="menu-list">
      <a-table
        :columns="columns"
        :data-source="menuList"
        :loading="loading"
        rowKey="id"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag color="green" v-if="record.status === 0">正常</a-tag>
            <a-tag color="red" v-else>停用</a-tag>
          </template>
          <template v-else-if="column.key === 'operation'">
            <a-space>
              <a-popconfirm
                title="确定要删除此菜单吗?"
                ok-text="确定"
                cancel-text="取消"
                @confirm="deleteMenu(record)"
              >
                <a class="delete-link">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>
    <RoleMenuDialog ref="roleMenuDialogRef" 
      :menuIds="menuIds" 
      :roleId="props.id" 
      @refreshData="handleRefreshData()"
      @refreshDataForCreate="refreshDataForCreate" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import RoleMenuDialog from './RoleMenuDialog.vue';
import type { RoleMenuItem, SaveMenuDTO } from '@/types/system';
import { sortMenuByOrder } from '../hooks/helper';
import { useRouter } from 'vue-router';
import * as api from '@/api/system';

const router = useRouter();

const props = defineProps({
  id: {
    type: Number,
    default: false
  }
});

// 表格列定义
const columns = [
  {
    title: '名称',
    dataIndex: 'menu_name',
    key: 'menu_name',
  },
  {
    title: '操作',
    key: 'operation',
    width: 150
  },
];

const menuIds = computed(() => {
  const ids: (number | string | undefined)[] = [];
  menuList.value.map((item) => {
    ids.push(item.menu_id);
    if (item.children?.length) {
      item.children.map((child) => {
        ids.push(child.menu_id);
      })
    }
  });
  return ids;
})

const selectedRowKeys = ref<string[]>([]); // 选中的行键数组
const roleMenuDialogRef = ref();

const onSelectChange = (selectedRowKeysValue: string[]) => {
  selectedRowKeys.value = selectedRowKeysValue;
};

// 提示词列表和加载状态
const menuList = ref<RoleMenuItem[]>([]);
const loading = ref(false);

// 获取提示词列表
const fetchMenuList = async () => {
  loading.value = true;
  try {
    const res = await api.getRoleMenuList({ 
      role_id: props.id, 
    });
    menuList.value = sortMenuByOrder(res.data) as RoleMenuItem[];
  } catch (error) {
    console.error('获取用户菜单列表失败', error);
  } finally {
    loading.value = false;
  }
};

const resetMenuList = () => {
  menuList.value = [];
  selectedRowKeys.value = [];
}

const handleOpenEdit = () => {
  roleMenuDialogRef.value?.handleOpen();
};

// 删除提示词
const deleteMenu = async (data?: RoleMenuItem) => {
  let ids: (string | number)[] = [];
  if (data) {
    ids.push(data.id);
  } else {
    ids = selectedRowKeys.value;
  }
  menuList.value.map((item) => {
    if (ids.includes(item.id)) {
      if (item.children?.length) {
        item.children.map((child) => {
          ids.push(child.id);
        })
      }
    }
  });
  if (props.id) {
    await api.deleteRoleMenu(props.id, { ids });
    message.success('删除成功');
    fetchMenuList();
  } else {
    const menuListTmp: RoleMenuItem[] = [];
    menuList.value.map((item) => {
      const menuItem: RoleMenuItem = { ...item };
      if (item.children?.length) {
        menuItem.children = menuItem.children?.filter(item =>!ids.includes(item.id));
      }
      if (!ids.includes(item.id) && !(menuItem.menu_name === '系统管理' && menuItem.children?.length === 0)) {
        menuListTmp.push(menuItem);
      }
    });
    menuList.value = menuListTmp;
  }
  if (!data) {
    selectedRowKeys.value = [];
  }
};

const handleRefreshData = () => {
  fetchMenuList();
  selectedRowKeys.value = [];
}

const refreshDataForCreate = (addMenuList: SaveMenuDTO[]) => {
  menuList.value = [];
  addMenuList.map((item) => {
    menuList.value.push({
      id: item.id || '',
      menu_id: item.id || '',
      menu_name: item.name || '',
      children: item.children?.map((child) => {
        return {
          id: child.id || '',
          menu_id: child.id || '',
          menu_name: child.name || '',
        }
      })
    }) 
  });
}

defineExpose({
  menuList,
  fetchMenuList,
  resetMenuList,
});
</script>

<style scoped>
.menu-manage-container {
  padding: 24px;
}

.tool-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  margin-top: 10px;
  .right {
    display: flex;
    .btn-item {
    margin-left: 10px;
    }
  }
}

.menu-list {
  margin-top: 16px;
}

.delete-link {
  color: #ff4d4f;
}
</style> 