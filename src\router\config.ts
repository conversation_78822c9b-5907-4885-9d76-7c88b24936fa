import type { RouteRecordRaw } from 'vue-router'; 

type RouteRecordMap = Record<string, RouteRecordRaw[]>;

export const dynamicRouteMap: RouteRecordMap = {
  '在线对话': [
    {
      path: 'chat',
      name: 'Chat',
      component: () => import('../views/chat/chat-index.vue'),
      meta: { 
        requiresAuth: true,
        keepAlive: true,
        title: 'Chat Normal',
      },
    },
    {
      path: '/chat/detail/:conversationId',
      name: 'ChatDetail',
      meta: {
        requiresAuth: true,
        keepAlive: true,
        title: 'Chat Normal',
      },
      component: () => import('../views/chat/chat-detail-page.vue'),
    },
    {
      path: 'chatNormal',
      name: 'ChatNormal',
      component: () => import('../views/chat/chat-normal-page.vue'),
      meta: { 
        requiresAuth: true,
        keepAlive: true,
        title: 'Chat Normal',
      },
    },
    {
      path: 'chatDb',
      name: 'ChatDB',
      component: () => import('../views/chatDb/chat-db-page.vue'),
      meta: { 
        requiresAuth: true,
        keepAlive: true,
        title: 'Chat Db',
      },
    },
    {
      path: '/chatDb/detail/:conversationId',
      name: 'ChatDbDetail',
      meta: {
        requiresAuth: true,
        keepAlive: true,
        title: 'Chat Db',
      },
      component: () => import('../views/chatDb/chat-db-detail-page.vue'),
    },
    {
      path: 'chatData',
      name: 'ChatData',
      component: () => import('../views/chatData/chat-data-page.vue'),
      meta: { 
        requiresAuth: true,
        keepAlive: true,
        title: 'Chat Data',
      },
    },
    {
      path: '/chatData/detail/:conversationId',
      name: 'ChatDataDetail',
      meta: {
        requiresAuth: true,
        keepAlive: true,
        title: 'Chat Data',
      },
      component: () => import('../views/chatData/chat-data-detail-page.vue'),
    },
  ],
  '看板管理': [
    {
      path: 'dashboard-list',
      name: 'DashboardList',
      component: () => import('../views/dashboard/DashboardList.vue'),
      meta: { 
        requiresAuth: true,
        title: '仪表盘列表'
      }
    },
    {
      path: 'dashboard/:id',
      name: 'Dashboard',
      component: () => import('../views/dashboard/Dashboard.vue'),
      meta: { 
        requiresAuth: true,
        title: '仪表盘详情'
      }
    },
    {
      path: 'dashboard-share/:id',
      name: 'DashboardShare',
      component: () => import('../views/dashboard/DashboardShare.vue'),
      meta: {
        requiresAuth: true,
        title: '看板分享'
      }
    }
  ],
  '模型管理': [
    {
      path: 'model',
      name: 'ModelManage',
      component: () => import('../views/model/Index.vue'),
      meta: {
        requiresAuth: true,
        title: '模型管理'
      }
    }
  ],
  '数据源管理': [
    {
      path: 'database',
      name: 'DatabaseManage',
      component: () => import('../views/database/Index.vue'),
      meta: {
        requiresAuth: true,
        title: '数据源管理'
      }
    },
    {
      path: 'topic',
      name: 'TopicManage',
      component: () => import('../views/database/metadataManager/topicIndex.vue'),
      meta: {
        requiresAuth: true,
        title: '主题管理'
      }
    },
    {
      path: 'metadata',
      name: 'MetadataManage',
      component: () => import('../views/database/metadataManager/metadataIndex.vue'),
      meta: {
        requiresAuth: true,
        title: '元数据管理'
      }
    }
  ],
  '提示词管理' : [
    {
      path: 'prompt',
      name: 'PromptManage',
      component: () => import('../views/prompt/Prompt.vue'),
      meta: {
        requiresAuth: true,
        title: '提示词管理'
      }
    }
  ],
  'SQL在线执行': [
    {
      path: 'onlineSql',
      name: 'onlineSql',
      component: () => import('../views/onlineSQL/index.vue'),
      meta: {
        requiresAuth: true,
        title: 'SQL在线执行'
      },
    }
  ],
  '系统管理': [
    {
      path: 'system/:mode',
      name: 'SystemManage',
      component: () => import('../views/system/System.vue'),
      meta: {
        requiresAuth: true,
        title: '系统管理'
      }
    }
  ],
};

export const noNeedAuthPath = ['/login', '/error', '/welcome'];