.vue-grid-layout {
  position: relative;
  width: 100%;
  min-height: 100%;
  transition: height 0.3s ease;
  overflow: visible;
}

.vue-grid-item {
  transition: all 200ms ease;
  transition-property: left, top, right;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 4px;
  overflow: visible;
  z-index: 1;
}

.vue-grid-item.vue-grid-placeholder {
  background: #f0f0f0;
  opacity: 0.7;
}

.vue-grid-item.resizing {
  opacity: 0.9;
  z-index: 3;
}

.vue-grid-item.vue-draggable-dragging {
  z-index: 3;
  opacity: 0.8;
}

.vue-grid-item > .vue-resizable-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  bottom: 0;
  right: 0;
  background: url('data:image/svg+xml;utf8,<svg width="10" height="10" xmlns="http://www.w3.org/2000/svg"><path d="M0 10V0h1v9h9v1z" fill="%23999"/><path d="M3 10V3h1v6h6v1z" fill="%23999"/><path d="M6 10V6h1v3h3v1z" fill="%23999"/></svg>') no-repeat;
  background-position: bottom right;
  background-size: 10px 10px;
  padding: 0 3px 3px 0;
  cursor: se-resize;
} 