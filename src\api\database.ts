import request from '../utils/request';
import type { ApiResponse } from '../types/api';
import type { DatabaseItem, DatabaseApiResponse } from '../types/database';

/**
 * 获取数据库类型列表
 */
export function getDatabaseTypes(): Promise<ApiResponse<any>> {
  return request.get('/v2/serve/datasource-types');
}

/**
 * 获取数据源列表
 */
export function getDataSources(): Promise<DatabaseApiResponse> {
  return request.get('/v2/serve/datasources');
}

/**
 * 获取数据源详情
 * @param id 数据源ID
 */
export function getDataSourceDetail(id: number): Promise<ApiResponse<DatabaseItem>> {
  return request.get(`/v2/serve/datasources/${id}`);
}

/**
 * 创建数据源
 * @param data 数据源配置数据
 */
export function createDataSource(data: any): Promise<ApiResponse<any>> {
  return request.post('/v2/serve/datasources', data);
}

/**
 * 更新数据源
 * @param id 数据源ID
 * @param data 数据源配置数据
 */
export function updateDataSource(id: number, data: any): Promise<ApiResponse<any>> {
  // 创建一个包含id的新数据对象
  const requestData = {
    ...data,
    id
  };
  return request.put('v2/serve/datasources', requestData);
}

/**
 * 删除数据源
 * @param id 数据源ID
 */
export function deleteDataSource(id: number): Promise<ApiResponse<any>> {
  return request.delete(`/v2/serve/datasources/${id}`);
}

/**
 * 测试数据源连接
 * @param data 数据源连接信息
 */
export function testDataSourceConnection(data: any): Promise<ApiResponse<any>> {
  return request.post('/v2/serve/datasources/test-connection', data);
} 

export function refreshDataSource(id: string): Promise<ApiResponse<boolean>> {
  return request.post(`/v2/serve/datasources/${id}/refresh`, {}, { timeout: 300000 });
}