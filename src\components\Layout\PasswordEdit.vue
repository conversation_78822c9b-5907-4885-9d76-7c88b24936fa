<template>
  <a-modal 
    v-model:visible="showDialog"
    title="修改密码" 
    @ok="handleOk" 
    @cancel="handleCancel" 
    cancelText="取消" 
    okText="确定">
    <div class="password-edit-container">
      <a-form
        ref="passwordFormRef"
        :model="passwordForm"
        name="basic"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
      >
        <a-form-item
          label="旧密码"
          name="oldPassword"
          :rules="[{ required: true, message: '请输入旧密码', trigger: 'blur' }]"
          :key="`old-password-${inputKey}`"
        >
          <a-input-password v-model:value="passwordForm.oldPassword" />
        </a-form-item>
        <a-form-item
          label="密码"
          name="password"
          :rules="[{ required: true, message: '请输入密码', trigger: 'blur' }]"
          :key="`password-${inputKey}`"
        >
          <a-input-password v-model:value="passwordForm.password" />
        </a-form-item>
        <a-form-item
          label="确认密码"
          name="passwordSecond"
          :rules="[{ required: true, message: '请确认密码', trigger: 'blur' }, { validator: checkPassword, trigger: 'blur' }]"
          :key="`password-second-${inputKey}`"
        >
          <a-input-password v-model:value="passwordForm.passwordSecond" />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { useUserStore } from '@/store/user';
import * as api from '@/api/user';

const passwordFormRef = ref();

const passwordForm = reactive<any>({
  id: undefined,
  oldPassword: undefined,
  password: undefined,
  passwordSecond: undefined,
});
const inputKey = ref<number>(0);
const showDialog = ref<boolean>(false);
const userStore = useUserStore();

const resetPasswordForm = () => {
  inputKey.value ++;
  passwordForm.id = undefined;
  passwordForm.oldPassword = undefined;
  passwordForm.password = undefined;
  passwordForm.passwordSecond = undefined;
}

const checkPassword = (_rule: Rule, value: string) => {
  if (!value) {
    return Promise.reject('');
  }
  if (value !== passwordForm.password) {
    return Promise.reject('两次输入密码不一致!');
  }
  return Promise.resolve();
}

const handleCancel = () => {
  showDialog.value = false;
  resetPasswordForm();
}

const handleOk = async () => {
  try {
    // 执行表单校验
    await passwordFormRef.value.validate();
  } catch (_error) {
    message.error('表单校验失败，请检查输入内容');
    return;
  }
  const formData = {
    user_id: userStore.userInfo?.user_id  + '',
    old_password: passwordForm.oldPassword,
    new_password: passwordForm.password,
  }
  await api.updatePassword(formData)
  message.success('修改成功');
  showDialog.value = false;
  resetPasswordForm();
}

const handleOpen = async () => {
  showDialog.value = true;
  inputKey.value ++;
  await nextTick(); // 等待 DOM 更新
  // passwordForm.id = undefined;
  // passwordForm.oldPassword = undefined;
  // passwordForm.password = undefined;
  // passwordForm.passwordSecond = undefined;
}

defineExpose({
  handleOpen,
});
</script>

<style scoped lang="scss">

</style> 