<template>
  <div class="base-chart-wrapper">
    <div v-if="loading" class="chart-loading">
      <a-spin />
    </div>
    <div v-else-if="error" class="chart-error">
      <a-alert type="error" :message="error" />
    </div>
    <slot v-else :options="processedOptions"></slot>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, defineEmits, computed, markRaw } from 'vue';
import { getChartData } from '../../api/chart';
import type { ChartOptions } from '../../types/chart';
import { useDashboardStore } from '../../store/dashboard';
import { debounce } from 'lodash-es'; 

// 定义props
const props = defineProps({
  chartId: {
    type: Number,
    required: true
  },
  dashboardId: {
    type: Number,
    required: true
  },
  autoRefresh: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['dataLoaded', 'error']);

// 使用Pinia store
const dashboardStore = useDashboardStore(props.dashboardId);

// 内部状态
const loading = ref(false);
const error = ref('');
const processedOptions = ref<any>({});

// 添加一个ref来存储上一次的SQL配置
const lastSqlConfig = ref<{
  dataMapping: string;
  dataType: string;
  db_name: string;
  sql: string;
} | null>(null);

// 获取图表配置
const chartOptions = computed(() => {
  return dashboardStore.getChartOptions(props.chartId);
});

// const chartKey = computed(() => {
//   return dashboardStore.getChartKey(props.chartId);
// });

/**
 * 处理图表数据，根据options.dataType转换数据格式
 */
const processChartData = async (options: ChartOptions): Promise<any> => {
  try {
    // 如果是静态数据
    if (options.dataType === 'static' && options.data) {
      // 不再进行数据转换，直接返回原始数据
      lastSqlConfig.value = {
        dataMapping: JSON.stringify(options.dataMapping || {}),
        dataType: 'static',
        db_name: '',
        sql: '',
      }
      return markRaw({
        config: options.config || {},
        data: options.data,
        dataMapping: options.dataMapping
      });
    }
    
    // 如果是SQL数据
    if (options.dataType === 'sql' && options.db_name && options.sql) {
      // 检查SQL配置是否发生变化
      const currentSqlConfig = {
        dataMapping: JSON.stringify(options.dataMapping || {}),
        dataType: options.dataType,
        db_name: options.db_name,
        sql: options.sql
      };

      // 如果SQL配置没有变化，且已有数据，直接返回上次的数据
      if (lastSqlConfig.value && 
          lastSqlConfig.value.dataType === currentSqlConfig.dataType && 
          lastSqlConfig.value.db_name === currentSqlConfig.db_name && 
          lastSqlConfig.value.sql === currentSqlConfig.sql &&
          lastSqlConfig.value.dataMapping === currentSqlConfig.dataMapping &&
          processedOptions.value?.data) {
        return processedOptions.value;
      } else {
        processedOptions.value = null; // 清空上次的数据
      }

      // 更新SQL配置
      lastSqlConfig.value = currentSqlConfig;

      // 调用接口获取数据
      const res = await getChartData({
        db_name: options.db_name,
        sql: options.sql,
        chart_type: options.config?.type || 'Table' // 使用配置中的图表类型或默认值
      });
      
      if (res.success && res.data && res.data.sql_data) {
        const { colunms, values } = res.data.sql_data;
        // 不再进行数据转换，返回原始数据和SQL查询结果
        return markRaw({
          config: options.config || {},
          data: { 
            columns:colunms,
            values:values
          },
          dataMapping: options.dataMapping
        });
      } else {
        throw new Error(res.err_msg || '获取数据失败');
      }
    }
    
    // 如果没有数据或数据类型不支持，返回默认配置
    return markRaw({
      config: options.config || {},
      data: null,
      dataMapping: options.dataMapping
    });
  } catch (error) {
    console.error('处理图表数据出错', error);
    return markRaw({
      config: options.config || {},
      data: null,
      dataMapping: options.dataMapping
    });
  }
};


// 加载图表数据
const loadChartData = async () => {
  if (!props.chartId) return;
  
  loading.value = true;
  error.value = '';
  
  try {
    // 确保Dashboard store已初始化
    if (!dashboardStore.initialized) {
      await dashboardStore.initDashboard();
    }
    
    const options = chartOptions.value;
    
    if (!options) {
      error.value = '图表配置不存在';
      loading.value = false;
      return;
    }
    
    // 处理图表数据，但不做转换，直接获取原始数据
    const rawData = await processChartData(options);

    console.log("图表配置", rawData)
    
    // 存储处理后的原始数据
    processedOptions.value = rawData;
    
    // 传递给子组件，让子组件自己进行数据转换处理
    emit('dataLoaded', rawData);
  } catch (err: any) {
    error.value = err.message || '加载图表数据失败';
    emit('error', error.value);
  } finally {
    loading.value = false;
  }
};

// 监视图表配置变化，自动重新加载数据
const debouncedLoadChartData = debounce(() => {
  console.log("图表配置变化", props.chartId)
  console.log("图表配置变化", chartOptions.value)
  if (props.autoRefresh) {
    loadChartData();
  }
}, 350); // 设置防抖时间为 300 毫秒

watch(chartOptions, debouncedLoadChartData, { deep: true, immediate: true });

// 提供刷新方法供父组件调用
defineExpose({
  refresh: loadChartData
});
</script>

<style scoped>
.base-chart-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

.chart-loading, .chart-error {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 10;
}

.chart-error {
  padding: 0 20px;
}
</style> 