/**
 * 提示词类型枚举
 */
export enum PromptType {
  SYSTEM = 'system',
  USER = 'user',
  ASSISTANT = 'assistant'
}

/**
 * 提示词项目接口
 */
export interface PromptItem {
  id: string;
  name: string;
  type: 'system' | 'user' | 'assistant';
  content: string;
  description?: string;
  tags?: string[];
  createdAt: string;
  updatedAt: string;
}

/**
 * 提示词表单接口
 */
export interface PromptForm {
  name: string;
  type: 'system' | 'user' | 'assistant';
  content: string;
  description?: string;
  tags?: string[];
} 

export interface Scene {
  desc: string;
  name: string;
}

export interface GetPromptTemplateVO {
  input_variables: string[];
  response_format: string;
  response_key: string;
  template: string;
  template_define: string;
  template_format: string;
  template_is_strict: string;
  template_scene: string;
}

export interface PromptListItem {
  chat_scene: string;
  content: string;
  gmt_created: string;
  gmt_modified: string;
  id: number;
  input_variables: string;
  model: string;
  prompt_code: string;
  prompt_desc: string;
  prompt_language: string;
  prompt_name: string;
  prompt_type: string;
  response_schema: string;
  sub_chat_scene: string;
  sys_code: string;
  user_code: string;
  user_name: string;
}

export interface GetPrmoptListVO {
  items: PromptListItem[];
  page: number;
  page_size: number;
  total_count: number;
  total_pages: number;
}

export interface AddPromptDTO {
  chat_scene: string;
  content: string;
  model: string;
  prompt_desc: string;
  prompt_language: string;
  prompt_name: string;
  prompt_type: string;
  sub_chat_scene: string;
  user_name: string;
  input_variables: string;
  response_schema: string;
  prompt_code?: string;
}

export interface AddPromptVO {
  id: number;
  chat_scene: string;
  content: string;
  model: string;
  prompt_desc: string;
  prompt_language: string;
  prompt_name: string;
  prompt_type: string;
  sub_chat_scene: string;
  user_name: string;
  input_variables: string;
  response_schema: string;
  gmt_created: string;
  gmt_modified: string;
  sys_code: string;
  user_code: string;
}

export interface DebugParams {
  chat_scene: string;
  sub_chat_scene: string;
  prompt_code: string;
  prompt_type: string;
  prompt_name: string;
  content: string;
  prompt_desc: string;
  response_schema: string;
  input_variables: string;
  model: string;
  prompt_language: 'en';
  input_values: Record<string, any>;
  temperature: number;
  debug_model: string;
  user_input: string;
}

export interface VerifyPromptDTO {
  llm_out?: string;
  prompt_type?: string;
  chat_scene?: string;
}