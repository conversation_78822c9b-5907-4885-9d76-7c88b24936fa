import { 
  CommentOutlined,
  AppstoreOutlined,
  ExperimentOutlined,
  DatabaseOutlined,
  MessageOutlined,
  ConsoleSqlOutlined,
  SettingOutlined,
} from '@ant-design/icons-vue';
import { useUserStore } from '@/store/user';
import { computed } from 'vue';

export interface MenuItem {
  key: string;
  icon: any;
  label: string;
  path: string;
}

export let menuList: MenuItem[] = [
  {
    key: 'chat',
    icon: CommentOutlined,
    label: '在线对话',
    path: '/chatData',
  },
  {
    key: 'dashboard',
    icon: AppstoreOutlined,
    label: '看板管理',
    path: '/dashboard-list',
  }, 
  {
    key: 'dashboardShare',
    icon: AppstoreOutlined,
    label: '看板分享',
    path: '/dashboard-share',
  }, 
  {
    key: 'model',
    icon: ExperimentOutlined,
    label: '模型管理',
    path: '/model',
  }, 
  {
    key: 'database',
    icon: DatabaseOutlined,
    label: '数据源管理',
    path: '/database',
  }, 
  {
    key: 'prompt',
    icon: MessageOutlined,
    label: '提示词管理',
    path: '/prompt',
  }, 
  {
    key: 'onlineSql',
    icon: ConsoleSqlOutlined,
    label: 'SQL在线执行',
    path: '/onlineSql',
  },
  {
    key: 'system',
    icon: SettingOutlined,
    label: '系统管理',
    path: '/system/user',
  },
];

export const userMenuList = computed(() => {
  const result: MenuItem[] = [];
  menuList.map((item) => {
    if (useUserStore().permissionMap.hasOwnProperty(item.label) && useUserStore().permissionMap[item.label]) {
      if (item.key === 'system' && !useUserStore().permissionMap.hasOwnProperty('用户管理')) {
        result.push({ 
          ...item,
         path: '/system/role'
        });
      } else {
        result.push(item);
      }
    }
  });
  return result;
});

