<template>
  <common-chart-settings
    :options="props.options"
    chart-type="word-cloud"
    @update="updateOptions"
  >
    <!-- 样式配置 -->
    <template #style-settings>
      <!-- 词云设置 -->
      <a-divider orientation="left">词云设置</a-divider>
      
      <a-form-item label="最小字号">
        <a-input-number 
          v-model:value="chartConfig.series[0].textStyle.fontSize[0]" 
          :min="8" 
          :max="60" 
          style="width: 100%"
          @change="updateConfig" 
        />
      </a-form-item>
      
      <a-form-item label="最大字号">
        <a-input-number 
          v-model:value="chartConfig.series[0].textStyle.fontSize[1]" 
          :min="20" 
          :max="120" 
          style="width: 100%"
          @change="updateConfig" 
        />
      </a-form-item>
      
      <a-form-item label="词云形状">
        <a-radio-group v-model:value="cloudShape" @change="updateCloudShape">
          <a-radio-button value="circle">圆形</a-radio-button>
          <a-radio-button value="cardioid">心形</a-radio-button>
          <a-radio-button value="diamond">菱形</a-radio-button>
          <a-radio-button value="triangle">三角形</a-radio-button>
          <a-radio-button value="pentagon">五边形</a-radio-button>
        </a-radio-group>
      </a-form-item>
      
      <a-form-item label="字体旋转">
        <a-switch v-model:checked="rotationEnabled" @change="updateRotation" />
      </a-form-item>
      
      <template v-if="rotationEnabled">
        <a-form-item label="旋转角度范围">
          <a-slider
            range
            v-model:value="rotationRange"
            :min="-90"
            :max="90"
            @change="updateRotationRange"
          />
        </a-form-item>
      </template>
      
      <a-form-item label="颜色方案">
        <a-select v-model:value="colorScheme" style="width: 100%" @change="updateColorScheme">
          <a-select-option value="category">分类色系</a-select-option>
          <a-select-option value="random">随机色系</a-select-option>
          <a-select-option value="gradient">渐变色系</a-select-option>
        </a-select>
      </a-form-item>
      
      <template v-if="colorScheme === 'gradient'">
        <a-form-item label="起始颜色">
          <a-input 
            v-model:value="startColor" 
            type="color"
            style="width: 100px" 
            @change="updateGradientColors" 
          />
        </a-form-item>
        
        <a-form-item label="结束颜色">
          <a-input 
            v-model:value="endColor" 
            type="color"
            style="width: 100px" 
            @change="updateGradientColors" 
          />
        </a-form-item>
      </template>
    </template>
  </common-chart-settings>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import CommonChartSettings from './CommonChartSettings.vue';
import type { ChartOptions } from '@/types/chart';

const props = defineProps({
  options: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update']);

// 形状
const cloudShape = ref('circle');

// 旋转
const rotationEnabled = ref(true);
const rotationRange = ref<[number, number]>([-45, 45]);

// 颜色方案
const colorScheme = ref('random');
const startColor = ref('#c6e48b');
const endColor = ref('#196127');

// 直接访问config部分的计算属性
const chartConfig = computed({
  get: () => {
    // 确保config对象存在
    if (!props.options.config) {
      return getDefaultConfig();
    }
    return props.options.config;
  },
  set: (newConfig) => {
    const updatedOptions = {
      ...props.options,
      config: newConfig
    };
    emit('update', updatedOptions);
  }
});

// 获取默认配置
const getDefaultConfig = () => {
  return {
    type: 'wordCloud',
    title: {
      text: '词云图'
    },
    series: [{
      type: 'wordCloud',
      shape: 'circle',
      sizeRange: [12, 60],
      rotationRange: [-45, 45],
      rotationStep: 15,
      gridSize: 8,
      drawOutOfBound: false,
      textStyle: {
        fontFamily: 'sans-serif',
        fontWeight: 'bold',
        fontSize: [12, 60],
        color: function () {
          return 'rgb(' + [
            Math.round(Math.random() * 160),
            Math.round(Math.random() * 160),
            Math.round(Math.random() * 160)
          ].join(',') + ')';
        }
      },
      emphasis: {
        textStyle: {
          shadowBlur: 10,
          shadowColor: '#333'
        }
      },
      data: [
        { name: '词云', value: 100 },
        { name: '可视化', value: 80 },
        { name: '数据', value: 70 },
        { name: '分析', value: 60 },
        { name: '图表', value: 50 }
      ]
    }]
  };
};

// 更新词云形状
const updateCloudShape = (shape: string) => {
  const config = { ...chartConfig.value };
  config.series[0].shape = shape;
  chartConfig.value = config;
};

// 更新旋转设置
const updateRotation = (enabled: boolean) => {
  rotationEnabled.value = enabled;
  
  const config = { ...chartConfig.value };
  if (enabled) {
    config.series[0].rotationRange = rotationRange.value;
  } else {
    config.series[0].rotationRange = [0, 0];
  }
  
  chartConfig.value = config;
};

// 更新旋转角度范围
const updateRotationRange = (range: [number, number]) => {
  const config = { ...chartConfig.value };
  config.series[0].rotationRange = range;
  chartConfig.value = config;
};

// 更新颜色方案
const updateColorScheme = (scheme: string) => {
  colorScheme.value = scheme;
  
  const config = { ...chartConfig.value };
  switch (scheme) {
    case 'category':
      config.series[0].textStyle.color = function () {
        // 使用ECharts内置的分类颜色
        const colors = [
          '#5470c6', '#91cc75', '#fac858', '#ee6666',
          '#73c0de', '#3ba272', '#fc8452', '#9a60b4'
        ];
        return colors[Math.floor(Math.random() * colors.length)];
      };
      break;
    case 'random':
      config.series[0].textStyle.color = function () {
        return 'rgb(' + [
          Math.round(Math.random() * 160),
          Math.round(Math.random() * 160),
          Math.round(Math.random() * 160)
        ].join(',') + ')';
      };
      break;
    case 'gradient':
      updateGradientColors();
      break;
  }
  
  chartConfig.value = config;
};

// 更新渐变颜色
const updateGradientColors = () => {
  // 注意：这里设置的是函数字符串，真正的函数会在ECharts初始化时生效
  const config = { ...chartConfig.value };
  config.series[0].textStyle.color = `function (_, index) {
    const startColorRGB = hexToRgb('${startColor.value}');
    const endColorRGB = hexToRgb('${endColor.value}');
    const total = ${chartConfig.value.series[0].data.length || 10};
    const ratio = index / total;
    
    return 'rgb(' + [
      Math.round(startColorRGB[0] + (endColorRGB[0] - startColorRGB[0]) * ratio),
      Math.round(startColorRGB[1] + (endColorRGB[1] - startColorRGB[1]) * ratio),
      Math.round(startColorRGB[2] + (endColorRGB[2] - startColorRGB[2]) * ratio)
    ].join(',') + ')';
  }`;
  
  chartConfig.value = config;
};

// 更新配置
const updateOptions = (newOptions: ChartOptions) => {
  emit('update', newOptions);
};

// 更新config
const updateConfig = () => {
  // 触发响应式更新
  chartConfig.value = { ...chartConfig.value };
};
</script> 