const wordCloudDefaultOptions = {
  dataType: 'static',
  data: {
    columns: ['词语', '权重'],
    values: [
      ['技术', 128],
      ['数据', 110],
      ['分析', 95],
      ['智能', 85],
      ['开发', 78],
      ['云计算', 72],
      ['创新', 68],
      ['平台', 65],
      ['服务', 60],
      ['系统', 58],
      ['研究', 55],
      ['大数据', 50],
      ['管理', 48],
      ['产品', 46],
      ['业务', 42]
    ]
  },
  config: {
    type: 'word-cloud',
    title: {
      text: '词云图',
      show: false
    },
    tooltip: {
      show: true
    },
    series: [{
      type: 'wordCloud',
      shape: 'circle',
      left: 'center',
      top: 'center',
      width: '80%',
      height: '80%',
      right: null,
      bottom: null,
      sizeRange: [12, 60],
      rotationRange: [-90, 90],
      rotationStep: 45,
      gridSize: 8,
      drawOutOfBound: false,
      textStyle: {
        fontFamily: 'sans-serif',
        fontWeight: 'bold',
        color: function(params: { dataIndex: number }) {
          const colors = [
            '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
            '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#6e7079',
            '#a05195', '#d45087', '#f95d6a', '#ff7c43', '#ffa600'
          ];
          return colors[params.dataIndex % colors.length];
        }
      },
      data: [
        { name: '技术', value: 128 },
        { name: '数据', value: 110 },
        { name: '分析', value: 95 },
        { name: '智能', value: 85 },
        { name: '开发', value: 78 },
        { name: '云计算', value: 72 },
        { name: '创新', value: 68 },
        { name: '平台', value: 65 },
        { name: '服务', value: 60 },
        { name: '系统', value: 58 },
        { name: '研究', value: 55 },
        { name: '大数据', value: 50 },
        { name: '管理', value: 48 },
        { name: '产品', value: 46 },
        { name: '业务', value: 42 }
      ]
    }]
  },
  dataMapping: {
    wordCloud: {
      wordField: '词语',
      valueField: '权重'
    }
  }
};
export default wordCloudDefaultOptions; 