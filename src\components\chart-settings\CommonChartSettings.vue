<template>
  <div class="chart-settings">
    <a-tabs v-model:activeKey="activeTabKey">
      <!-- 样式配置选项卡 -->
      <!-- <a-tab-pane key="style" tab="样式配置">
        <a-form layout="horizontal" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
          -- 使用折叠面板进行样式配置 --
          <a-collapse v-model:activeKey="activeCollapseKeys">

            -- 标题设置 --
            <a-collapse-panel key="title" header="标题">          
              <a-form-item label="标题">
                <a-input v-model:value="configOptions.title" placeholder="请输入图表标题" />
              </a-form-item>
            </a-collapse-panel>
                        
            -- 图例设置 --
            <a-collapse-panel key="legend" header="图例">
              <a-form-item label="显示图例">
                <a-switch v-model:checked="configOptions.config.legend.show" />
              </a-form-item>
              
              <template v-if="configOptions.config.legend && configOptions.config.legend.show">
                <a-form-item label="图例方向">
                  <a-radio-group v-model:value="configOptions.config.legend.orient">
                    <a-radio-button value="horizontal">水平</a-radio-button>
                    <a-radio-button value="vertical">垂直</a-radio-button>
                  </a-radio-group>
                </a-form-item>
                
                <a-form-item label="图例位置">
                  <a-select v-model:value="configOptions.config.legend.left">
                    <a-select-option value="left">左侧</a-select-option>
                    <a-select-option value="center">居中</a-select-option>
                    <a-select-option value="right">右侧</a-select-option>
                    <a-select-option value="top">顶部</a-select-option>
                    <a-select-option value="bottom">底部</a-select-option>
                  </a-select>
                </a-form-item>
              </template>
            </a-collapse-panel>

            -- 颜色配置 --
            <a-collapse-panel key="color" header="颜色配置">
              <a-form-item label="主题颜色">
                <a-radio-group v-model:value="colorTheme" @change="updateColorTheme">
                  <a-radio-button value="default">默认</a-radio-button>
                  <a-radio-button value="warm">暖色</a-radio-button>
                  <a-radio-button value="cool">冷色</a-radio-button>
                  <a-radio-button value="custom">自定义</a-radio-button>
                </a-radio-group>
              </a-form-item>

              <template v-if="colorTheme === 'custom'">
                <a-form-item 
                  v-for="(color, index) in colors" 
                  :key="index" 
                  :label="`颜色 ${index + 1}`"
                >
                  <a-popover trigger="click" placement="bottom">
                    <template #content>
                      <div class="color-picker-panel">
                        <div class="color-presets">
                          <div
                            v-for="(presetColor, presetIndex) in colorPresets"
                            :key="presetIndex"
                            class="color-preset-item"
                            :style="{ backgroundColor: presetColor }"
                            @click="updateColorItem(index, presetColor)"
                          ></div>
                        </div>
                        <a-input
                          v-model:value="colors[index]"
                          placeholder="#RRGGBB"
                          @change="updateColorItem(index, $event.target.value)"
                          class="color-input"
                          allow-clear
                        >
                          <template #prefix>
                            <div class="color-preview" :style="{ backgroundColor: colors[index] }"></div>
                          </template>
                        </a-input>
                      </div>
                    </template>
                    <a-button class="color-picker-trigger">
                      <template #icon>
                        <bg-colors-outlined />
                      </template>
                      <div class="color-preview" :style="{ backgroundColor: colors[index] }"></div>
                    </a-button>
                  </a-popover>
                </a-form-item>
                
                <a-button type="dashed" block @click="addColor">
                  添加颜色
                </a-button>
              </template>
            </a-collapse-panel>

            -- 背景配置 --
            <a-collapse-panel key="background" header="背景配置">
              <a-form-item label="背景颜色">
                <a-popover trigger="click" placement="bottom">
                  <template #content>
                    <div class="color-picker-panel">
                      <div class="color-presets">
                        <div
                          v-for="(color, index) in colorPresets"
                          :key="index"
                          class="color-preset-item"
                          :style="{ backgroundColor: color }"
                          @click="updateBackgroundColor(color)"
                        ></div>
                      </div>
                      <a-input
                        v-model:value="backgroundColor"
                        placeholder="#RRGGBB"
                        @change="updateBackgroundColor"
                        class="color-input"
                        allow-clear
                      >
                        <template #prefix>
                          <div class="color-preview" :style="{ backgroundColor: backgroundColor }"></div>
                        </template>
                      </a-input>
                    </div>
                  </template>
                  <a-button class="color-picker-trigger">
                    <template #icon>
                      <bg-colors-outlined />
                    </template>
                    <div class="color-preview" :style="{ backgroundColor: backgroundColor }"></div>
                  </a-button>
                </a-popover>
              </a-form-item>
            </a-collapse-panel>

            -- 动画设置 --
            <a-collapse-panel key="animation" header="动画设置">
              <a-form-item label="开启动画">
                <a-switch v-model:checked="animation" @change="updateAnimation" />
              </a-form-item>

              <a-form-item v-if="animation" label="动画时长">
                <a-input-number 
                  v-model:value="animationDuration" 
                  :min="100" 
                  :max="5000"
                  :step="100"
                  @change="updateAnimation" 
                />
              </a-form-item>
            </a-collapse-panel>

            -- 提示框设置 --
            <a-collapse-panel key="tooltip" header="提示框设置">
              <a-form-item label="显示提示框">
                <a-switch v-model:checked="tooltipShow" @change="updateTooltip" />
              </a-form-item>

              <a-form-item v-if="tooltipShow" label="提示框触发">
                <a-select v-model:value="tooltipTrigger" @change="updateTooltip">
                  <a-select-option value="item">数据项</a-select-option>
                  <a-select-option value="axis">坐标轴</a-select-option>
                  <a-select-option value="none">无</a-select-option>
                </a-select>
              </a-form-item>
            </a-collapse-panel>

            -- 图表特定样式设置插槽 --
            <slot name="style-settings"></slot>
          </a-collapse>
        </a-form>
      </a-tab-pane> -->

      <!-- 数据配置选项卡 -->
      <a-tab-pane key="data" tab="数据配置">
        <a-form layout="vertical">
          <!-- 数据源选择 -->
          <a-form-item label="数据源类型" class="data-type-selector">
            <a-radio-group v-model:value="configOptions.dataType" button-style="solid">
              <a-radio-button value="static">静态数据</a-radio-button>
              <a-radio-button value="sql">SQL查询</a-radio-button>
            </a-radio-group>
          </a-form-item>

          <!-- 静态数据配置 -->
          <template v-if="configOptions.dataType === 'static'">
            <!-- 静态数据表格编辑器 -->
            <a-form-item class="json-data-form-item">
              <div class="json-editor-container" ref="jsonEditorContainer" @click="focusTextarea">
                <textarea 
                  v-model="jsonDataText" 
                  class="json-editor-textarea"
                  ref="jsonTextarea"
                  spellcheck="false"
                  autocomplete="off"
                  placeholder='{ "columns": ["类别", "数值"], "values": [ ["柱状", 36], ["饼形", 20] ] }'
                  @input="highlightJsonSyntax"
                  @scroll="syncScroll"
                  @keydown="handleTabKey"
                ></textarea>
                <pre class="json-highlight-layer" ref="jsonHighlightLayer"><code v-html="highlightedJsonText"></code></pre>
                <div class="json-editor-toolbar">
                  <a-tooltip title="格式化">
                    <a-button 
                      type="text" 
                      size="small"
                      @click="formatJsonText"
                    >
                      <template #icon><format-painter-outlined /></template>
                      格式化
                    </a-button>
                  </a-tooltip>
                </div>
                <div class="edit-indicator">
                  <edit-outlined />
                </div>
              </div>
            </a-form-item>
            
            <!-- 添加静态数据操作按钮 -->
            <a-form-item>
              <a-space class="static-data-actions">
                <a-button type="primary" @click="downloadStaticData">
                  <template #icon><download-outlined /></template>
                  下载数据
                </a-button>
                <a-upload
                  :show-upload-list="false"
                  :before-upload="handleUploadStaticData"
                  accept=".json"
                >
                  <a-button type="primary">
                    <template #icon><upload-outlined /></template>
                    上传数据
                  </a-button>
                </a-upload>
              </a-space>
            </a-form-item>
          </template>

          <!-- SQL查询配置 -->
          <template v-if="configOptions.dataType === 'sql'">
            <a-form-item class="sql-form-item">
              <template #label>
                <div class="form-item-label-with-tip">
                  <span>数据集选择</span>
                  <a-tooltip title="选择预设的数据集">
                    <question-circle-outlined />
                  </a-tooltip>
                </div>
              </template>
              <template v-if="!chartDataSets.loading">
                <a-select
                  placeholder="请选择预设数据集（可选）"
                  class="full-width-select"
                  size="large"
                  allowClear
                  v-model:value="chartDataSetValue"
                  @change="handleDataSetSelect"
                  @clear="handleDataSetClear"
                  :options="chatDataSetOptions"
                />
              </template>
              <a-spin v-else />
            </a-form-item>

            <a-form-item class="sql-form-item">
              <template #label>
                <div class="form-item-label-with-tip">
                  <span>数据源选择</span>
                  <a-tooltip title="选择要查询的数据库">
                    <question-circle-outlined />
                  </a-tooltip>
                </div>
              </template>
              <template v-if="!dataSources.loading">
                <a-select
                  v-model:value="configOptions.db_name"
                  placeholder="请选择数据源"
                  class="full-width-select"
                  size="large"
                  :options="dataSources.list.map(ds => ({
                    label: ds.params && ds.params.database ? ds.params.database : '',
                    value: ds.params && ds.params.database ? ds.params.database : ''
                  }))"
                />
              </template>
              <a-spin v-else />
            </a-form-item>

            <a-form-item class="sql-form-item">
              <template #label>
                <div class="form-item-label-with-tip">
                  <span>SQL查询语句</span>
                  <a-tooltip title="输入SQL查询语句获取数据">
                    <question-circle-outlined />
                  </a-tooltip>
                </div>
              </template>
              <div class="sql-editor-container">
                <a-textarea
                  v-model:value="dashboardStore.sqlInput"
                  :auto-size="{ minRows: 8, maxRows: 15 }"
                  placeholder="请输入SQL查询语句，例如: SELECT category, value FROM data_table"
                  class="sql-textarea"
                />
              </div>
            </a-form-item>

            <a-form-item class="sql-actions">
              <a-space>
                <a-button type="primary" size="large" @click="handleSqlQuery" :loading="sqlExecuting">
                  执行查询
                </a-button>
              </a-space>
            </a-form-item>

            <a-alert
              v-if="sqlStatus === 'success'"
              type="success"
              message="查询成功"
              description="数据已成功获取并更新到图表中"
              show-icon
              class="sql-alert"
            />

            <a-alert
              v-if="sqlStatus === 'error'"
              type="error"
              message="查询失败"
              :description="sqlError"
              show-icon
              class="sql-alert"
            />
          </template>

          <!-- 数据映射配置 -->
          <!-- <a-divider orientation="left" class="data-mapping-divider">数据映射配置</a-divider> -->
          
          <!-- <div class="data-mapping-container">
            -- 基础数据映射 --
            <template v-if="['bar', 'line', 'area', 'horizontal-bar'].includes(props.chartType)">
              <a-form-item label="X轴字段" class="mapping-form-item">
                <a-select 
                  v-model:value="configOptions.dataMapping.xField" 
                  placeholder="选择X轴字段"
                  :options="Array.isArray(availableFields) ? availableFields : []"
                  class="mapping-select"
                />
              </a-form-item>
              
              <a-form-item label="Y轴字段" class="mapping-form-item">
                <a-select
                  v-model:value="configOptions.dataMapping.yFields"
                  mode="multiple"
                  placeholder="选择Y轴字段"
                  :options="Array.isArray(availableFields) ? availableFields : []"
                  class="mapping-select"
                />
              </a-form-item>
              
              <a-form-item label="系列名称" class="mapping-form-item" v-if="configOptions.dataMapping.yFields?.length">
                <div class="series-name-container">
                  <div 
                    v-for="(field, index) in configOptions.dataMapping.yFields" 
                    :key="index"
                    class="series-name-item"
                    @click="initSeriesNames"
                  >
                    <span class="series-field-name">{{ field }}</span>
                    <a-input 
                      v-if="configOptions.dataMapping.seriesNames"
                      v-model:value="configOptions.dataMapping.seriesNames[index]" 
                      placeholder="系列名称" 
                      class="series-name-input"
                    />
                    <a-input v-else placeholder="系列名称" class="series-name-input" disabled />
                  </div>
                </div>
              </a-form-item>
            </template>
            
            -- 饼图数据映射 --
            <template v-if="props.chartType === 'pie'">
              <a-form-item label="名称字段">
                <a-select 
                  :value="configOptions.dataMapping.pie?.nameField || ''" 
                  @update:value="handleFieldUpdate($event, 'pie', 'nameField')"
                  placeholder="选择名称字段"
                  :options="Array.isArray(availableFields) ? availableFields : []"
                />
              </a-form-item>
              
              <a-form-item label="数值字段">
                <a-select 
                  :value="configOptions.dataMapping.pie?.valueField || ''" 
                  @update:value="handleFieldUpdate($event, 'pie', 'valueField')"
                  placeholder="选择数值字段"
                  :options="Array.isArray(availableFields) ? availableFields : []"
                />
              </a-form-item>
            </template>
            
            -- 漏斗图数据映射 --
            <template v-if="props.chartType === 'funnel'">
              <a-form-item label="名称字段">
                <a-select 
                  :value="configOptions.dataMapping.funnel?.nameField || ''" 
                  @update:value="handleFieldUpdate($event, 'funnel', 'nameField')"
                  placeholder="选择名称字段"
                  :options="Array.isArray(availableFields) ? availableFields : []"
                />
              </a-form-item>
              
              <a-form-item label="数值字段">
                <a-select 
                  :value="configOptions.dataMapping.funnel?.valueField || ''" 
                  @update:value="handleFieldUpdate($event, 'funnel', 'valueField')"
                  placeholder="选择数值字段"
                  :options="Array.isArray(availableFields) ? availableFields : []"
                />
              </a-form-item>
            </template>
            
            -- 散点图数据映射 --
            <template v-if="props.chartType === 'scatter'">
              <a-form-item label="X轴字段">
                <a-select 
                  :value="configOptions.dataMapping.scatter?.xField || ''" 
                  @update:value="handleFieldUpdate($event, 'scatter', 'xField')"
                  placeholder="选择X轴字段"
                  :options="Array.isArray(availableFields) ? availableFields : []"
                />
              </a-form-item>
              
              <a-form-item label="Y轴字段">
                <a-select 
                  :value="configOptions.dataMapping.scatter?.yField || ''" 
                  @update:value="handleFieldUpdate($event, 'scatter', 'yField')"
                  placeholder="选择Y轴字段"
                  :options="Array.isArray(availableFields) ? availableFields : []"
                />
              </a-form-item>
            </template>
            
            -- 热力图数据映射 --
            <template v-if="props.chartType === 'heatmap'">
              <a-form-item label="X轴字段">
                <a-select 
                  :value="configOptions.dataMapping.heatmap?.xField || ''" 
                  @update:value="handleFieldUpdate($event, 'heatmap', 'xField')"
                  placeholder="选择X轴字段"
                  :options="Array.isArray(availableFields) ? availableFields : []"
                />
              </a-form-item>
              
              <a-form-item label="Y轴字段">
                <a-select 
                  :value="configOptions.dataMapping.heatmap?.yField || ''" 
                  @update:value="handleFieldUpdate($event, 'heatmap', 'yField')"
                  placeholder="选择Y轴字段"
                  :options="Array.isArray(availableFields) ? availableFields : []"
                />
              </a-form-item>
              
              <a-form-item label="热力值字段">
                <a-select 
                  :value="configOptions.dataMapping.heatmap?.valueField || ''" 
                  @update:value="handleFieldUpdate($event, 'heatmap', 'valueField')"
                  placeholder="选择热力值字段"
                  :options="Array.isArray(availableFields) ? availableFields : []"
                />
              </a-form-item>
            </template>
            
            -- 雷达图数据映射 --
            <template v-if="props.chartType === 'radar'">
              <a-form-item label="指标字段">
                <a-select 
                  :value="configOptions.dataMapping.radar?.indicatorField || ''" 
                  @update:value="handleFieldUpdate($event, 'radar', 'indicatorField')"
                  placeholder="选择指标字段"
                  :options="Array.isArray(availableFields) ? availableFields : []"
                />
              </a-form-item>
              
              <a-form-item label="系列字段">
                <a-select
                  :value="configOptions.dataMapping.radar?.seriesFields || ''"
                  @update:value="handleFieldUpdate($event, 'radar', 'seriesFields')"
                  mode="multiple"
                  placeholder="选择系列字段"
                  :options="Array.isArray(availableFields) ? availableFields : []"
                  style="width: 100%"
                />
              </a-form-item>
            </template>
            
            -- 仪表盘数据映射 --
            <template v-if="props.chartType === 'gauge'">
              <a-form-item label="指标名称字段">
                <a-select 
                  :value="configOptions.dataMapping.gauge?.nameField || ''" 
                  @update:value="handleFieldUpdate($event, 'gauge', 'nameField')"
                  placeholder="选择指标名称字段"
                  :options="Array.isArray(availableFields) ? availableFields : []"
                />
              </a-form-item>
              
              <a-form-item label="指标值字段">
                <a-select 
                  :value="configOptions.dataMapping.gauge?.valueField || ''" 
                  @update:value="handleFieldUpdate($event, 'gauge', 'valueField')"
                  placeholder="选择指标值字段"
                  :options="Array.isArray(availableFields) ? availableFields : []"
                />
              </a-form-item>
            </template>
            
            -- 矩形树图数据映射 --
            <template v-if="props.chartType === 'treemap'">
              <a-form-item label="父级字段">
                <a-select 
                  :value="configOptions.dataMapping.treemap?.parentField || ''" 
                  @update:value="handleFieldUpdate($event, 'treemap', 'parentField')"
                  placeholder="选择父级字段"
                  :options="Array.isArray(availableFields) ? availableFields : []"
                />
              </a-form-item>
              
              <a-form-item label="子级字段">
                <a-select 
                  :value="configOptions.dataMapping.treemap?.childField || ''" 
                  @update:value="handleFieldUpdate($event, 'treemap', 'childField')"
                  placeholder="选择子级字段"
                  :options="Array.isArray(availableFields) ? availableFields : []"
                />
              </a-form-item>
              
              <a-form-item label="值字段">
                <a-select 
                  :value="configOptions.dataMapping.treemap?.valueField || ''" 
                  @update:value="handleFieldUpdate($event, 'treemap', 'valueField')"
                  placeholder="选择值字段"
                  :options="Array.isArray(availableFields) ? availableFields : []"
                />
              </a-form-item>
            </template>
            
            -- 词云图数据映射 --
            <template v-if="props.chartType === 'word-cloud'">
              <a-form-item label="词语字段">
                <a-select 
                  :value="configOptions.dataMapping.wordCloud?.wordField || ''" 
                  @update:value="handleFieldUpdate($event, 'wordCloud', 'wordField')"
                  placeholder="选择词语字段"
                  :options="Array.isArray(availableFields) ? availableFields : []"
                />
              </a-form-item>
              
              <a-form-item label="权重字段">
                <a-select 
                  :value="configOptions.dataMapping.wordCloud?.valueField || ''" 
                  @update:value="handleFieldUpdate($event, 'wordCloud', 'valueField')"
                  placeholder="选择权重字段"
                  :options="Array.isArray(availableFields) ? availableFields : []"
                />
              </a-form-item>
            </template>
            
            -- 指标图数据映射 --
            <template v-if="props.chartType === 'metric'">
              <a-form-item label="指标名称字段">
                <a-select 
                  :value="configOptions.dataMapping.metric?.titleField || ''" 
                  @update:value="handleFieldUpdate($event, 'metric', 'titleField')"
                  placeholder="选择指标名称字段"
                  :options="Array.isArray(availableFields) ? availableFields : []"
                />
              </a-form-item>
              
              <a-form-item label="指标值字段">
                <a-select 
                  :value="configOptions.dataMapping.metric?.valueField || ''" 
                  @update:value="handleFieldUpdate($event, 'metric', 'valueField')"
                  placeholder="选择指标值字段"
                  :options="Array.isArray(availableFields) ? availableFields : []"
                />
              </a-form-item>
              
              <a-form-item label="单位字段">
                <a-select 
                  :value="configOptions.dataMapping.metric?.unitField || ''" 
                  @update:value="handleFieldUpdate($event, 'metric', 'unitField')"
                  placeholder="选择单位字段"
                  :options="Array.isArray(availableFields) ? availableFields : []"
                />
              </a-form-item>
            </template>
            
            -- 表格数据映射 --
            <template v-if="props.chartType === 'table'">
              <a-form-item label="表格列">
                <a-select
                  :value="configOptions.dataMapping.table?.columns || ''"
                  @update:value="handleFieldUpdate($event, 'table', 'columns')"
                  mode="multiple"
                  placeholder="选择表格列"
                  :options="Array.isArray(availableFields) ? availableFields : []"
                  style="width: 100%"
                />
              </a-form-item>
            </template>
          </div> -->
        </a-form>
      </a-tab-pane>

      <!-- 配置预览选项卡 -->
      <!-- <a-tab-pane key="preview" tab="配置预览">
        <a-form layout="vertical">
          <a-form-item>
            <div class="json-preview">
              <pre><code v-html="highlightedJsonConfig" class="json"></code></pre>
            </div>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="copyJsonConfig">
              <template #icon><CopyOutlined /></template>
              复制配置
            </a-button>
          </a-form-item>
        </a-form>
      </a-tab-pane> -->
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import { CopyOutlined, QuestionCircleOutlined, BgColorsOutlined, FormatPainterOutlined, EditOutlined, DownloadOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { getDataSources } from '@/api/database';
import { getChartData, getChartDataSetList } from '@/api/chart';
import type { DatabaseItem } from '@/types/database';
import type { ChartOptions, SqlDataResult, DataSet } from '@/types/chart';
import { useDashboardStore } from '@/store/dashboard';
import { cloneDeep } from 'lodash-es';
import { useRoute } from 'vue-router';
import hljs from 'highlight.js';
import 'highlight.js/styles/monokai-sublime.css';

const props = defineProps({
  chartType: {
    type: String,
    required: true
  },
  options: {
    type: Object,
    required: true
  },
  chartId: {
    type: Number,
  }
});

const emit = defineEmits(['update']);

const route = useRoute();
const dashboardStore = useDashboardStore((route.params as any)?.id || '');
console.log('dashboardStore', dashboardStore)

// 添加标签页和折叠面板状态
const activeTabKey = ref("data");
const activeCollapseKeys = ref(["title", "value", "legend", "color"]);

// SQL查询结果
const queryResult = ref<SqlDataResult | null>(null);

// JSON数据文本
const jsonDataText = ref('');

// 高亮后的JSON文本
const highlightedJsonText = ref('');

// 添加需要的ref
const jsonTextarea = ref<HTMLTextAreaElement | null>(null);
const jsonHighlightLayer = ref<HTMLElement | null>(null);
const jsonEditorContainer = ref<HTMLElement | null>(null);

// 颜色主题
const colorTheme = ref('default');
const colors = ref<string[]>([]);

// 颜色预设
const colorPresets = ref<string[]>([
  '#1677FF', '#F5222D', '#FA8C16', '#FADB14', 
  '#52C41A', '#13C2C2', '#2F54EB', '#722ED1',
  '#EB2F96', '#F5222D', '#FA541C', '#FAAD14',
  '#A0D911', '#52C41A', '#13C2C2', '#1677FF'
]);

// 背景颜色
const backgroundColor = ref('#ffffff');

// 动画设置
const animation = ref(true);
const animationDuration = ref(1000);

// 提示框设置
const tooltipShow = ref(true);
const tooltipTrigger = ref('item');

// SQL查询状态
const sqlStatus = ref('');
const sqlError = ref('');
const sqlExecuting = ref(false);

// JSON配置
const jsonConfig = ref('');
const highlightedJsonConfig = ref('');

// 数据源列表
const dataSources = ref<{
  loading: boolean;
  list: DatabaseItem[];
}>({
  loading: false,
  list: []
});

// 可用字段列表，不再使用computed而是ref使其可赋值
const availableFields = ref<{ label: string; value: string }[]>([]);

// 主题颜色预设映射
const themeColors = {
  default: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],
  warm: ['#ff7f50', '#ff6347', '#e9967a', '#f08080', '#cd5c5c', '#dc143c', '#b22222', '#a52a2a', '#8b0000'],
  cool: ['#4682b4', '#5f9ea0', '#6495ed', '#7b68ee', '#6a5acd', '#483d8b', '#9370db', '#8a2be2', '#9400d3']
};

// 直接访问config部分的ref
const configOptions = ref(cloneDeep(props.options as ChartOptions));
const chartDataSetValue = ref<number>();

const chatDataSetOptions = computed(() => {
  const result = [];
  for (let i = 0; i < chartDataSets.value.list.length; i++) {
    result.push({
      label: chartDataSets.value.list[i].name,
      value: i,
    });
  }
  return result;
});

// watch(
//   props.options,
//   (newOptions) => {
//   },
//   { deep: true, immediate: true }
// );

// 创建监听器来处理属性变更
watch(
  configOptions,
  (newOptions) => {
    dashboardStore.sqlInput = newOptions.sql;
    console.log('update-options-from-configOptions-change', newOptions);
    emit('update', newOptions);
  },
  { deep: true, immediate: true }
);

// 确保初始化必要的嵌套属性
const initConfigOptions = () => {
  console.log('initConfigOptions', configOptions.value);
  if (!configOptions.value.config) {
    configOptions.value.config = {
      type: props.chartType,
      legend: { show: true, orient: 'horizontal', left: 'center' },
      tooltip: { show: true, trigger: 'item' },
      valueType: 'number',
      valueUnit: 'none',
      decimalPlaces: 2,
      backgroundColor: '#ffffff',
      animation: true,
      animationDuration: 1000,
      color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']
    };
  } else {
    // 确保所有必要的嵌套对象都存在
    if (!configOptions.value.config.legend) configOptions.value.config.legend = { show: true, orient: 'horizontal', left: 'center' };
    if (!configOptions.value.config.tooltip) configOptions.value.config.tooltip = { show: true, trigger: 'item' };
  }
};

// 初始化配置项
initConfigOptions();

// 初始化可用字段列表，根据数据类型不同执行不同的初始化逻辑
const initAvailableFields = async (isClearDataMapping = true) => {
  try {
    if (configOptions.value.dataType === 'static' && configOptions.value.data?.columns) {
      // 静态数据，直接从data中获取
      availableFields.value = configOptions.value.data.columns.map((col: string) => ({
        label: col,
        value: col
      }));
    } else if (configOptions.value.dataType === 'sql' && configOptions.value.db_name && configOptions.value.sql) {
      // SQL数据，需要调用接口获取
      try {
        const res = await getChartData({
          db_name: configOptions.value.db_name,
          sql: configOptions.value.sql,
          chart_type: props.chartType
        });
        // console.log('getChartData', res)
        if (res.success && res.data?.sql_data?.colunms) {
          availableFields.value = res.data.sql_data.colunms.map((col: string) => ({
            label: col,
            value: col
          }));
          // 清空之前的数据映射
          if (isClearDataMapping) {
            clearDataMapping();
          }
        }
      } catch (error) {
        console.error('获取SQL数据字段出错:', error);
      }
    } else {
      availableFields.value = [];
    }
  } catch (error) {
    console.warn('初始化可用字段列表出错:', error);
    availableFields.value = [];
  }
};

// 根据图表类型清空数据映射
const clearDataMapping = () => {
  // 复制原始对象，避免直接修改
  const newMapping = configOptions.value.dataMapping ? { ...configOptions.value.dataMapping } : {};
  
  // 根据图表类型清空不同的映射
  switch (props.chartType) {
    case 'bar':
    case 'line':
    case 'area':
    case 'horizontal-bar':
      newMapping.xField = '';
      newMapping.yFields = [];
      newMapping.seriesNames = [];
      break;
    case 'pie':
      if (!newMapping.pie) newMapping.pie = {};
      newMapping.pie.nameField = '';
      newMapping.pie.valueField = '';
      break;
    case 'funnel':
      if (!newMapping.funnel) newMapping.funnel = {};
      newMapping.funnel.nameField = '';
      newMapping.funnel.valueField = '';
      break;
    case 'scatter':
      if (!newMapping.scatter) newMapping.scatter = {};
      newMapping.scatter.xField = '';
      newMapping.scatter.yField = '';
      break;
    case 'heatmap':
      if (!newMapping.heatmap) newMapping.heatmap = {};
      newMapping.heatmap.xField = '';
      newMapping.heatmap.yField = '';
      newMapping.heatmap.valueField = '';
      break;
    case 'radar':
      if (!newMapping.radar) newMapping.radar = {};
      newMapping.radar.indicatorField = '';
      newMapping.radar.seriesFields = [];
      break;
    case 'gauge':
      if (!newMapping.gauge) newMapping.gauge = {};
      newMapping.gauge.nameField = '';
      newMapping.gauge.valueField = '';
      break;
    case 'treemap':
      if (!newMapping.treemap) newMapping.treemap = {};
      newMapping.treemap.parentField = '';
      newMapping.treemap.childField = '';
      newMapping.treemap.valueField = '';
      break;
    case 'word-cloud':
      if (!newMapping.wordCloud) newMapping.wordCloud = {};
      newMapping.wordCloud.wordField = '';
      newMapping.wordCloud.valueField = '';
      break;
    case 'metric':
      if (!newMapping.metric) newMapping.metric = {};
      newMapping.metric.titleField = '';
      newMapping.metric.valueField = '';
      newMapping.metric.unitField = '';
      break;
    case 'table':
      if (!newMapping.table) newMapping.table = {};
      newMapping.table.columns = [];
      break;
  }
  
  // 更新数据映射
  configOptions.value = {
    ...configOptions.value,
    dataMapping: newMapping
  };
};

// 加载数据源列表
const loadDataSources = async () => {
  dataSources.value.loading = true;
  try {
    const res = await getDataSources();
    if (res.success) {
      dataSources.value.list = res.data || [];
    } else {
      message.error(res.err_msg || '获取数据源列表失败');
    }
  } catch (error) {
    message.error('获取数据源列表出错');
  } finally {
    dataSources.value.loading = false;
  }
};

// 高亮JSON语法
const highlightJsonSyntax = () => {
  try {
    // 解析JSON以确保格式正确
    JSON.parse(jsonDataText.value);
    console.log(JSON.parse(jsonDataText.value));

    // 使用highlight.js高亮JSON
    const highlighted = hljs.highlight(jsonDataText.value, { language: 'json' }).value;
    highlightedJsonText.value = highlighted;
    
    // 更新configOptions
    const jsonObj = JSON.parse(jsonDataText.value);
    const newConfigOptions = { ...configOptions.value };
    newConfigOptions.data = jsonObj;
    // configOptions.value = newConfigOptions;
  } catch (e) {
    // 保持原始文本，不高亮
    highlightedJsonText.value = jsonDataText.value
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;");
    console.warn('JSON格式错误:', e);
    // const highlighted = hljs.highlight(jsonDataText.value, { language: 'plaintext' }).value;
    // console.log(highlighted);
    // highlightedJsonText.value = highlighted;
  }
};

// 同步滚动
const syncScroll = (e: Event) => {
  if (jsonHighlightLayer.value && e.target) {
    const target = e.target as HTMLElement;
    jsonHighlightLayer.value.scrollTop = target.scrollTop;
    jsonHighlightLayer.value.scrollLeft = target.scrollLeft;
  }
};

// 处理Tab键
const handleTabKey = (e: KeyboardEvent) => {
  if (e.key === 'Tab') {
    e.preventDefault();
    
    // 获取光标位置
    const target = e.target as HTMLTextAreaElement;
    const start = target.selectionStart;
    const end = target.selectionEnd;
    
    // 在光标位置插入两个空格
    const newValue = target.value.substring(0, start) + '  ' + target.value.substring(end);
    target.value = newValue;
    
    // 更新数据并设置新的光标位置
    jsonDataText.value = newValue;
    target.selectionStart = target.selectionEnd = start + 2;
    
    // 高亮更新后的JSON
    highlightJsonSyntax();
  }
};

// 格式化JSON文本
const formatJsonText = () => {
  try {
    const jsonObj = JSON.parse(jsonDataText.value);
    jsonDataText.value = JSON.stringify(jsonObj, null, 2);
    highlightJsonSyntax();
  } catch (e) {
    message.error('JSON格式错误，无法格式化');
    console.warn('JSON格式错误:', e);
  }
};

// 更新背景颜色
const updateBackgroundColor = (color: any) => {
  backgroundColor.value = typeof color === 'string' ? color : backgroundColor.value;
  
  const newConfig = { ...configOptions.value };
  newConfig.config.backgroundColor = backgroundColor.value;
  configOptions.value = newConfig;
};

// 更新动画设置
const updateAnimation = () => {
  const newConfig = { ...configOptions.value };
  newConfig.config.animation = animation.value;
  if (animation.value) {
    newConfig.config.animationDuration = animationDuration.value;
  }
  
  configOptions.value = newConfig;
};

// 更新提示框设置
const updateTooltip = () => {
  const newConfig = { ...configOptions.value };
  if (!newConfig.config.tooltip) {
    newConfig.config.tooltip = {};
  }
  
  newConfig.config.tooltip.show = tooltipShow.value;
  newConfig.config.tooltip.trigger = tooltipTrigger.value;
  
  configOptions.value = newConfig;
};

// 更新颜色主题
const updateColorTheme = (e: any) => {
  const theme = e.target.value;
  colorTheme.value = theme;
  
  if (theme !== 'custom') {
    colors.value = [...themeColors[theme as keyof typeof themeColors]];
    
    const newConfig = { ...configOptions.value };
    newConfig.config.color = colors.value;
    configOptions.value = newConfig;
  }
};

// 更新颜色项
const updateColorItem = (index: number, color: string) => {
  colors.value[index] = color;
  
  const newConfig = { ...configOptions.value };
  newConfig.config.color = [...colors.value];
  configOptions.value = newConfig;
};

// 添加颜色
const addColor = () => {
  colors.value.push('#1677FF');
  
  const newConfig = { ...configOptions.value };
  newConfig.config.color = [...colors.value];
  configOptions.value = newConfig;
};

// 处理标题颜色变化
const handleTitleColorChange = (color: any) => {
  const newColor = typeof color === 'string' ? color : color.target.value;
  
  const newConfig = { ...configOptions.value };
  if (!newConfig.config.title) {
    newConfig.config.title = {
      text: '',
      show: true,
      align: 'center',
      color: newColor,
      fontWeight: 'bold',
      backgroundColor: 'none'
    };
  } else {
    newConfig.config.title.color = newColor;
  }
  
  configOptions.value = newConfig;
};

// 校验SQL数据是否支持当前图表类型
function validateSqlDataForChart(chartType: string, columns: string[], values: any[]): string {
  if (!Array.isArray(columns) || columns.length === 0) {
    return 'SQL结果无字段，无法配置图表';
  }
  if (!Array.isArray(values) || values.length === 0) {
    return 'SQL结果无数据，无法配置图表';
  }

  // 检查每行数据的列数是否与columns匹配
  const invalidRow = values.find(row => !Array.isArray(row) || row.length !== columns.length);
  if (invalidRow) {
    return '数据格式错误：某些行的列数与字段数不匹配';
  }

  if (['bar', 'line', 'area', 'horizontal-bar'].includes(chartType)) {
    if (columns.length < 2) return '该图表类型至少需要2个字段（如X轴和Y轴）';
    // 检查Y轴数据是否为数值类型
    const hasInvalidNumber = values.some(row => {
      return row.slice(1).some((val: any) => typeof Number(val) !== 'number' && val !== null);
    });
    if (hasInvalidNumber) return 'Y轴字段必须为数值类型';
  }

  if (['pie', 'funnel'].includes(chartType)) {
    if (columns.length < 2) return '该图表类型至少需要2个字段（如名称和数值）';
    // 检查数值字段是否为数值类型
    const hasInvalidNumber = values.some(row => typeof Number(row[1]) !== 'number' && row[1] !== null);
    if (hasInvalidNumber) return '数值字段必须为数值类型';
  }

  if (chartType === 'scatter') {
    if (columns.length < 2) return '散点图至少需要2个字段（X、Y）';
    // 检查X、Y轴数据是否为数值类型
    console.log('columns', columns)
    console.log('configOptions', configOptions.value)
    let hasInvalidNumber = false;
    let xIndex = columns.indexOf(configOptions.value?.dataMapping?.scatter?.xField || '');
    let yIndex = columns.indexOf(configOptions.value?.dataMapping?.scatter?.yField || '');
    if (xIndex > -1 && yIndex > -1) {
      hasInvalidNumber = false;
      values.map((item) => {
        if (typeof Number(item[xIndex]) !== 'number' && item[xIndex] !== null ||
          (typeof Number(item[yIndex]) !== 'number' && item[yIndex] !== null)) {
            hasInvalidNumber = true;
        }
      })
    }
    if (hasInvalidNumber) return 'X轴和Y轴字段必须为数值类型';
  }

  if (chartType === 'heatmap') {
    if (columns.length < 3) return '热力图至少需要3个字段（X、Y、值）';
    // 检查值字段是否为数值类型
    const hasInvalidNumber = values.some(row => typeof row[2] !== 'number' && row[2] !== null);
    if (hasInvalidNumber) return '热力值字段必须为数值类型';
  }

  if (chartType === 'radar') {
    if (columns.length < 2) return '雷达图至少需要2个字段（指标、系列）';
    // 检查系列值是否为数值类型
    const hasInvalidNumber = values.some(row => {
      return row.slice(1).some((val: any) => typeof val !== 'number' && val !== null);
    });
    if (hasInvalidNumber) return '系列值必须为数值类型';
  }

  if (chartType === 'gauge') {
    if (columns.length < 2) return '仪表盘至少需要2个字段（名称、值）';
    // 检查值是否为数值类型且在0-100之间
    const hasInvalidNumber = values.some(row => {
      const val = row[1];
      // return typeof val !== 'number' || val < 0 || val > 100;
      return typeof val !== 'number';
    });
    if (hasInvalidNumber) return '仪表盘数值必须为0-100之间的数值';
  }

  if (chartType === 'treemap') {
    if (columns.length < 3) return '矩形树图至少需要3个字段（父级、子级、值）';
    // 检查值字段是否为数值类型
    const hasInvalidNumber = values.some(row => typeof row[2] !== 'number' && row[2] !== null);
    if (hasInvalidNumber) return '值字段必须为数值类型';
  }

  if (chartType === 'word-cloud') {
    if (columns.length < 2) return '词云图至少需要2个字段（词语、权重）';
    // 检查权重是否为数值类型
    const hasInvalidNumber = values.some(row => typeof row[1] !== 'number' && row[1] !== null);
    if (hasInvalidNumber) return '权重字段必须为数值类型';
  }

  if (chartType === 'metric') {
    if (columns.length < 2) return '指标卡至少需要2个字段（名称、值）';
    // 检查值是否为数值类型
    const hasInvalidNumber = values.some(row => typeof row[1] !== 'number' && row[1] !== null);
    if (hasInvalidNumber) return '指标值必须为数值类型';
  }

  if (chartType === 'table') {
    if (columns.length < 1) return '表格至少需要1个字段';
  }

  return '';
}

// 根据chartType和columns生成默认dataMapping
function getDefaultDataMapping(chartType: string, columns: string[], values: any[][] = []): any {
  const mapping: any = {};

  // 辅助函数：检查列是否为数值类型
  const isNumericColumn = (colIndex: number): boolean => {
    if (values.length === 0) return false;
    return values.every(row => typeof row[colIndex] === 'number' || row[colIndex] === null);
  };

  // 辅助函数：获取数值列索引
  const getNumericColumns = (): number[] => {
    return columns
      .map((_, index) => ({ index, isNumeric: isNumericColumn(index) }))
      .filter(col => col.isNumeric)
      .map(col => col.index);
  };

  // 辅助函数：获取非数值列索引
  const getNonNumericColumns = (): number[] => {
    return columns
      .map((_, index) => ({ index, isNumeric: isNumericColumn(index) }))
      .filter(col => !col.isNumeric)
      .map(col => col.index);
  };

  if (['bar', 'line', 'area', 'horizontal-bar'].includes(chartType)) {
    // 优先使用第一个非数值列作为X轴
    const nonNumericCols = getNonNumericColumns();
    const numericCols = getNumericColumns();
    
    if (nonNumericCols.length > 0 && numericCols.length > 0) {
      mapping.xField = columns[nonNumericCols[0]];
      mapping.yFields = numericCols.map(index => columns[index]);
      mapping.seriesNames = numericCols.map((_, i) => `系列${i + 1}`);
    } else if (numericCols.length >= 2) {
      // 如果没有非数值列，使用第一个数值列作为X轴
      mapping.xField = columns[numericCols[0]];
      mapping.yFields = numericCols.slice(1).map(index => columns[index]);
      mapping.seriesNames = numericCols.slice(1).map((_, i) => `系列${i + 1}`);
    } else {
      // 默认值
      mapping.xField = columns[0];
      mapping.yFields = [columns[1]];
      mapping.seriesNames = ['系列1'];
    }
  } else if (chartType === 'pie' || chartType === 'funnel') {
    const nonNumericCols = getNonNumericColumns();
    const numericCols = getNumericColumns();
    
    if (nonNumericCols.length > 0 && numericCols.length > 0) {
      mapping[chartType] = {
        nameField: columns[nonNumericCols[0]],
        valueField: columns[numericCols[0]]
      };
    } else {
      mapping[chartType] = {
        nameField: columns[0],
        valueField: columns[1]
      };
    }
  } else if (chartType === 'scatter') {
    const numericCols = getNumericColumns();
    
    if (numericCols.length >= 2) {
      mapping.scatter = {
        xField: columns[numericCols[0]],
        yField: columns[numericCols[1]]
      };
    } else {
      mapping.scatter = {
        xField: columns[0],
        yField: columns[1]
      };
    }
  } else if (chartType === 'heatmap') {
    const nonNumericCols = getNonNumericColumns();
    const numericCols = getNumericColumns();
    
    if (nonNumericCols.length >= 2 && numericCols.length > 0) {
      mapping.heatmap = {
        xField: columns[nonNumericCols[0]],
        yField: columns[nonNumericCols[1]],
        valueField: columns[numericCols[0]]
      };
    } else if (numericCols.length >= 3) {
      mapping.heatmap = {
        xField: columns[numericCols[0]],
        yField: columns[numericCols[1]],
        valueField: columns[numericCols[2]]
      };
    } else {
      mapping.heatmap = {
        xField: columns[0],
        yField: columns[1],
        valueField: columns[2]
      };
    }
  } else if (chartType === 'radar') {
    const nonNumericCols = getNonNumericColumns();
    const numericCols = getNumericColumns();
    
    if (nonNumericCols.length > 0 && numericCols.length > 0) {
      mapping.radar = {
        indicatorField: columns[nonNumericCols[0]],
        seriesFields: numericCols.map(index => columns[index])
      };
    } else {
      mapping.radar = {
        indicatorField: columns[0],
        seriesFields: [columns[1]]
      };
    }
  } else if (chartType === 'gauge') {
    const nonNumericCols = getNonNumericColumns();
    const numericCols = getNumericColumns();
    
    if (nonNumericCols.length > 0 && numericCols.length > 0) {
      mapping.gauge = {
        nameField: columns[nonNumericCols[0]],
        valueField: columns[numericCols[0]]
      };
    } else {
      mapping.gauge = {
        nameField: columns[0],
        valueField: columns[1]
      };
    }
  } else if (chartType === 'treemap') {
    const nonNumericCols = getNonNumericColumns();
    const numericCols = getNumericColumns();
    
    if (nonNumericCols.length >= 2 && numericCols.length > 0) {
      mapping.treemap = {
        parentField: columns[nonNumericCols[0]],
        childField: columns[nonNumericCols[1]],
        valueField: columns[numericCols[0]]
      };
    } else {
      mapping.treemap = {
        parentField: columns[0],
        childField: columns[1],
        valueField: columns[2]
      };
    }
  } else if (chartType === 'word-cloud') {
    const nonNumericCols = getNonNumericColumns();
    const numericCols = getNumericColumns();
    
    if (nonNumericCols.length > 0 && numericCols.length > 0) {
      mapping.wordCloud = {
        wordField: columns[nonNumericCols[0]],
        valueField: columns[numericCols[0]]
      };
    } else {
      mapping.wordCloud = {
        wordField: columns[0],
        valueField: columns[1]
      };
    }
  } else if (chartType === 'metric') {
    const nonNumericCols = getNonNumericColumns();
    const numericCols = getNumericColumns();
    
    if (nonNumericCols.length > 0 && numericCols.length > 0) {
      mapping.metric = {
        titleField: columns[nonNumericCols[0]],
        valueField: columns[numericCols[0]],
        unitField: nonNumericCols.length > 1 ? columns[nonNumericCols[1]] : ''
      };
    } else {
      mapping.metric = {
        titleField: columns[0],
        valueField: columns[1],
        unitField: columns[2] || ''
      };
    }
  } else if (chartType === 'table') {
    mapping.table = { columns: columns };
  }

  return mapping;
}

const handleSqlQuery = () => {
  executeSqlQuery();
}

// 修改executeSqlQuery方法
const executeSqlQuery = async () => {
  if (!configOptions.value.db_name || !dashboardStore.sqlInput) {
    message.warning('请选择数据源并输入SQL查询语句');
    return;
  }
  sqlExecuting.value = true;
  sqlStatus.value = '';
  sqlError.value = '';
  
  try {
    configOptions.value.sql = dashboardStore.sqlInput;
    const res = await getChartData({
      db_name: configOptions.value.db_name,
      sql: dashboardStore.sqlInput,
      chart_type: props.chartType
    });
    
    if (res.success) {
      const columns = res.data?.sql_data?.colunms || [];
      const values = res.data?.sql_data?.values || [];
      // 校验数据结构
      console.log("接口数据",props.chartType, columns, values);
      const errMsg = validateSqlDataForChart(props.chartType, columns, values);
      if (errMsg) {
        sqlStatus.value = 'error';
        sqlError.value = errMsg;
        return;
      }
      sqlStatus.value = 'success';
      queryResult.value = res.data;
      // 更新本地选项
      // configOptions.value = {
      //   ...configOptions.value,
      //   dataType: 'sql',
      //   dataMapping: getDefaultDataMapping(props.chartType, columns, values)
      // };
      // 更新可用字段列表
      if (columns.length) {
        availableFields.value = columns.map((col) => ({ label: col, value: col }));
        // 清空之前的数据映射（已在dataMapping赋值时覆盖）
      }
    } else {
      sqlStatus.value = 'error';
      sqlError.value = res.err_msg || '查询失败';
      queryResult.value = null;
    }
  } catch (error) {
    sqlStatus.value = 'error';
    sqlError.value = (error as Error).message || '查询出错';
    queryResult.value = null;
  } finally {
    sqlExecuting.value = false;
  }
};

// 更新JSON显示
const updateJson = () => {
  try {
    const jsonObj = JSON.stringify(configOptions.value, null, 2);
    jsonConfig.value = jsonObj;
    
    // 使用highlight.js高亮JSON
    const highlighted = hljs.highlight(jsonObj, { language: 'json' }).value;
    highlightedJsonConfig.value = highlighted;
  } catch (e) {
    console.warn('JSON格式化出错:', e);
  }
};

// 复制JSON配置
const copyJsonConfig = () => {
  try {
    navigator.clipboard.writeText(jsonConfig.value).then(
      () => {
        message.success('复制成功');
      },
      () => {
        message.error('复制失败');
      }
    );
  } catch (e) {
    message.error('复制失败');
  }
};

// 处理JSON文本框点击，聚焦文本区域
const focusTextarea = () => {
  if (jsonTextarea.value) {
    jsonTextarea.value.focus();
  }
};

// 初始化chartData配置
const initChartData = (loadStaticData = false) => {
  try {
    // 从props.options获取数据配置
    if (props.options) {
      // 设置JSON文本
      console.log('initChartData-options', props.options);
      if ((props.options.dataType === 'static' || loadStaticData) && props.options.data?.columns && props.options.data?.values) {
        const jsonObj = { columns: props.options.data.columns, values: props.options.data.values };
        console.log('jsonObj', jsonObj);
        jsonDataText.value = JSON.stringify(jsonObj, null, 2);
        // 高亮JSON
        highlightJsonSyntax();
      } else if ((props.options.dataType === 'static' || loadStaticData) && (typeof props.options.data === 'string' || typeof props.options.data === 'number')) {
        // 如果data是字符串，直接使用
        jsonDataText.value = props.options.data + '';
        highlightJsonSyntax();
      }
    }
  } catch (err) {
    console.error('初始化配置出错:', err);
  }
};

// 下载静态数据配置
const downloadStaticData = () => {
  let jsonDataStr = jsonDataText.value;
  try {
    const jsonData = JSON.parse(jsonDataText.value);
    if (!jsonData.columns || !jsonData.values) {
      message.error('数据格式不正确，无法下载');
      return;
    }
    jsonDataStr = JSON.stringify(jsonData, null, 2)
  } catch (e) {
    console.error('下载失败：' + (e instanceof Error ? e.message : String(e)));
  }
  const dataStr = 'data:text/json;charset=utf-8,' + encodeURIComponent(jsonDataStr);
  const downloadAnchorNode = window.document.createElement('a');
  downloadAnchorNode.setAttribute('href', dataStr);
  downloadAnchorNode.setAttribute('download', `chart-data-${new Date().getTime()}.json`);
  window.document.body.appendChild(downloadAnchorNode);
  downloadAnchorNode.click();
  downloadAnchorNode.remove();
  message.success('下载成功');
};

// 处理上传静态数据配置
const handleUploadStaticData = (file: File) => {
  const reader = new window.FileReader();
  reader.onload = (e: ProgressEvent<FileReader>) => {
    try {
      const content = e.target?.result as string;
      const jsonData = JSON.parse(content);
      
      // 检查数据格式有效性
      if (!jsonData.columns || !Array.isArray(jsonData.columns) || 
          !jsonData.values || !Array.isArray(jsonData.values)) {
        message.error('无效的数据格式：缺少columns或values字段');
        return;
      }
      
      // 校验数据结构和类型
      const errMsg = validateSqlDataForChart(props.chartType, jsonData.columns, jsonData.values);
      if (errMsg) {
        message.error('数据验证失败：' + errMsg);
        return;
      }
      
      // 更新编辑器JSON文本
      jsonDataText.value = JSON.stringify(jsonData, null, 2);
      highlightJsonSyntax();
      
      // 更新configOptions中的静态数据
      const newConfigOptions = { ...configOptions.value };
      newConfigOptions.dataType = 'static';
      newConfigOptions.data = jsonData;
      
      // 重新初始化DataMapping
      newConfigOptions.dataMapping = getDefaultDataMapping(props.chartType, jsonData.columns, jsonData.values);
      
      // 更新配置
      configOptions.value = newConfigOptions;
      
      // 更新可用字段列表
      availableFields.value = jsonData.columns.map((col: string) => ({
        label: col,
        value: col
      }));
      
      message.success('数据导入成功');
    } catch (err) {
      // message.error('解析数据文件失败：' + (err instanceof Error ? err.message : String(err)));
      message.error('解析数据文件失败');
    }
  };
  reader.readAsText(file);
  return false; // 阻止默认上传行为
};

// 添加数据集列表和加载状态
const chartDataSets = ref<{
  loading: boolean;
  list: DataSet[];
}>({
  loading: false,
  list: []
});

// 加载数据集列表
const loadChartDataSets = async () => {
  if (!props.chartType) return;
  
  chartDataSets.value.loading = true;
  try {
    const res = await getChartDataSetList(props.chartType);
    if (res.success) {
      chartDataSets.value.list = res.data || [];
    } else {
      message.error(res.err_msg || '获取数据集列表失败');
    }
  } catch (error) {
    console.error('获取数据集列表出错:', error);
    message.error('获取数据集列表出错');
  } finally {
    chartDataSets.value.loading = false;
  }
};

// 处理数据集选择
const handleDataSetSelect = (dataSetIndex: number) => {
  const selectedDataSet = chartDataSets.value.list[dataSetIndex];
  // const selectedDataSet = chartDataSets.value.list.find(ds => ds.name === dataSetName);
  if (!selectedDataSet) return;
  console.log('selectedDataSet', selectedDataSet);
  
  // 更新SQL表单
  // configOptions.value = {
  //   ...configOptions.value,
  //   db_name: selectedDataSet.db_name || '',
  //   sql: selectedDataSet.sql || '',
  //   datasourceId: selectedDataSet.id,
  //   datasourceName: selectedDataSet.name,
  //   dataMapping: selectedDataSet.dataMapping || {},
  //   // data: {
  //   //   columns: configOptions.value.data?.columns || [],
  //   //   values: [],
  //   // }
  // };
  console.log('configOptions', configOptions.value);
  dashboardStore.sqlInput = selectedDataSet.sql || '';
  configOptions.value.datasourceId = selectedDataSet.id;
  configOptions.value.datasourceName = selectedDataSet.name;
  configOptions.value.dataMapping = selectedDataSet.dataMapping;
  configOptions.value.db_name = selectedDataSet.db_name || '';
  // configOptions.value =  {
  //   ...configOptions.value,
  //   db_name: selectedDataSet.db_name || '',
  //   sql: selectedDataSet.sql || '',
  //   datasourceId: selectedDataSet.id,
  //   datasourceName: selectedDataSet.name,
  //   dataMapping: selectedDataSet.dataMapping || {},
  // }
  console.log('update-options-from-handleDataSetSelect', configOptions.value);
  emit('update', {
    ...configOptions.value,
    db_name: selectedDataSet.db_name || '',
    sql: selectedDataSet.sql || '',
    datasourceId: selectedDataSet.id,
    datasourceName: selectedDataSet.name,
    dataMapping: selectedDataSet.dataMapping || {},
    // data: {
    //   columns: configOptions.value.data?.columns || [],
    //   values: [],
    // }
  });
  // 执行查询以获取字段
  executeSqlQuery();
};

const handleDataSetClear = () => {
  if (configOptions.value.hasOwnProperty('datasourceId')) {
    delete configOptions.value.datasourceId;
  }
  if (configOptions.value.hasOwnProperty('datasourceName')) {
    delete configOptions.value.datasourceName;
  }
}

// 初始化
onMounted(() => {
  // 加载数据源列表
  loadDataSources();
  
  // 加载数据集列表
  loadChartDataSets();
  
  // 初始化config
  initChartData();
  
  // 初始化可用字段列表
  initAvailableFields(false);
  
  // 初始化seriesNames
  initSeriesNames();
  
  try {
    // 从config更新控制变量
    if (configOptions.value) {
      // 更新背景颜色
      backgroundColor.value = configOptions.value.config.backgroundColor || '#ffffff';
      
      // 更新动画设置
      animation.value = configOptions.value.config.animation !== false;
      animationDuration.value = configOptions.value.config.animationDuration || 1000;
      
      // 更新提示框设置
      if (configOptions.value.config.tooltip) {
        tooltipShow.value = configOptions.value.config.tooltip.show !== false;
        tooltipTrigger.value = configOptions.value.config.tooltip.trigger || 'item';
      }
      
      // 更新颜色设置
      if (configOptions.value.config.color && configOptions.value.config.color.length) {
        colors.value = [...configOptions.value.config.color];
        colorTheme.value = 'custom';
      } else {
        colors.value = [...themeColors.default];
      }
    }
  } catch (err) {
    console.error('配置初始化出错:', err);
  }
  
  // 更新JSON配置
  updateJson();
});

// 监听数据类型变化，重新初始化可用字段列表
watch(() => configOptions.value.dataType, async (newVal) => {
  await initAvailableFields(false);
  if (newVal === 'static') {
    initChartData(true);
  } else {
    // executeSqlQuery();
    console.log('configOptions-change-from-dataType', configOptions.value);
    emit('update', configOptions.value);
  }
}, { immediate: false });

// 监听SQL变化，如果是SQL类型，重新初始化可用字段列表
watch([() => configOptions.value.sql, () => configOptions.value.db_name], async ([newSql, newDbName]) => {
  // 只有在SQL模式且有实际变化时才重新初始化
  if (configOptions.value.dataType === 'sql' && newSql && newDbName) {
    await initAvailableFields(false);
  }
}, { immediate: false });

const handleJsonDataText = () => {
  if (configOptions.value.dataType === 'static') {
    try {
      const jsonData = JSON.parse(jsonDataText.value);

      // 检查数据格式有效性
      if (jsonData.columns && Array.isArray(jsonData.columns) && 
          jsonData.values && Array.isArray(jsonData.values)) {
        
        // 校验数据结构和类型
        const errMsg = validateSqlDataForChart(props.chartType, jsonData.columns, jsonData.values);
        if (!errMsg) {
          // 更新configOptions中的静态数据
          const newConfigOptions = { ...configOptions.value };
          newConfigOptions.data = jsonData;
          
          // 更新可用字段列表
          availableFields.value = jsonData.columns.map((col: string) => ({
            label: col,
            value: col
          }));
          
          // 更新配置
          configOptions.value = newConfigOptions;
        }
      } else {
        const newConfigOptions = { ...configOptions.value };
        newConfigOptions.data = jsonData;
        configOptions.value = newConfigOptions;
      }
    } catch (err) {
      // JSON解析出错时不进行处理，等待用户完成编辑
      // if (configOptions.value?.data) {
      configOptions.value.data = jsonDataText.value as any;
      // }
      console.log('configOptions', configOptions.value)
      console.debug('JSON数据解析中，等待用户完成编辑');
    }
    dashboardStore.chartsKey[props.chartId || ''] ++;
  }
}

// 监听静态数据变化，更新可用字段列表和数据映射
watch(() => jsonDataText.value, () => {
  handleJsonDataText();
}, { immediate: false });

// 监听配置变化，更新JSON显示
watch(() => configOptions.value, () => {
  updateJson();
}, { deep: true });

// 初始化seriesNames数组
const initSeriesNames = () => {
  // 确保seriesNames数组存在
  if (!configOptions.value.dataMapping.seriesNames) {
    configOptions.value.dataMapping.seriesNames = [];
  }
  
  // 确保seriesNames数组长度与yFields一致
  if (configOptions.value.dataMapping.yFields) {
    while (configOptions.value.dataMapping.seriesNames.length < configOptions.value.dataMapping.yFields.length) {
      configOptions.value.dataMapping.seriesNames.push(`系列${configOptions.value.dataMapping.seriesNames.length + 1}`);
    }
  }
};

// 监听yFields变化，初始化seriesNames
watch(() => configOptions.value.dataMapping.yFields, () => {
  initSeriesNames();
}, { deep: true });

// 处理字段更新的函数
const handleFieldUpdate = (
  value: string | string[] | null,
  objectKey: string, 
  propertyKey: string
) => {
  // 确保对象存在
  if (!configOptions.value.dataMapping[objectKey]) {
    configOptions.value.dataMapping[objectKey] = {};
  }
  
  // 赋值
  configOptions.value.dataMapping[objectKey][propertyKey] = value;
};

watch(() => [configOptions.value, chartDataSets.value.list], ([newOptions, newList]) => {
 console.log('options-chartDataSets', props.options);
 console.log('configOptions-chartDataSets', configOptions.value);
 chartDataSetValue.value = undefined;
 if (newOptions && newOptions.hasOwnProperty('datasourceId')) {
  (newList as any).forEach((ds: DataSet, dsIndex: number) => {
    if (ds.id === (newOptions as ChartOptions).datasourceId) {
      chartDataSetValue.value = dsIndex;
    }
  });
  console.log('chartDataSetValue', chartDataSetValue.value);
  console.log('chatDataSetOptions', chatDataSetOptions.value);
 }
}, { 
  immediate: true,
  deep: true
});

// watch (()=> dashboardStore.updateSql, (newVal) => {
//   console.log('dashboardStore.updateSql', newVal);
//   // executeSqlQuery();
//   if (configOptions.value.sql !== dashboardStore.sqlInput) {
//     configOptions.value.sql = dashboardStore.sqlInput;
//   }
// }, { immediate: false });
</script>

<style scoped>
.chart-settings {
  padding: 10px;
}

.font-size-color {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-picker-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-picker {
  width: 40px;
  height: 30px;
  border: 1px solid #d9d9d9;
  margin-left: 10px;
}

.decrease-btn, .increase-btn {
  min-width: 32px;
  padding: 0 8px;
}

.json-preview {
  position: relative;
  width: 100%;
  max-height: 400px;
  overflow: auto;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #272822;
  padding: 10px;
}

.json-preview pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

:deep(.hljs) {
  background-color: transparent;
  padding: 0;
}

:deep(.hljs-attr) {
  color: #f92672; /* 属性名红色 */
}

:deep(.hljs-string) {
  color: #a6e22e; /* 字符串绿色 */
}

:deep(.hljs-number) {
  color: #ae81ff; /* 数字紫色 */
}

:deep(.hljs-literal) {
  color: #66d9ef; /* 布尔值和null蓝色 */
}

:deep(.hljs-punctuation) {
  color: #f8f8f2; /* 括号、冒号等白色 */
}

/* 增强编辑器样式 */
.json-editor-container {
  position: relative;
  border: 1px solid #444;
  border-radius: 4px;
  overflow: hidden;
  height: 350px;
  background-color: #272822;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
  cursor: text;
  transition: border-color 0.3s;
}

.json-editor-container:focus-within {
  border-color: #a6e22e;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3), 0 0 0 2px rgba(166, 226, 46, 0.1);
}

.json-editor-textarea, 
.json-highlight-layer {  
  position: absolute;  
  top: 0;  
  left: 0;  
  width: 100%;  
  height: 100%;  
  margin: 0;  
  padding: 12px;  
  font-family: Consolas, 'Courier New', monospace;
  font-size: 14px;  
  line-height: 20px;  
  letter-spacing: 0;  
  word-spacing: 0;  
  overflow: auto;  
  white-space: pre;  
  tab-size: 2;  
  -moz-tab-size: 2;  
  -o-tab-size: 2;  
  box-sizing: border-box;  
  word-break: normal;  
  word-wrap: normal;
  font-variant-ligatures: none;
}

.json-highlight-layer pre,
.json-highlight-layer code {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  background: transparent;
  margin: 0;
  padding: 0;
  white-space: inherit;
}

.json-editor-textarea {
  z-index: 1;
  border: none;
  resize: none;
  background-color: transparent;
  color: rgba(0, 0, 0, 0);
  caret-color: white;
  outline: none;
}

.json-highlight-layer {
  z-index: 0;
  background-color: #272822;
  color: #f8f8f2;
  pointer-events: none;
}

.json-editor-textarea:focus {
  color: rgba(0, 0, 0, 0);
  outline: none;
  box-shadow: none;
}

.json-editor-textarea::selection {
  background-color: rgba(73, 72, 62, 0.99);
  color: rgba(255, 255, 255, 0.9);
}

/* 确保滚动条样式一致 */
.json-editor-textarea::-webkit-scrollbar,
.json-highlight-layer::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.json-editor-textarea::-webkit-scrollbar-thumb,
.json-highlight-layer::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.json-editor-textarea::-webkit-scrollbar-track,
.json-highlight-layer::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.json-editor-toolbar {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 2;
  background-color: rgba(39, 40, 34, 0.7);
  padding: 4px;
  border-radius: 4px;
  backdrop-filter: blur(3px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.json-editor-toolbar .ant-btn {
  color: #f8f8f2;
  padding: 0 8px;
  height: 24px;
  line-height: 24px;
  font-size: 12px;
}

.json-editor-toolbar .ant-btn:hover {
  color: #a6e22e;
  background-color: rgba(255, 255, 255, 0.1);
}

/* 编辑指示器 */
.edit-indicator {
  position: absolute;
  left: 8px;
  bottom: 8px;
  color: rgba(255, 255, 255, 0.3);
  z-index: 2;
  font-size: 16px;
  pointer-events: none;
  transition: opacity 0.3s;
}

.json-editor-container:focus-within .edit-indicator {
  opacity: 0;
}

/* 处理中文字符宽度 */
:deep(.chinese-char) {
  font-family: Consolas, 'Courier New', monospace;
}

/* 表单布局额外优化 */
.form-item-label-with-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 12px;
}

.data-format-alert {
  border-left: 4px solid #1890ff;
  background-color: #e6f7ff;
}

.full-width-select {
  width: 100%;
}

/* SQL编辑器优化 */
.sql-form-item {
  margin-bottom: 24px;
}

.sql-editor-container {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  overflow: hidden;
  background-color: #fff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
}

.sql-textarea {
  width: 100%;
  font-family: 'Monaco', 'Menlo', 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.6;
  padding: 16px;
  border: none;
  background-color: #fafafa;
}

.sql-actions {
  margin-bottom: 24px;
}

.sql-alert {
  margin-bottom: 32px;
  border-left: 4px solid;
}

/* 数据映射部分优化 */
.data-mapping-divider {
  margin: 32px 0 24px;
  color: #1890ff;
  font-weight: 500;
  font-size: 16px;
}

.data-mapping-container {
  background-color: #f9f9f9;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 24px;
}

.mapping-form-item {
  margin-bottom: 20px;
}

.mapping-select {
  width: 100%;
}

.series-name-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.series-name-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 8px 12px;
}

.series-field-name {
  min-width: 120px;
  color: #555;
  font-weight: 500;
  padding-right: 12px;
  border-right: 1px solid #f0f0f0;
  margin-right: 12px;
}

/* 颜色选择器相关样式 */
.color-picker-panel {
  width: 240px;
  padding: 12px;
}

.color-presets {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 8px;
  margin-bottom: 12px;
}

.color-preset-item {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s;
  border: 1px solid #f0f0f0;
}

.color-preset-item:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.color-input {
  margin-top: 8px;
}

.color-preview {
  width: 16px;
  height: 16px;
  border-radius: 2px;
  display: inline-block;
  margin-right: 8px;
  border: 1px solid #f0f0f0;
}

.color-picker-trigger {
  display: flex;
  align-items: center;
}

/* 静态数据操作按钮样式 */
.static-data-actions {
  margin-top: 12px;
  display: flex;
  gap: 8px;
}

.static-data-actions .ant-btn {
  border-radius: 4px;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);
  transition: all 0.3s;
}

.static-data-actions .ant-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
</style> 