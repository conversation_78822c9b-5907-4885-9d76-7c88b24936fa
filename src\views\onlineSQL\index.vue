<template>
  <a-layout>
    <!-- 左侧导航栏 -->
    <a-layout-sider width="300" theme="light" style="overflow-x: auto;">
      <a-spin :spinning="loading" style="min-width: 280px;">
        <a-tree
            :tree-data="treeData"
            :field-names="{ title: 'name', key: 'key' }"
            @select="handleNodeSelect"
            style="min-width: 100%;"
        >
          <template #title="{ name, type, rawData, loaded, loading }">
            <span style="display: flex; align-items: center; max-width: 100%;">
              <!-- 数据源图标 -->
              <template v-if="type === 'source'">
                <img :src="dbIcons[(rawData as DataSourceItem).db_type]"
                     :alt="(rawData as DataSourceItem).db_type"
                     style="width: 16px; height: 16px; margin-right: 8px;"/>
              </template>

              <!-- 数据库图标 -->
              <template v-else-if="type === 'database'">
                <database-outlined style="margin-right: 8px;"/>
              </template>

              <!-- 表图标 -->
              <template v-else-if="type === 'table'">
                <table-outlined style="margin-right: 8px;"/>
              </template>

              <!-- 字段图标 -->
              <template v-else-if="type === 'column'">
                <column-width-outlined style="margin-right: 8px;"/>
              </template>

              <span
                  style="
                  flex: 1;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                "
              >
                {{ name }}
              </span>

              <!-- 加载状态指示 -->
              <span v-if="loading" style="margin-left: 8px; flex-shrink: 0;">
                <loading-outlined />
              </span>
            </span>
          </template>
        </a-tree>
      </a-spin>
    </a-layout-sider>

    <!-- 右侧内容区域 -->
    <a-layout-content>
      <div style="display: flex; flex-direction: column; height: 100vh;">
        <!-- SQL输入区域 -->
        <a-card title="SQL在线执行" :bordered="false" style="flex: 0 0 auto;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 16px;">
            <div>
              <span v-if="selectedDatabase">当前数据库：{{ selectedDatabase.db_name }}</span>
              <span v-else style="color: #999">请先选择数据库</span>
            </div>
            <a-button
                type="primary"
                @click="executeSql"
                :disabled="!selectedDatabase"
            >
              执行
            </a-button>
          </div>
          <a-textarea
              v-model:value="sqlStatement"
              placeholder="输入SQL语句..."
              :auto-size="{ minRows: 6, maxRows: 6 }"
          />
        </a-card>

        <!-- 结果展示区域 -->
        <a-card title="执行结果" :bordered="false" style="flex: 1; margin-top: 16px; overflow: auto;">
          <div v-if="errorMessage" class="error-container">
            <a-alert type="error" :message="errorTitle" :description="errorMessage" show-icon />
          </div>

          <a-table
              v-else-if="resultColumns.length > 0"
              :columns="resultColumns"
              :data-source="resultData"
              bordered
              size="small"
          >
            <template #bodyCell="{ column, text }">
              <template v-if="typeof text === 'string' && text.match(/^\d{4}-\d{2}-\d{2}$/)">
                {{ text }} <!-- 日期格式化可根据需要扩展 -->
              </template>
              <template v-else>
                {{ text }}
              </template>
            </template>
          </a-table>
          <div v-else style="color: #999; text-align: center; padding: 20px;">
            请执行有效的查询语句
          </div>
        </a-card>
      </div>
    </a-layout-content>
  </a-layout>
</template>

<script setup lang="ts">
import {ref, onMounted} from 'vue';
import type {TableColumnsType, TreeProps} from 'ant-design-vue';
import {getDatasourceConfig, getTableStructure, handleSql} from '@/api/onlineSQL.ts';
// import {DribbbleOutlined} from '@ant-design/icons-vue';
import {
  DatabaseOutlined,
  TableOutlined,
  ColumnWidthOutlined,
  LoadingOutlined
} from '@ant-design/icons-vue';


// 引入所有数据库图标
import mysqlIcon from '../../assets/icons/database/mysql.png';
import postgresqlIcon from '../../assets/icons/database/postgresql.png';
import sqlserverIcon from '../../assets/icons/database/sqlserver.png';
import oracleIcon from '../../assets/icons/database/oracle.png';
import sqliteIcon from '../../assets/icons/database/sqlite.png';
import cassandraIcon from '../../assets/icons/database/cassandra.png';
import clickhouseIcon from '../../assets/icons/database/clickhouse.png';
import couchbaseIcon from '../../assets/icons/database/couchbase.png';
import db2Icon from '../../assets/icons/database/db2.png';
import dorisIcon from '../../assets/icons/database/doris.png';
import firebirdIcon from '../../assets/icons/database/firebird.png';
import hanaIcon from '../../assets/icons/database/hana.png';
import hiveIcon from '../../assets/icons/database/hive.png';
import kylinIcon from '../../assets/icons/database/kylin.png';
import mariadbIcon from '../../assets/icons/database/mariadb.png';
import oceanbaseIcon from '../../assets/icons/database/oceanbase.png';
import prestodbIcon from '../../assets/icons/database/prestodb.png';
import snowflakeIcon from '../../assets/icons/database/snowflake.png';
import teradataIcon from '../../assets/icons/database/teradata.png';
import tidbIcon from '../../assets/icons/database/tidb.png';
import verticaIcon from '../../assets/icons/database/vertica.png';
import accessIcon from '../../assets/icons/database/access.png';
import kingbaseIcon from '../../assets/icons/database/kingbase.png';
import damengIcon from '../../assets/icons/database/dameng.png';
import gaussdbIcon from '../../assets/icons/database/gaussdb.png';
import tugraphIcon from '../../assets/icons/database/tugraph.png';
import sparkIcon from '../../assets/icons/database/spark.png';
import duckdbIcon from '../../assets/icons/database/duckdb.png';
import starrocksIcon from '../../assets/icons/database/starrocks.png';

interface DatabaseItem {
  id: number;
  db_name: string;
}

interface DataSourceItem {
  host: string;
  port: string;
  db_type: string;
  db_user: string;
  db_pwd: string;
  children: DatabaseItem[];
}

interface ColumnItem {
  name: string;
  type: string;
  comment: string | null;
}

interface TableStructure {
  table_name: string;
  columns: ColumnItem[];
}

interface TreeNode {
  key: string;
  name: string;
  type: 'source' | 'database' | 'table' | 'column';
  dbType?: string;  // 新增类型字段
  children?: TreeNode[];
  rawData: DataSourceItem | DatabaseItem | TableStructure | ColumnItem;
  loaded?: boolean; // 是否已加载子节点
  loading?: boolean; // 是否正在加载
  [key: string]: any; // 允许扩展属性
}

const loading = ref(false);
const treeData = ref<TreeNode[]>([]);

const selectedDatabase = ref<DatabaseItem | null>(null);
const sqlStatement = ref('');
const resultColumns = ref<TableColumnsType>([]);
const resultData = ref<any[]>([]);

// 在script部分新增状态和修改执行方法
const errorMessage = ref<string>('');
const errorTitle = ref('');


// 转换接口数据为树形结构
const transformTreeData = (data: DataSourceItem[]): TreeNode[] => {
  return data.map(source => ({
    key: `source_${source.host}:${source.port}`,
    name: `${source.host}:${source.port} (${source.db_type})`,
    type: 'source',
    dbType: source.db_type,
    children: source.children?.map(db => ({
      key: `db_${db.id}`,
      name: db.db_name,
      type: 'database',
      rawData: db,
      children: [] // 初始为空数组，点击时加载
    })),
    rawData: source
  }));
};
// const transformTreeData = (data: DataSourceItem[]): TreeNode[] => {
//   return data.map(source => ({
//     key: `${source.host}:${source.port}`,
//     name: `${source.host}:${source.port} (${source.db_type})`,
//     type: 'source',
//     dbType: source.db_type,  // 携带数据库类型
//     children: source.children?.map(db => ({
//       key: `db_${db.id}`,
//       name: db.db_name,
//       type: 'database',
//       rawData: db
//     })),
//     rawData: source
//   }));
// };

// 加载表结构
const loadTableStructure = async (node: TreeNode) => {
  try {
    node.loading = true;
    const db = node.rawData as DatabaseItem;
    const response = await getTableStructure(db.id);

    if (response.success) {
      const tables = response.data.map((table: TableStructure) => ({
        key: `${node.key}_table_${table.table_name}`,
        name: table.table_name,
        type: 'table' as const,
        children: table.columns.map((column: ColumnItem) => ({
          key: `${node.key}_table_${table.table_name}_col_${column.name}`,
          name: `${column.name} (${column.type.split(' ')[0]})`,
          type: 'column' as const,
          rawData: column
        })),
        rawData: table,
        loaded: true
      }));

      // 使用 findNode 函数定位需要更新的节点
      const updateTreeData = (nodes: TreeNode[]): TreeNode[] => {
        return nodes.map(n => {
          if (n.key === node.key) {
            return { ...n, children: tables, loaded: true, loading: false };
          }
          if (n.children) {
            return { ...n, children: updateTreeData(n.children) };
          }
          return n;
        });
      };

      treeData.value = updateTreeData(treeData.value);
    }
  } catch (error) {
    console.error('加载表结构失败:', error);
  } finally {
    node.loading = false;
  }
};

// 添加树节点查找工具函数（可选）
const findNode = (key: string, nodes: TreeNode[]): TreeNode | undefined => {
  for (const node of nodes) {
    if (node.key === key) return node;
    if (node.children) {
      const found = findNode(key, node.children);
      if (found) return found;
    }
  }
};

// 处理节点选择
const handleNodeSelect: TreeProps['onSelect'] = async (keys, { node }) => {
  if ((node as any).type === 'database') { // 使用类型断言
    const treeNode = node as unknown as TreeNode; // 双重类型转换
    selectedDatabase.value = treeNode.rawData as DatabaseItem;

    // 如果未加载过子节点，则加载表结构
    if (!treeNode.loaded && !treeNode.children?.length) {
      await loadTableStructure(treeNode);
    }
  } else {
    selectedDatabase.value = null;
  }
};

const executeSql = async () => {
  // 重置状态
  errorMessage.value = '';
  errorTitle.value = '';
  resultColumns.value = [];
  resultData.value = [];

  if (!selectedDatabase.value || !sqlStatement.value.trim()) return;

  try {
    const params = {
      datasource_id: selectedDatabase.value.id,
      sql: sqlStatement.value
    }
    const response = await handleSql(params);

    if (response.success) {
      // 处理表格列
      resultColumns.value = response.data.columns.map((col: string) => ({
        title: col,
        dataIndex: col,
        key: col
      }));

      // 处理表格数据
      resultData.value = response.data.data.map((item: any, index: number) => ({
        key: index,
        ...item
      }));
    } else {
      errorTitle.value = `执行错误 (${response.err_code})`;
      errorMessage.value = response.data || '';
    }
  } catch (error) {
    console.error('SQL执行失败:', error);
  }
};

// 图标映射
const dbIcons: Record<string, any> = {
  mysql: mysqlIcon,
  postgresql: postgresqlIcon,
  sqlserver: sqlserverIcon,
  oracle: oracleIcon,
  sqlite: sqliteIcon,
  cassandra: cassandraIcon,
  clickhouse: clickhouseIcon,
  couchbase: couchbaseIcon,
  db2: db2Icon,
  doris: dorisIcon,
  firebird: firebirdIcon,
  hana: hanaIcon,
  hive: hiveIcon,
  kylin: kylinIcon,
  mariadb: mariadbIcon,
  oceanbase: oceanbaseIcon,
  prestodb: prestodbIcon,
  snowflake: snowflakeIcon,
  teradata: teradataIcon,
  tidb: tidbIcon,
  vertica: verticaIcon,
  access: accessIcon,
  kingbase: kingbaseIcon,
  dameng: damengIcon,
  gaussdb: gaussdbIcon,
  tugraph: tugraphIcon,
  spark: sparkIcon,
  mssql: sqlserverIcon,
  duckdb: duckdbIcon,
  starrocks: starrocksIcon
};

// 获取数据源
const fetchDataSources = async () => {
  try {
    loading.value = true;
    const response = await getDatasourceConfig();
    if (response.success) {
      treeData.value = transformTreeData(response.data);
    }
  } catch (error) {
    console.error('Failed to fetch data sources:', error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchDataSources();
});
</script>

<style scoped>
.ant-layout {
  min-height: 100vh;
}

.ant-layout-sider {
  background: #fff;
  border-right: 1px solid #e8e8e8;
  padding: 16px;
}

.ant-layout-content {
  padding: 16px;
  background: #fff;
}

:deep(.ant-card) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  border-radius: 8px;
}

:deep(.ant-card-head) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.ant-table) {
  margin-top: 16px;
}
.error-container {
  padding: 16px;
  pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    margin: 0;
    font-family: monospace;
  }
}
.ant-tree-node-content-wrapper {
  display: flex;
  align-items: center;
}

.ant-tree-treenode {
  padding: 4px 0;
}

/* 添加树节点最小宽度 */
:deep(.ant-tree-treenode) {
  min-width: 100%;
}

/* 处理节点内容溢出 */
:deep(.ant-tree-node-content-wrapper) {
  max-width: calc(100% - 24px);
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 调整滚动条样式 */
.ant-layout-sider::-webkit-scrollbar {
  width: 12px;
  height: 14px;
}

.ant-layout-sider::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 7px;
}

.ant-layout-sider::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
}
</style>