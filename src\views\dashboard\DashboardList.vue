<template>
  <div class="dashboard-list-container">
    <div class="content-area">
      <div class="layout-container">
        <!-- 左侧分组列表 -->
        <div class="left-sidebar">
          <div class="group-title">看板分组</div>
          <div class="group-list">
            <div 
              v-for="group in dashboardGroups" 
              :key="group.id" 
              class="group-item" 
              :class="{ active: appStore.activeDashboardGroup === group.id }"
              :data-id="group.id"
              @click="selectGroup(group.id)"
            >
              <div class="group-name">
                {{ group.title }} 
                <!-- <span class="count">({{ group.count }})</span> -->
              </div>
              <div class="group-actions" v-if="group.id !== 0">
                <edit-outlined class="action-icon" @click.stop="handleEditGroup(group)" />
                <delete-outlined class="action-icon" @click.stop="showDeleteGroupConfirm(group)" />
              </div>
            </div>
            <div class="add-group">
              <a-button type="text" block class="add-group-btn" @click="handleShowCreateGroup">
                <plus-outlined />
                新建分组
              </a-button>
            </div>
          </div>
        </div>

        <!-- 右侧看板列表 -->
        <div class="main-content">  
          <div v-if="loading" class="loading-container">
            <a-spin size="large" />
          </div>        
          <div v-else class="board-container">
            <div class="boards-row">
              <!-- 新建看板卡片 -->
              <div class="board-card new-board" @click="handleShowCreateDashboardModal">
                <div class="add-icon">
                  <plus-outlined />
                </div>
                <div class="board-title">新建看板</div>
              </div>
              
              <!-- 示例看板卡片 -->
              <div 
                v-for="board in dashboards" 
                :key="board.id" 
                class="board-card"
                @click="openDashboard(board.id)"
              >
                <div class="board-header">
                  <div class="board-title" @click="openDashboard(board.id)">{{ board.title }}</div>
                  <div class="board-actions">
                    <eye-outlined class="action-icon" @click.stop="previewDashboard(board)" />
                    <delete-outlined class="action-icon" @click.stop="showDeleteBoardConfirm(board)" />
                  </div>
                </div>
                <div class="board-preview">
                  <div class="preview-images">
                    <img v-if="board.thumbnail" :src="board.thumbnail" alt="看板预览" />
                    <div v-else class="preview-placeholder">
                      <div class="chart-icon"></div>
                      <div class="chart-icon"></div>
                      <div class="chart-icon"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 新建分组对话框 -->
    <a-modal
      v-model:visible="showCreateGroupModal"
      title="新建分组"
      @ok="createGroup"
      :confirmLoading="creatingGroup"
      okText="确认"
      cancelText="取消"
    >
      <a-form :model="groupForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" ref="createGroupFormRef" :key="createGroupFormKey">
        <a-form-item label="分组名称" name="name" :rules="[{ required: true, message: '请输入分组名称', trigger: ['blur', 'change'] }]">
          <a-input v-model:value="groupForm.name" placeholder="请输入分组名称" maxlength="50" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 编辑分组对话框 -->
    <a-modal
      v-model:visible="editGroupModalVisible"
      title="编辑分组"
      @ok="updateGroup"
      :confirmLoading="updatingGroup"
      okText="确认"
      cancelText="取消"
    >
      <a-form :model="editGroupForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" ref="editGroupFormRef" :key="editGroupFormKey">
        <a-form-item label="分组名称" name="name" :rules="[{ required: true, message: '请输入分组名称', trigger: ['blur', 'change'] }]">
          <a-input v-model:value="editGroupForm.name" placeholder="请输入分组名称" maxlength="50" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 预览看板对话框 -->
    <a-modal
      v-model:visible="showPreviewModal"
      title="预览看板"
      width="80%"
      :maskClosable="false"
      :footer="null"
    >
      <div class="preview-container">
        <iframe v-if="previewUrl" :src="previewUrl" frameborder="0"></iframe>
      </div>
    </a-modal>

    <!-- 新建看板对话框 -->
    <a-modal
      v-model:visible="showCreateDashboardModal"
      title="新建看板"
      @ok="createNewDashboard"
      :confirmLoading="creatingDashboard"
      okText="确认"
      cancelText="取消"
    >
      <a-form :model="dashboardForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" ref="dashboardFormRef" :key="formKey">
        <a-form-item label="看板名称" name="title" :rules="[{ required: true, message: '请输入看板名称', trigger: ['blur', 'change'] }]">
          <a-input v-model:value="dashboardForm.title" placeholder="请输入看板名称" maxlength="50" />
        </a-form-item>
        <a-form-item label="看板描述" name="description">
          <a-textarea v-model:value="dashboardForm.description" placeholder="请输入看板描述" :rows="4" maxlength="100" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';
import { 
  getDashboardGroups, 
  getDashboardList, 
  createDashboard, 
  createDashboardGroup,
  updateDashboardGroup,
  deleteDashboardGroup,
  deleteDashboard
} from '../../api/dashboard';
import type { DashboardGroup, Dashboard } from '../../types/dashboard';
import type { ApiResponse } from '../../types/api';
import { useAppStore } from '@/store/app';

const router = useRouter();
const loading = ref(true);
const dashboardGroups = ref<DashboardGroup[]>([]);
const dashboards = ref<Dashboard[]>([]);
const appStore = useAppStore();

// 新建分组相关
const showCreateGroupModal = ref(false);
const creatingGroup = ref(false);
const groupForm = reactive({
  name: ''
});

// 编辑分组相关
const editGroupModalVisible = ref(false);
const updatingGroup = ref(false);
const editGroupForm = reactive({
  id: 0,
  name: ''
});

// 新建看板相关
const showCreateDashboardModal = ref(false);
const creatingDashboard = ref(false);
const dashboardForm = reactive({
  title: '',
  description: ''
});

// 预览看板相关
const showPreviewModal = ref(false);
const previewUrl = ref('');
const formKey = ref(0); // 用于强制刷新表单
const createGroupFormKey = ref(0); // 用于强制刷新新建分组表单
const editGroupFormKey = ref(0); // 用于强制刷新编辑分组表单
const createGroupFormRef = ref();
const editGroupFormRef = ref();
const dashboardFormRef = ref();

// 获取分组列表
const fetchGroups = async () => {
  try {
    const res = await getDashboardGroups();
    if (res.success) {
      dashboardGroups.value = res.data || [];
      if (dashboardGroups.value.length > 0 && appStore.activeDashboardGroup === -1) {
        appStore.setActiveDashboardGroup(dashboardGroups.value[0].id);
      }
    } else {
      message.error('获取分组失败');
    }
  } catch (error) {
    console.error('获取分组错误', error);
    message.error('获取分组出错');
  }
};

// 获取看板列表
const fetchDashboards = async () => {
  loading.value = true;
  try {
    const res = await getDashboardList(appStore.activeDashboardGroup);
    if (res.success) {
      dashboards.value = res.data || [];
    } else {
      message.error('获取看板列表失败');
    }
  } catch (error) {
    console.error('获取看板列表错误', error);
    message.error('获取看板列表出错');
  } finally {
    loading.value = false;
  }
};

// 选择分组
const selectGroup = (groupId: number) => {
  console.log('选择分组:', groupId);
  appStore.setActiveDashboardGroup(groupId);
  fetchDashboards();
};

// 创建分组
const createGroup = async () => {
  try {
    createGroupFormRef.value?.validate();
  } catch (error) {
    console.error('表单验证失败', error);
  }

  if (!groupForm.name.trim()) {
    message.error('请输入分组名称');
    return;
  }

  creatingGroup.value = true;
  try {
    const res = await createDashboardGroup({ title: groupForm.name.trim() });
    
    if (res.success) {
      message.success('创建分组成功');
      // 清空表单并关闭对话框
      groupForm.name = '';
      showCreateGroupModal.value = false;
      // 加载新分组的看板
      fetchGroups();
    } else {
      message.error(res.err_msg || '创建分组失败');
    }
  } catch (error) {
    console.error('创建分组错误', error);
    message.error('创建分组出错');
  } finally {
    creatingGroup.value = false;
  }
};

// 处理编辑分组
const handleEditGroup = (group: DashboardGroup) => {
  // 检查是否为默认分组(id为0)，如果是则不允许编辑
  if (group.id === 0) {
    message.warning('默认分组不能修改');
    return;
  }
  
  editGroupForm.id = group.id;
  editGroupForm.name = group.title;
  editGroupModalVisible.value = true;
  editGroupFormKey.value ++; // 强制刷新表单
};

// 更新分组
const updateGroup = async () => {
  try {
    editGroupFormRef.value?.validate();
  } catch (error) {
    console.error('表单验证失败', error);
  }

  if (!editGroupForm.name.trim()) {
    message.error('请输入分组名称');
    return;
  }
  
  // 再次检查，确保不是默认分组
  if (editGroupForm.id === 0) {
    message.warning('默认分组不能修改');
    return;
  }

  updatingGroup.value = true;
  try {
    const res = await updateDashboardGroup({
      id: editGroupForm.id,
      title: editGroupForm.name.trim()
    });
    
    if (res.success) {
      message.success('更新分组成功');
      // 更新本地数据
      const index = dashboardGroups.value.findIndex(g => g.id === editGroupForm.id);
      if (index !== -1) {
        dashboardGroups.value[index].title = editGroupForm.name.trim();
      }
      editGroupModalVisible.value = false;
    } else {
      message.error(res.err_msg || '更新分组失败');
    }
  } catch (error) {
    console.error('更新分组错误', error);
    message.error('更新分组出错');
  } finally {
    updatingGroup.value = false;
  }
};

// 显示删除分组确认框
const showDeleteGroupConfirm = (group: DashboardGroup) => {
  // 检查是否为默认分组(id为0)，如果是则不允许删除
  if (group.id === 0) {
    message.warning('默认分组不能删除');
    return;
  }
  
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除分组"${group.title}"吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        const res = await deleteDashboardGroup(group.id);
        if (res.success) {
          message.success('删除分组成功');
          // 从列表中移除
          dashboardGroups.value = dashboardGroups.value.filter(g => g.id !== group.id);
          // 如果删除的是当前选中的分组，则选择第一个分组
          if (appStore.activeDashboardGroup === group.id && dashboardGroups.value.length > 0) {
            appStore.setActiveDashboardGroup(dashboardGroups.value[0].id);
            fetchDashboards();
          }
        } else {
          message.error(res.err_msg || '删除分组失败');
        }
      } catch (error) {
        console.error('删除分组错误', error);
        message.error('删除分组出错');
      }
    }
  });
};

// 预览看板
const previewDashboard = (board: Dashboard) => {
  previewUrl.value = `/dashboard/${board.id}?mode=preview`;
  showPreviewModal.value = true;
};

// 显示删除看板确认框
const showDeleteBoardConfirm = (board: Dashboard) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除看板"${board.title}"吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        const res = await deleteDashboard(board.id);
        if (res.success) {
          message.success('删除看板成功');
          // 从列表中移除
          dashboards.value = dashboards.value.filter(b => b.id !== board.id);
        } else {
          message.error(res.err_msg || '删除看板失败');
        }
      } catch (error) {
        console.error('删除看板错误', error);
        message.error('删除看板出错');
      }
    }
  });
};

// 打开看板
const openDashboard = (id: number) => {
  router.push(`/dashboard/${id}`);
};

const handleShowCreateDashboardModal = () => {
  dashboardForm.title = '';
  dashboardForm.description = '';
  formKey.value ++;
  showCreateDashboardModal.value = true;
};

// 创建新看板
const createNewDashboard = async () => {
  dashboardFormRef.value?.validate();
  if (!dashboardForm.title.trim()) {
    message.error('请输入看板名称');
    return;
  }

  creatingDashboard.value = true;
  try {
    const res = await createDashboard({
      title: dashboardForm.title.trim(),
      dashboard_group_id: appStore.activeDashboardGroup,
      description: dashboardForm.description.trim()
    });
    
    if (res.success) {
      message.success('创建看板成功');
      // 清空表单并关闭对话框
      dashboardForm.title = '';
      dashboardForm.description = '';
      showCreateDashboardModal.value = false;
      // 跳转到新看板页面
      if (res.data) {
        router.push(`/dashboard/${res.data.id}`);
      }
    } else {
      message.error(res.err_msg || '创建看板失败');
    }
  } catch (error) {
    console.error('创建看板错误', error);
    message.error('创建看板出错');
  } finally {
    creatingDashboard.value = false;
  }
};

const handleShowCreateGroup = () => {
  createGroupFormKey.value++;
  groupForm.name = '';
  showCreateGroupModal.value = true;
}


onMounted(async () => {
  await fetchGroups();
  await fetchDashboards();
});
</script>

<style scoped>
.dashboard-list-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  margin: 0;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.header-left {
  display: flex;
  align-items: center;
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.layout-container {
  display: flex;
  height: 100%;
}

.left-sidebar {
  width: 200px;
  background-color: #fff;
  border-right: 1px solid #eee;
  box-sizing: border-box;
  padding-top: 24px;
  padding-bottom: 24px;
}

.group-title {
  font-size: 14px;
  font-weight: bold;
  padding: 0 16px 12px 16px;
  border-bottom: 1px solid #eee;
  text-align: left;
}

.group-list {
  padding: 20px 0 20px 0;
}

.group-item {
  padding: 16px 16px;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  border-radius: 4px;
}

.group-name {
  display: flex;
  align-items: center;
  font-size: 15px;
  color: #333;
  flex: 1;
  min-width: 0;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.count {
  color: #999;
  font-size: 12px;
  margin-left: 4px;
}

.group-item:hover {
  background-color: #f5f7fa;
}

.group-item.active {
  background-color: #e6f0ff;
  color: #1677ff;
}

.group-item.active .group-name {
  color: #1677ff;
}

.group-actions {
  display: flex;
  gap: 8px;
  margin-left: 8px;
}

/* 添加特殊选择器，确保ID为0的分组不显示操作按钮 */
.group-item[data-id="0"] .group-actions {
  display: none !important;
}

.group-item:hover .group-actions {
  display: flex;
}

.action-icon {
  color: #999;
  cursor: pointer;
  font-size: 14px;
}

.action-icon:hover {
  color: #1890ff;
}

.add-group {
  padding: 8px 16px;
  margin-top: 16px;
}

.add-group-btn {
  text-align: left !important;
  color: #999;
  justify-content: flex-start;
  display: flex;
  align-items: center;
}

.main-content {
  flex: 1;
  padding: 0 16px;
  overflow: auto;
}

.tab-header {
  display: flex;
  border-bottom: 1px solid #eee;
  margin-bottom: 16px;
}

.tab-item {
  padding: 12px 16px;
  cursor: pointer;
  position: relative;
  color: #666;
}

.tab-item.active {
  color: #1890ff;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #1890ff;
}

.board-container {
  padding: 0;
}

.boards-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.board-card {
  width: 240px;
  height: 160px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  cursor: pointer;
  display: flex;
  flex-direction: column;
}

.new-board {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px dashed #ccc;
  background: #fafafa;
}

.add-icon {
  font-size: 24px;
  color: #42b983;
  margin-bottom: 8px;
}

.board-title {
  padding: 12px;
  font-size: 14px;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
}

.board-preview {
  flex: 1;
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-images {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.preview-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  opacity: 0.5;
}

.chart-icon {
  width: 48%;
  height: 48%;
  background: #f5f5f5;
  border-radius: 2px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  width: 100%;
}

.board-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.board-actions {
  display: none;
  gap: 8px;
}

.board-card:hover .board-actions {
  display: flex;
}

.preview-container {
  height: 70vh;
  width: 100%;
}

.preview-container iframe {
  width: 100%;
  height: 100%;
  border: none;
}
</style> 