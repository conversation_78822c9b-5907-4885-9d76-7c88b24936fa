<template>
  <div class="model-list">
    <a-row :gutter="[16, 16]">
      <a-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6" v-for="model in modelList" :key="model.id">
        <model-card 
          :model="model" 
          @stop="stopModelAction" 
          @start="startModelAction" 
          @stopAndDelete="confirmStopAndDelete"
        />
      </a-col>
    </a-row>

    <!-- 停止并删除确认对话框 -->
    <a-modal
      v-model:visible="stopAndDeleteModalVisible"
      title="确认停止并删除"
      @ok="handleStopAndDeleteOk"
      @cancel="handleStopAndDeleteCancel"
      okText="确认"
      cancelText="取消"
    >
      <p>确定要停止并删除模型 "{{ currentModel?.name || currentModel?.model_name }}" 吗？</p>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import ModelCard from './ModelCard.vue';
import type { ModelItem } from '../../types/model';
import { getModelList, startModel, stopModel, stopAndDeleteModel } from '../../api/model';

const props = defineProps<{
  refreshTrigger?: number; // 用于触发刷新的属性
}>();

const emit = defineEmits<{
  (e: 'refresh'): void;
}>();

// 模型列表
const modelList = ref<ModelItem[]>([]);
const currentModel = ref<ModelItem | null>(null);

// 停止并删除确认对话框
const stopAndDeleteModalVisible = ref(false);

// 获取模型列表
const fetchModelList = async () => {
  try {
    const response = await getModelList();
    
    if (response.success && response.data) {
      // 处理模型数据，添加必要的兼容字段
      modelList.value = response.data.map(model => ({
        ...model,
        id: model.id || model.model_name, // 使用model_name作为id
        name: model.name || model.model_name,
        manageHost: model.manager_host || '',
        lastHeartBeat: model.last_heartbeat || '',
        status: model.healthy ? 'Healthy' : 'Unhealthy'
      }));
    } else {
      message.error('获取模型列表失败: ' + (response.err_msg || '未知错误'));
      modelList.value = [];
    }
  } catch (error) {
    console.error('获取模型列表出错:', error);
    // message.error('获取模型列表出错，请检查网络连接');
    modelList.value = [];
  }
};

// 停止模型
const stopModelAction = async (model: ModelItem) => {
  let loadingMessage: any = null;
  try {
    loadingMessage = message.loading('正在停止模型...', 0);
    
    const requestData = {
      host: model.host,
      port: model.port,
      model: model.model_name,
      worker_type: model.worker_type,
      params: {}
    };
    
    const response = await stopModel(requestData);
    
    // 立即关闭loading消息
    loadingMessage();
    
    if (response.success) {
      message.success('停止模型成功');
      // 刷新模型列表
      fetchModelList();
      emit('refresh');
    } else {
      message.error('停止模型失败: ' + (response.err_msg || '未知错误'));
    }
  } catch (error) {
    console.error('停止模型出错:', error);
    message.error('停止模型出错，请检查网络连接');
  } finally {
    if (loadingMessage) {
      loadingMessage(); // 确保无论如何都会关闭 loading
    }
  }
};

// 启动模型
const startModelAction = async (model: ModelItem) => {
  let loadingMessage: any = null;
  try {
    loadingMessage = message.loading('正在启动模型...', 0);
    
    const requestData = {
      host: model.host,
      port: model.port,
      model: model.model_name,
      worker_type: model.worker_type,
      params: {}
    };
    
    const response = await startModel(requestData);
    
    // // 立即关闭loading消息
    // loadingMessage();
    
    if (response.success) {
      message.success('启动模型成功');
      // 刷新模型列表
      fetchModelList();
      emit('refresh');
    } else {
      message.error('启动模型失败: ' + (response.err_msg || '未知错误'));
    }
  } catch (error) {
    console.error('启动模型出错:', error);
    message.error('启动模型出错，请检查网络连接');
  } finally {
    if (loadingMessage) {
      loadingMessage(); // 确保无论如何都会关闭 loading
    }
  }
};

// 确认停止并删除模型
const confirmStopAndDelete = (model: ModelItem) => {
  currentModel.value = model;
  stopAndDeleteModalVisible.value = true;
};

// 处理停止并删除确认
const handleStopAndDeleteOk = async () => {
  if (!currentModel.value) return;
  let loadingMessage: any = null;
  try {
    loadingMessage = message.loading('正在停止并删除模型...', 0);
    
    const requestData = {
      host: currentModel.value.host,
      port: currentModel.value.port,
      model: currentModel.value.model_name,
      worker_type: currentModel.value.worker_type,
      delete_after: true,
      params: {}
    };
    
    const response = await stopAndDeleteModel(requestData);
    
    // // 立即关闭loading消息
    // loadingMessage();
    
    if (response.success) {
      message.success('停止并删除模型成功');
      // 刷新模型列表
      fetchModelList();
      emit('refresh');
    } else {
      message.error('停止并删除模型失败: ' + (response.err_msg || '未知错误'));
    }
  } catch (error) {
    console.error('停止并删除模型出错:', error);
    message.error('停止并删除模型出错，请检查网络连接');
  } finally {
    stopAndDeleteModalVisible.value = false;
    currentModel.value = null;
    if (loadingMessage) {
      loadingMessage(); // 确保无论如何都会关闭 loading
    }
  }
};

// 取消停止并删除
const handleStopAndDeleteCancel = () => {
  stopAndDeleteModalVisible.value = false;
  currentModel.value = null;
};

onMounted(() => {
  fetchModelList();
});

// 监听 refreshTrigger 属性变化来触发刷新
if (props.refreshTrigger !== undefined) {
  watch(() => props.refreshTrigger, () => {
    fetchModelList();
  });
}
</script>

<style scoped>
.model-list {
  margin-top: 16px;
}
</style>
