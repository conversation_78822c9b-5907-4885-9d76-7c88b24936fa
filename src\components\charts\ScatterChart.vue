<template>
  <BaseChart 
    :chart-id="chartId" 
    :dashboard-id="dashboardId"
    @data-loaded="onDataLoaded"
    @error="onError"
  >
    <template #default="slotProps">
      <div class="chart-wrapper" v-if="showChat">
        <v-chart :option="processedOptions" autoresize />
      </div>
    </template>
  </BaseChart>
</template>

<script setup lang="ts">
import { ref, markRaw } from 'vue';
import VChart from 'vue-echarts';
import BaseChart from './BaseChart.vue';

// 定义props
const props = defineProps({
  chartId: {
    type: Number,
    required: true
  },
  dashboardId: {
    type: Number,
    required: true
  }
});

// 处理后的图表ECharts配置
const processedOptions = ref<any>({});
const showChat = ref<boolean>(false);

/**
 * 处理散点图数据
 */
const processScatterData = (rawData: any): any => {
  const { config = {}, data, dataMapping } = rawData;
  
  // 如果没有数据，返回基础配置
  if (!data?.columns || !data?.values || data.values.length === 0) {
    return false;
  }

  const { columns, values } = data;
  console.log('rawData', rawData);

  // 使用映射逻辑或默认逻辑
  let xField = 0; // 默认第一列为X轴数据
  let yField = 1; // 默认第二列为Y轴数据
  let sizeField = -1; // 默认不使用大小映射
  let categoryField = -1; // 默认不使用分类映射
  
  // 如果提供了映射配置，使用映射配置
  if (dataMapping) {
    // 查找xField对应的列索引
    if (dataMapping.hasOwnProperty('scatter') && dataMapping.scatter.hasOwnProperty('xField')) {
      const xFieldIndex = columns.findIndex((col: string) => col === dataMapping.scatter.xField);
      if (xFieldIndex !== -1) {
        xField = xFieldIndex;
      }
    }
    console.log('xField', xField);
    
    // 查找yField对应的列索引
    if (dataMapping.hasOwnProperty('scatter') && dataMapping.scatter.hasOwnProperty('yField')) {
      const yFieldIndex = columns.findIndex((col: string) => col === dataMapping.scatter.yField);
      if (yFieldIndex !== -1) {
        yField = yFieldIndex;
      }
    }
    console.log('yField', yField);
    
    // 查找sizeField对应的列索引（可选）
    if (dataMapping.hasOwnProperty('scatter') && dataMapping.scatter.hasOwnProperty('sizeField')) {
      const sizeFieldIndex = columns.findIndex((col: string) => col === dataMapping.scatter.sizeField);
      if (sizeFieldIndex !== -1) {
        sizeField = sizeFieldIndex;
      }
    }
    console.log('sizeField', sizeField);
    
    // 查找categoryField对应的列索引（可选）
    if (dataMapping.categoryField) {
      const categoryFieldIndex = columns.findIndex((col: string) => col === dataMapping.categoryField);
      if (categoryFieldIndex !== -1) {
        categoryField = categoryFieldIndex;
      }
    }
  }
  
  let series;
  
  // 如果指定了分类字段，按分类处理数据
  if (categoryField !== -1) {
    // 提取所有分类
    const categories = Array.from(new Set(values.map((row: any[]) => String(row[categoryField]))));
    
    // 为每个分类创建一个系列
    series = categories.map(category => {
      // 过滤出属于当前分类的数据
      const categoryData = values.filter((row: any[]) => String(row[categoryField]) === category);
      
      // 为该分类构建散点数据
      const data = categoryData.map((row: any[]) => {
        const point = [
          Number(row[xField]) || 0,
          Number(row[yField]) || 0
        ];
        
        // 如果有大小字段，添加大小数据
        if (sizeField !== -1) {
          point.push(Number(row[sizeField]) || 0);
        }
        
        return point;
      });
      
      return {
        name: category,
        type: 'scatter',
        data: data,
        symbolSize: 10,
        // symbolSize: sizeField !== -1 ? (val: any[]) => {
        //   return val[2] * 5; // 根据第三个值调整大小
        // } : 10 // 固定大小
      };
    });
  } else {
    // 如果没有分类字段，将所有数据放在一个系列中
    const data = values.map((row: any[]) => {
      const point = [
        Number(row[xField]) || 0,
        Number(row[yField]) || 0
      ];
      
      // 如果有大小字段，添加大小数据
      if (sizeField !== -1) {
        point.push(Number(row[sizeField]) || 0);
      }
      
      return point;
    });
    
    series = [{
      type: 'scatter',
      data: data,
      symbolSize: 10
      // symbolSize: sizeField !== -1 ? (val: any[]) => {
      //   return val[2] * 5; // 根据第三个值调整大小
      // } : 10 // 固定大小
    }];
  }

  console.log(series);
  
  // 构建返回配置
  return {
    ...config,
    title: {
      show: false
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const data = params.data;
        let result = `(${data[0]}, ${data[1]})`;
        if (data.length > 2) {
          result += `<br/>大小: ${data[2]}`;
        }
        return result;
      }
    },
    xAxis: {
      type: 'value',
      scale: true
    },
    yAxis: {
      type: 'value',
      scale: true
    },
    series: series
  };
};

// 数据加载完成
const onDataLoaded = (rawData: any) => {
  try {
    // 直接处理原始数据
    const processedConfig = processScatterData(rawData);
     if (!processedConfig) {
      showChat.value = false;
      return;
    }

    // 使用markRaw避免Vue对复杂对象进行递归响应式处理
    processedOptions.value = markRaw(processedConfig);
    console.log(rawData);
    showChat.value = true;
  } catch (error) {
    console.error('处理散点图数据出错', error);
    processedOptions.value = {};
     showChat.value = false;
  }
};

// 数据加载错误
const onError = (error: string) => {
  console.error('图表数据加载错误', error);
};
</script>

<style scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;
}
</style> 