<template>
  <div style="margin-top: 5px">
    <van-radio-group v-model="echartsType" @change="typeChange">
      <van-radio name="bar">柱状图</van-radio>
      <van-radio name="line">折线图</van-radio>
      <van-radio name="pie">饼图</van-radio>
    </van-radio-group>
    <div ref="chartDom" style="width: calc(100vw - 52px);max-width: 925px; height: 400px;"></div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  code: {
    type: String,
    required: true
  }
});

const echartsType = ref("bar")
const chartDom = ref(null); // 使用ref代替ID
let myChart:any = null; // 用来保存 ECharts 实例
let option:any = {}//echarts的option


// 当窗口大小变化时
window.addEventListener('resize', function() {
  if (myChart) {
    myChart.resize(); // 调用 ECharts 实例的 resize 方法
  }
});

window.addEventListener('resize', function() {
  myChart.resize();
});

onMounted(() => {
  if (props.code) {
    executeChartCode(props.code);
  }
});

const typeChange = (value: string) => {
  if (value == 'bar') {
    changeToBar()
  } else if (value == 'line') {
    changeToLine()
  } else {
    changeToPie()
  }
}


function executeChartCode(code: string) {
  if (chartDom.value) { // 使用chartDom.value代替document.getElementById
    myChart = echarts.init(chartDom.value);
    // 注意：这里假设props.code是一个有效的JSON字符串，否则JSON.parse会失败
    try {
      option = eval("("+code+")")
      delete option["xAxis"].boundaryGap

      // 确保 option["legend"] 已定义
      if (!option["legend"]) {
        option["legend"] = {};
      }

      // 设置 legend 的 top 属性
      option["legend"].top = 25;
      myChart.setOption(option);
    }catch (e){
      console.log("初始化Echarts失败:"+e)
    }
  }
}


// 切换到柱状图
function changeToBar() {
  (option as any).series[0].type = 'bar';
  myChart.setOption(option);
}

// 切换到折线图
function changeToLine() {
  (option as any).series[0].type = 'line';
  myChart.setOption(option);
}

// 切换到饼图
function changeToPie() {
  var pieOption = generatePieOption(option);
  myChart.setOption(pieOption);
}

// 封装生成饼图配置的方法
function generatePieOption(originalOption :any) {
  // 从原始配置中提取数据
  var xAxisData = originalOption.xAxis.data;
  var seriesData = originalOption.series[0].data;

  // 构建饼图配置
  return {
    title: {
      text: originalOption.title.text,
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [{
      name: originalOption.series[0].name,
      type: 'pie',
      radius: '50%',
      data: xAxisData.map((name :any, index :any) => ({
        name: name,
        value: seriesData[index]
      })),
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  };
}

</script>

<style scoped lang="scss">
</style>
