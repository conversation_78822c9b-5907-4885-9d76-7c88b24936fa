import { createMessageStore } from './message';

type DynamicStoreMap = Map<string, ReturnType<ReturnType<typeof createMessageStore>>>;

export const useMessageDynamicStore = {
  stores: new Map() as DynamicStoreMap,

  // getStore(storeId: string) {
  //   if (!this.stores.has(storeId)) {
  //     const time = new Date().getTime().toString();
  //     this.stores.set(storeId, createMessageStore(time)());
  //   }
  //   return this.stores.get(storeId)!;
  // },

  getStore(storeId: string) {
    if (!this.stores.has(storeId)) {
      this.stores.set(storeId, createMessageStore(storeId)()); // 用 storeId 作为 key
    }
    return this.stores.get(storeId)!;
  },

  createStore(storeId: string) {
    if (!this.stores.has(storeId)) {
      const newStore = this.stores.get('')!
      this.stores.set(storeId, newStore);
      this.stores.delete('');
      // 创建新的store
      const time = new Date().getTime().toString();
      this.stores.set('', createMessageStore(time)());
    }
  },

  deleteStore(storeId: string) {
    if (this.stores.has(storeId)) {
      this.stores.delete(storeId);
    }
  },

  consoleStores() {
    console.log('动态store', this.stores);
  }
};

