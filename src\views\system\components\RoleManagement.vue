<template>
  <div class="role-manage-container">
    <a-form
      layout="inline"
      :model="searchForm"
      @finish="handleFinish"
    >
      <a-form-item label="名称">
        <a-input v-model:value="searchForm.name" placeholder="请输入角色名称">
          <template #prefix><UserOutlined style="color: rgba(0, 0, 0, 0.25)" /></template>
        </a-input>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" html-type="submit" >搜索</a-button>
      </a-form-item>
      <a-form-item @click="resetSearchForm">
        <a-button>重置</a-button>
      </a-form-item>
    </a-form>
    <div class="tool-line">
      <div />
      <div class ="right">
        <a-button type="primary" @click="handleOpenEdit">
          <plus-outlined /> 创建角色
        </a-button>
        <a-popconfirm
          title="确定要删除角色吗?"
          ok-text="确定"
          cancel-text="取消"
          :disabled="selectedRowKeys.length === 0"
          @confirm="deleteRole()"
        >
          <a-button type="primary" danger class="btn-item" :disabled="selectedRowKeys.length === 0" >批量删除</a-button>
        </a-popconfirm>
      </div>
    </div>

    <div class="role-list">
      <a-table
        :columns="columns"
        :data-source="roleList"
        :loading="loading"
        rowKey="id"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        :pagination="getPagination"
        :scroll="{ x: 700 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag color="green" v-if="record.status === 'enabled'">正常</a-tag>
            <a-tag color="red" v-else>停用</a-tag>
          </template>
          <template v-else-if="column.key === 'operation'">
            <a-space>
              <a @click="editRole(record)">编辑</a>
              <a-divider type="vertical" v-if="record.name !== '超级管理员'" />
              <a-popconfirm
                v-if="record.name !== '超级管理员'"
                title="确定要删除此角色吗?"
                ok-text="确定"
                cancel-text="取消"
                @confirm="deleteRole(record)"
              >
                <a class="delete-link">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <RoleEdit ref="roleEditRef" :role="currRole" @refreshData="fetchRoleList()" />
  </div>
</template>

<script setup lang="ts">
import { h, ref, reactive, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import type { GetRoleListDTO, SaveRoleDTO } from '@/types/system';
import RoleEdit from './RoleEdit.vue';
import type { UnwrapRef } from 'vue';
import type { FormProps } from 'ant-design-vue';
import * as api from '@/api/system';

// 表格列定义
const columns = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
    width: 150, // 设置最大宽度
    ellipsis: true // 文本溢出显示省略号
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    width: 250, // 设置最大宽度
    customRender: ({ record } : { record: any }) => {
      return h('div', {
        style: {
          display: '-webkit-box',
          WebkitBoxOrient: 'vertical',
          WebkitLineClamp: 2,
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          wordBreak: 'break-all',
        }
      }, record.description);
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100, // 设置最大宽度
    ellipsis: true // 文本溢出显示省略号
  },
  {
    title: '操作',
    key: 'operation',
    width: 150,
  },
];

interface SearchForm {
  name: string;
}

const searchForm: UnwrapRef<SearchForm> = reactive({
  name: '',
});
const selectedRowKeys = ref<string[]>([]); // 选中的行键数组
const currRole = ref<SaveRoleDTO>(); // 当前编辑的角色信息
const roleEditRef = ref();

const handleOpenEdit = () => {
  currRole.value = undefined;
  roleEditRef.value?.handleOpen();
};

const onSelectChange = (selectedRowKeysValue: string[]) => {
  selectedRowKeys.value = selectedRowKeysValue;
};

const handleFinish: FormProps['onFinish'] = values => {
  fetchRoleList();
};

const resetSearchForm = () => {
  searchForm.name = '';
  fetchRoleList();
};

const roleList = ref<SaveRoleDTO[]>([]);
const loading = ref(false);

const fetchRoleList = async () => {
  loading.value = true;
  try {
    const formData: GetRoleListDTO = {}
    if (searchForm.name) {
      formData.name = searchForm.name;
    }
    const res = await api.getRoleList(formData);
    roleList.value = res.data;
  } catch (error: any) {
    message.error(error?.message || '获取角色列表失败');
  } finally {
    loading.value = false;
  }
};

// 动态计算分页配置
const getPagination = computed(() => {
  const total = roleList.value.length;
  return {
    pageSize: total <= 10 ? 10 : 6,
    showSizeChanger: false, // 不显示分页大小切换器
  };
});

const editRole = (data: SaveRoleDTO) => {
  currRole.value = data;
  roleEditRef.value?.handleOpen();
};

const deleteRole = async (data?: SaveRoleDTO) => {
  let ids: (string | number)[] = [];
  if (data) {
    ids.push(data.id as string);
  } else {
    ids = selectedRowKeys.value;
  }
  await api.deleteRole({ ids });
  message.success('删除成功');
  fetchRoleList();
};

onMounted(() => {
  fetchRoleList();
});
</script>

<style scoped>
.role-manage-container {
  padding: 24px;
}

.tool-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  margin-top: 10px;
  .right {
    display: flex;
    .btn-item {
    margin-left: 10px;
    }
  }
}

.role-list {
  margin-top: 16px;
}

.delete-link {
  color: #ff4d4f;
}
</style> 