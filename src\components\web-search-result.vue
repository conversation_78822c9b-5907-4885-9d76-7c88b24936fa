<template>
  <div class="">
    <div class="network-label">联网搜索结果:</div>
    <div class="sites-cited-list-container">
      <div v-for="item in webSearchResult" class="site-card" @click="openUrl(item.url)">
        <div class="site-description">
          {{ item.snippet }}
        </div>
        <div class="site-info">
          <div class="site-icon" :style="{'background-image': 'url('+item.siteIcon+')'}"></div>
          <div class="site-name">{{ item.siteName }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">

// import type { WebSearchResultVO } from "@/api/webSearchResult/types";

const props = defineProps(
  {webSearchResult: {type: Array as () => any[], required: true}}
)
const openUrl = (url: string) => {
  window.open(url, '_blank');
}
</script>

<style scoped lang="scss">
.network-label {
  margin-top: 6px;
}
.sites-cited-list-container {
  display: flex;
  gap: 8px;
  overflow-x: auto; /* 允许横向滚动 */
  white-space: nowrap; /* 防止子元素换行 */
  padding-bottom: 10px; /* 为滚动条留出空间 */
}

.site-card {
  display: flex;
  flex: 0 1 auto;
  padding: 10px 12px;
  flex-direction: column;
  gap: 6px;
  border-radius: 8px;
  background: var(--bg_grey_1, #f6f7f9);
  min-width: 180px;
  box-sizing: border-box;
  position: relative;
  cursor: pointer; /* 将鼠标指针变成手指形状 */
}

.site-description {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 限制最多显示 2 行 */
  overflow: hidden;
  text-overflow: ellipsis; /* 超出部分用省略号代替 */
  color: var(--txt_icon_black_1, #1a2029);
  font-family: PingFang SC;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  word-break: break-word; /* 允许单词换行 */
  white-space: normal; /* 允许文字换行 */
}

.site-info {
  display: flex;
  align-items: center;
  gap: 4px;

}

.site-icon {
  width: 12px;
  height: 12px;
  background-repeat: no-repeat;
  background-size: cover;
  border-radius: 7px;
  flex: 0 0 12px
}

.site-name {
  overflow: hidden;
  color: var(--txt_icon_grey_5, #838a95);
  text-overflow: ellipsis;
  font-family: PingFang SC;
  font-size: 10px;
  font-style: normal;
  font-weight: 400;
  line-height: 14px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  flex: 0 1 auto;
  word-break: break-word;
}
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background-color: #c0c0c0;
  border-radius: 3px;
}
</style>
