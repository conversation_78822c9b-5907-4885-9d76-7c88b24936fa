import request from '../utils/request';
import type { ApiResponse } from '../types/api';
import type {
  ConversaionDbDTO,
  ConversationDbVO,
  AppInfoDTO,
  AppInfoVO,
  GetParamsDTO,
  GetParamsVO,
  GetMessageListVO,
  AddFeedbackDTO,
  CancelFeedbackDTO,
  Feedback,
  GetMetadataTopicListVO,
  IntentRecognitionDTO,
  DeleteConversationDTO, saveChatDTO,
} from '@/types/app';

export function createConversationInfo(chatMode: string, data: ConversaionDbDTO): Promise<ApiResponse<ConversationDbVO>> {
  return request.post(`/v1/chat/dialogue/new?chat_mode=${chatMode}&model_name=undefined`, data);
} 

export function getAppInfo(params?: AppInfoDTO): Promise<ApiResponse<AppInfoVO>> {
  return request.get('/v1/app/info', params);
}

export function getModels(): Promise<ApiResponse<string[]>> {
  return request.get( '/v1/model/types');
}

export function getParams(params: GetParamsDTO): Promise<ApiResponse<GetParamsVO[]>> {
  return request.post(`/v1/chat/mode/params/list?chat_mode=${params.chat_mode}`);
}


export function clearMemory(con_uid: string) {
  return request.post( `/v1/chat/dialogue/clear?con_uid=${con_uid}`, {
      con_uid
    },
  );
} 

export function getConversationList(): Promise<ApiResponse<ConversationDbVO[]>> {
  return request.get('/v1/chat/dialogue/list', { chat_mode: 'chat_db_with_execute' })
}

export function getMessageList(conversationId: string): Promise<ApiResponse<GetMessageListVO[]>> {
  return request.get( `/v1/chat/dialogue/messages/history?con_uid=${conversationId}`);
}

export function addFeedback(data: AddFeedbackDTO): Promise<ApiResponse<Feedback>> {
  return request.post('/v1/conv/feedback/add', data);
} 

export function cancelFeedback(data: CancelFeedbackDTO) {
  return request.post('/v1/conv/feedback/cancel', data);
} 

export function getMetadataTopicList(id: string | number): Promise<ApiResponse<GetMetadataTopicListVO[]>> {
  return request.get( `/v2/serve/datasources/getTopicList/${id}`);
}

export function intentRecognition(data: IntentRecognitionDTO): Promise<ApiResponse<number | string>> {
  return request.post('/v2/serve/datasources/chat/intentRecognition', data);
} 

export function deleteConversation(data: DeleteConversationDTO): Promise<ApiResponse<boolean>> {
  return request.post(`/v1/chat/dialogue/delete?con_uid=${data.con_uid}`, data);
}

export function saveChat(data: {
  sql: string,
  type: string,
  connect_config_id: string,
  chart_config: string,
  name: string,
  dashboard_id?: number
}) {
  return request.post('/v2/serve/datasources/chat/save', data);
}