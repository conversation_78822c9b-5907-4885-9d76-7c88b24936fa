import { MockMethod } from 'vite-plugin-mock'
import Mock from 'mockjs'

// 生成模拟的看板组
let mockGroups = [
  {
    id: '0',
    name: '默认分组',
    count: 2
  },
  {
    id: '1',
    name: '业务看板',
    count: 1
  }
]

// 生成模拟的看板列表数据
let mockDashboards = [
  {
    id: '1',
    title: '销售数据看板',
    groupId: '0',
    description: '展示销售相关的数据指标',
    thumbnail: null,
    created_at: '2023-01-01 10:00:00',
    updated_at: '2023-01-10 15:30:00',
    created_by: 'user1',
    is_deleted: 0
  },
  {
    id: '2',
    title: '运营数据看板',
    groupId: '0',
    description: '展示运营相关的数据指标',
    thumbnail: null,
    created_at: '2023-02-05 14:20:00',
    updated_at: '2023-02-15 09:45:00',
    created_by: 'user1',
    is_deleted: 0
  },
  {
    id: '3',
    title: '用户分析看板',
    groupId: '1',
    description: '展示用户相关的数据指标',
    thumbnail: null,
    created_at: '2023-03-10 11:30:00',
    updated_at: '2023-03-20 16:15:00',
    created_by: 'user2',
    is_deleted: 0
  }
]

// 模拟图表数据
let mockCharts = [
  // 看板1的图表
  {
    id: 'chart-1',
    dashboard_id: '1',
    title: '月度销售额',
    type: 'bar',
    options: {
      tooltip: { trigger: 'axis' },
      xAxis: { 
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月'] 
      },
      yAxis: { type: 'value' },
      series: [{ 
        name: '销售额',
        type: 'bar',
        data: [5000, 20000, 36000, 10000, 10000, 20000],
        itemStyle: {
          color: '#3aa1ff'
        }
      }]
    },
    position_x: 0,
    position_y: 0,
    width: 4,
    height: 8,
    sort_order: 0,
    created_at: '2023-01-01 10:30:00',
    updated_at: '2023-01-10 15:30:00',
    is_deleted: 0
  },
  {
    id: 'chart-2',
    dashboard_id: '1',
    title: '销售分布',
    type: 'pie',
    options: {
      tooltip: { trigger: 'item' },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [{ 
        name: '销售来源',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 335, name: '直接访问' },
          { value: 310, name: '邮件营销' },
          { value: 234, name: '联盟广告' },
          { value: 135, name: '视频广告' },
          { value: 1548, name: '搜索引擎' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    },
    position_x: 4,
    position_y: 0,
    width: 4,
    height: 8,
    sort_order: 1,
    created_at: '2023-01-01 11:00:00',
    updated_at: '2023-01-10 15:30:00',
    is_deleted: 0
  },
  {
    id: 'chart-3',
    dashboard_id: '1',
    title: '销售趋势',
    type: 'line',
    options: {
      tooltip: { trigger: 'axis' },
      legend: { data: ['销售额', '利润'] },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '销售额',
          type: 'line',
          data: [150, 230, 224, 218, 135, 147],
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#5470c6'
          }
        },
        {
          name: '利润',
          type: 'line',
          data: [50, 80, 96, 86, 45, 56],
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#91cc75'
          }
        }
      ]
    },
    position_x: 8,
    position_y: 0,
    width: 4,
    height: 8,
    sort_order: 2,
    created_at: '2023-01-01 11:30:00',
    updated_at: '2023-01-10 15:30:00',
    is_deleted: 0
  },
  
  // 看板2的图表
  {
    id: 'chart-4',
    dashboard_id: '2',
    title: '用户增长',
    type: 'line',
    options: {
      tooltip: { trigger: 'axis' },
      xAxis: { 
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月'] 
      },
      yAxis: { type: 'value' },
      series: [{ 
        name: '用户数',
        type: 'line',
        data: [1500, 2300, 2240, 2180, 1350, 1470],
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(58,162,254,0.3)' },
              { offset: 1, color: 'rgba(58,162,254,0.1)' }
            ]
          }
        },
        lineStyle: {
          width: 3,
          color: '#3aa1ff'
        }
      }]
    },
    position_x: 0,
    position_y: 0,
    width: 4,
    height: 8,
    sort_order: 0,
    created_at: '2023-02-05 14:30:00',
    updated_at: '2023-02-15 09:45:00',
    is_deleted: 0
  },
  {
    id: 'chart-5',
    dashboard_id: '2',
    title: '用户分布',
    type: 'pie',
    options: {
      tooltip: { trigger: 'item' },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [{ 
        name: '用户来源',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '40',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 1048, name: '北京' },
          { value: 735, name: '上海' },
          { value: 580, name: '广州' },
          { value: 484, name: '深圳' },
          { value: 300, name: '其他' }
        ]
      }]
    },
    position_x: 4,
    position_y: 0,
    width: 4,
    height: 8,
    sort_order: 1,
    created_at: '2023-02-06 15:00:00',
    updated_at: '2023-02-15 09:45:00',
    is_deleted: 0
  },
  {
    id: 'chart-6',
    dashboard_id: '2',
    title: '活跃用户统计',
    type: 'bar',
    options: {
      tooltip: { trigger: 'axis' },
      legend: { data: ['日活', '月活'] },
      xAxis: { 
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月'] 
      },
      yAxis: { type: 'value' },
      series: [
        { 
          name: '日活',
          type: 'bar',
          data: [320, 332, 301, 334, 390, 330],
          stack: 'total',
          emphasis: {
            focus: 'series'
          }
        },
        { 
          name: '月活',
          type: 'bar',
          data: [820, 932, 901, 934, 1290, 1330],
          stack: 'total',
          emphasis: {
            focus: 'series'
          }
        }
      ]
    },
    position_x: 8,
    position_y: 0,
    width: 4,
    height: 8,
    sort_order: 2,
    created_at: '2023-02-07 16:20:00',
    updated_at: '2023-02-15 09:45:00',
    is_deleted: 0
  },
  
  // 看板3的图表
  {
    id: 'chart-7',
    dashboard_id: '3',
    title: '用户留存率',
    type: 'line',
    options: {
      tooltip: { trigger: 'axis' },
      legend: { data: ['7日留存', '14日留存', '30日留存'] },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['1月', '2月', '3月', '4月', '5月', '6月']
      },
      yAxis: { 
        type: 'value',
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [
        {
          name: '7日留存',
          type: 'line',
          data: [55, 60, 56, 58, 62, 65],
          smooth: true
        },
        {
          name: '14日留存',
          type: 'line',
          data: [42, 45, 40, 42, 48, 50],
          smooth: true
        },
        {
          name: '30日留存',
          type: 'line',
          data: [28, 30, 25, 28, 32, 35],
          smooth: true
        }
      ]
    },
    position_x: 0,
    position_y: 0,
    width: 4,
    height: 8,
    sort_order: 0,
    created_at: '2023-03-10 11:40:00',
    updated_at: '2023-03-20 16:15:00',
    is_deleted: 0
  },
  {
    id: 'chart-8',
    dashboard_id: '3',
    title: '访问来源',
    type: 'pie',
    options: {
      tooltip: { trigger: 'item' },
      legend: {
        top: '5%',
        left: 'center'
      },
      series: [{ 
        name: '访问来源',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '70%'],
        data: [
          { value: 1048, name: '搜索引擎' },
          { value: 735, name: '直接访问' },
          { value: 580, name: '邮件营销' },
          { value: 484, name: '联盟广告' },
          { value: 300, name: '视频广告' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    },
    position_x: 4,
    position_y: 0,
    width: 4,
    height: 8,
    sort_order: 1,
    created_at: '2023-03-12 14:20:00',
    updated_at: '2023-03-20 16:15:00',
    is_deleted: 0
  },
  {
    id: 'chart-9',
    dashboard_id: '3',
    title: '页面转化漏斗',
    type: 'funnel',
    options: {
      tooltip: { trigger: 'item', formatter: '{a} <br/>{b} : {c}%' },
      legend: { data: ['访问', '注册', '下单', '支付', '复购'] },
      series: [{ 
        name: '转化率',
        type: 'funnel',
        left: '10%',
        top: 60,
        bottom: 60,
        width: '80%',
        min: 0,
        max: 100,
        minSize: '0%',
        maxSize: '100%',
        sort: 'descending',
        gap: 2,
        label: {
          show: true,
          position: 'inside'
        },
        labelLine: {
          length: 10,
          lineStyle: {
            width: 1,
            type: 'solid'
          }
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        emphasis: {
          label: {
            fontSize: 20
          }
        },
        data: [
          { value: 100, name: '访问' },
          { value: 60, name: '注册' },
          { value: 30, name: '下单' },
          { value: 20, name: '支付' },
          { value: 10, name: '复购' }
        ]
      }]
    },
    position_x: 8,
    position_y: 0,
    width: 4,
    height: 8,
    sort_order: 2,
    created_at: '2023-03-15 10:30:00',
    updated_at: '2023-03-20 16:15:00',
    is_deleted: 0
  }
]

// 获取看板分组列表
const getGroups = {
  url: '/api/dashboard/groups',
  method: 'get',
  response: () => {
    return {
      success: true,
      err_code: null,
      err_msg: null,
      data: mockGroups
    }
  }
}

// 获取看板列表
const getDashboards = {
  url: '/api/dashboard/list',
  method: 'get',
  response: ({ query }) => {
    const { groupId, keyword } = query
    
    let result = [...mockDashboards]
    
    if (groupId) {
      result = result.filter(item => item.groupId === groupId)
    }
    
    if (keyword) {
      result = result.filter(item => 
        item.title.includes(keyword) || 
        item.description.includes(keyword)
      )
    }
    
    return {
      success: true,
      err_code: null,
      err_msg: null,
      data: result
    }
  }
}

// 获取看板详情
const getDashboardDetail = {
  url: '/api/dashboard/detail',
  method: 'get',
  response: ({ query }) => {
    const { id } = query
    const dashboard = mockDashboards.find(item => item.id === id)
    
    if (dashboard) {
      // 查找该看板下的所有图表
      const charts = mockCharts.filter(chart => chart.dashboard_id === id && chart.is_deleted === 0)
      
      return {
        success: true,
        err_code: null,
        err_msg: null,
        data: {
          ...dashboard,
          charts: charts
        }
      }
    } else {
      return {
        success: false,
        err_code: 404,
        err_msg: '看板不存在',
        data: null
      }
    }
  }
}

// 创建新看板
const createDashboard = {
  url: '/api/dashboard/create',
  method: 'post',
  response: ({ body }) => {
    const { title, groupId, description = '' } = body
    
    const newId = Mock.mock('@id')
    
    const newDashboard = {
      id: newId,
      title,
      description,
      groupId,
      thumbnail: '',
      charts: []
    }
    
    // 在实际应用中会将新看板添加到数据库
    
    return {
      success: true,
      err_code: null,
      err_msg: null,
      data: newDashboard
    }
  }
}

// 保存看板数据
const saveDashboard = {
  url: '/api/dashboard/save',
  method: 'post',
  response: ({ body }) => {
    const { id, charts } = body
    const dashboard = mockDashboards.find(item => item.id === id)
    
    if (dashboard) {
      // 更新dashboard的updated_at
      dashboard.updated_at = new Date().toISOString().replace('T', ' ').substring(0, 19)
      
      // 在实际应用中会更新数据库中的charts数据
      if (charts && charts.length > 0) {
        // 先删除该看板下的所有图表
        mockCharts = mockCharts.filter(chart => chart.dashboard_id !== id)
        
        // 然后添加新的图表数据
        charts.forEach((chart, index) => {
          mockCharts.push({
            id: chart.id,
            dashboard_id: id,
            title: chart.title,
            type: chart.type,
            options: chart.options,
            position_x: chart.position_x || 0,
            position_y: chart.position_y || 0,
            width: chart.width || 4,
            height: chart.height || 8,
            sort_order: index,
            created_at: new Date().toISOString().replace('T', ' ').substring(0, 19),
            updated_at: new Date().toISOString().replace('T', ' ').substring(0, 19),
            is_deleted: 0
          })
        })
      }
      
      return {
        success: true,
        err_code: null,
        err_msg: null,
        data: {
          id,
          charts: mockCharts.filter(chart => chart.dashboard_id === id && chart.is_deleted === 0)
        }
      }
    } else {
      return {
        success: false,
        err_code: 404,
        err_msg: '看板不存在',
        data: null
      }
    }
  }
}

// 创建看板分组
const createDashboardGroup = {
  url: '/api/dashboard/group/create',
  method: 'post',
  response: ({ body }) => {
    const { name } = body
    
    const newId = Mock.mock('@id')
    
    const newGroup = {
      id: newId,
      name,
      count: 0
    }
    
    // 在实际应用中会将新分组添加到数据库
    
    return {
      success: true,
      err_code: null,
      err_msg: null,
      data: newGroup
    }
  }
}

// 更新看板分组
const updateDashboardGroup = {
  url: '/api/dashboard/group/update',
  method: 'post',
  response: ({ body }) => {
    const { id, name } = body;
    const group = mockGroups.find(g => g.id === id);
    if (group) {
      group.name = name;
      return {
        success: true,
        data: group
      };
    }
    return {
      success: false,
      err_msg: '分组不存在'
    };
  }
}

// 删除看板分组
const deleteDashboardGroup = {
  url: '/api/dashboard/group/delete/:id',
  method: 'delete',
  response: ({ params }) => {
    const { id } = params;
    const index = mockGroups.findIndex(g => g.id === id);
    if (index !== -1) {
      mockGroups.splice(index, 1);
      // 同时删除该分组下的所有看板
      mockDashboards = mockDashboards.filter(d => d.groupId !== id);
      return {
        success: true,
        data: null
      };
    }
    return {
      success: false,
      err_msg: '分组不存在'
    };
  }
}

// 删除看板
const deleteDashboard = {
  url: '/api/dashboard/delete/:id',
  method: 'delete',
  response: ({ params }) => {
    const { id } = params;
    const index = mockDashboards.findIndex(d => d.id === id);
    if (index !== -1) {
      mockDashboards.splice(index, 1);
      return {
        success: true,
        data: null
      };
    }
    return {
      success: false,
      err_msg: '看板不存在'
    };
  }
}

// 更新单个图表
const updateChart = {
  url: '/api/chart/update',
  method: 'post',
  response: ({ body }) => {
    const { id, dashboard_id, title, options, position_x, position_y, width, height } = body
    
    const chartIndex = mockCharts.findIndex(chart => chart.id === id && chart.dashboard_id === dashboard_id)
    
    if (chartIndex !== -1) {
      // 更新图表数据
      mockCharts[chartIndex] = {
        ...mockCharts[chartIndex],
        title: title || mockCharts[chartIndex].title,
        options: options || mockCharts[chartIndex].options,
        position_x: position_x !== undefined ? position_x : mockCharts[chartIndex].position_x,
        position_y: position_y !== undefined ? position_y : mockCharts[chartIndex].position_y,
        width: width !== undefined ? width : mockCharts[chartIndex].width,
        height: height !== undefined ? height : mockCharts[chartIndex].height,
        updated_at: new Date().toISOString().replace('T', ' ').substring(0, 19)
      }
      
      return {
        success: true,
        err_code: null,
        err_msg: null,
        data: mockCharts[chartIndex]
      }
    } else {
      return {
        success: false,
        err_code: 404,
        err_msg: '图表不存在',
        data: null
      }
    }
  }
}

// 批量更新图表位置
const updateChartsPosition = {
  url: '/api/chart/update_position',
  method: 'post',
  response: ({ body }) => {
    const { charts } = body
    
    if (charts && charts.length > 0) {
      const updatedCharts: any[] = []
      
      for (const chart of charts) {
        const { id, dashboard_id, position_x, position_y, width, height } = chart
        
        const chartIndex = mockCharts.findIndex(c => c.id === id && c.dashboard_id === dashboard_id)
        
        if (chartIndex !== -1) {
          // 更新图表位置数据
          mockCharts[chartIndex] = {
            ...mockCharts[chartIndex],
            position_x,
            position_y,
            width,
            height,
            updated_at: new Date().toISOString().replace('T', ' ').substring(0, 19)
          }
          
          updatedCharts.push(mockCharts[chartIndex])
        }
      }
      
      return {
        success: true,
        err_code: null,
        err_msg: null,
        data: updatedCharts
      }
    } else {
      return {
        success: false,
        err_code: 400,
        err_msg: '无效的请求数据',
        data: null
      }
    }
  }
}

// 创建新图表
const createChart = {
  url: '/api/chart/create',
  method: 'post',
  response: ({ body }) => {
    const { dashboard_id, title, type, options, position_x, position_y, width, height, sort_order = 0 } = body
    
    // 生成唯一的图表ID
    const chartIds = mockCharts
      .filter(c => c.dashboard_id === dashboard_id && c.id.startsWith('chart-'))
      .map(c => parseInt(c.id.replace('chart-', ''), 10))
    
    const nextId = chartIds.length > 0 ? Math.max(...chartIds) + 1 : 1
    const id = `chart-${nextId}`
    
    // 创建新图表
    const newChart = {
      id,
      dashboard_id,
      title,
      type,
      options,
      position_x,
      position_y,
      width,
      height,
      sort_order,
      created_at: new Date().toISOString().replace('T', ' ').substring(0, 19),
      updated_at: new Date().toISOString().replace('T', ' ').substring(0, 19),
      is_deleted: 0
    }
    
    // 添加到图表数组
    mockCharts.push(newChart)
    
    return {
      success: true,
      err_code: null,
      err_msg: null,
      data: newChart
    }
  }
}

export default [
  getGroups,
  getDashboards,
  getDashboardDetail,
  createDashboard,
  saveDashboard,
  createDashboardGroup,
  updateDashboardGroup,
  deleteDashboardGroup,
  deleteDashboard,
  updateChart,
  updateChartsPosition,
  createChart
] as MockMethod[] 