<template>
  <div class="menu-manage-container">
    <a-form
      layout="inline"
      :model="searchForm"
      @finish="handleFinish"
    >
      <a-form-item label="名称">
        <a-input v-model:value="searchForm.name" placeholder="请输入名称">
          <template #prefix><UserOutlined style="color: rgba(0, 0, 0, 0.25)" /></template>
        </a-input>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" html-type="submit" >搜索</a-button>
      </a-form-item>
      <a-form-item @click="resetSearchForm">
        <a-button>重置</a-button>
      </a-form-item>
    </a-form>
    <div class="tool-line">
      <div />
      <div class ="right">
        <a-button type="primary" @click="handleOpenEdit">
          <plus-outlined /> 创建菜单
        </a-button>
        <a-popconfirm
          title="确定要删除菜单吗?"
          ok-text="确定"
          cancel-text="取消"
          :disabled="selectedRowKeys.length === 0"
          @confirm="deleteMenu()"
        >
          <a-button type="primary" danger class="btn-item" :disabled="selectedRowKeys.length === 0" >批量删除</a-button>
        </a-popconfirm>
      </div>
    </div>

    <div class="menu-list">
      <a-table
        :columns="columns"
        :data-source="menuList"
        :loading="loading"
        rowKey="id"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag color="green" v-if="record.status === 'enabled'">正常</a-tag>
            <a-tag color="red" v-else>停用</a-tag>
          </template>
          <template v-else-if="column.key === 'operation'">
            <a-space>
              <a v-if="record.parent_id === 0" @click="handleOpenEdit(record)">新增</a>
              <div v-else class="add-btn" />
              <a @click="editMenu(record)">编辑</a>
              <a-divider type="vertical" />
              <a-popconfirm
                title="确定要删除此菜单吗?"
                ok-text="确定"
                cancel-text="取消"
                @confirm="deleteMenu(record)"
              >
                <a class="delete-link">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <MenuEdit ref="menuEditRef" :menu="currMenu" @refreshData="fetchMenuList()" />
  </div>
</template>

<script setup lang="ts">
import { h, ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import type { GetMenuListDTO, SaveMenuDTO } from '@/types/system';
import MenuEdit from './MenuEdit.vue';
import type { UnwrapRef } from 'vue';
import type { FormProps } from 'ant-design-vue';
import * as api from '@/api/system';

// 表格列定义
const columns = [
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: 'URL',
    dataIndex: 'url',
    key: 'url',
  },
  {
    title: '排序',
    dataIndex: 'sort',
    key: 'sort',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '操作',
    key: 'operation',
    width: 200
  },
];

interface SearchForm {
  name: string;
}

const searchForm: UnwrapRef<SearchForm> = reactive({
  name: '',
});
const selectedRowKeys = ref<string[]>([]); // 选中的行键数组
const currMenu = ref<SaveMenuDTO>(); // 当前编辑的用户信息
const menuEditRef = ref();

const handleOpenEdit = (data?: SaveMenuDTO) => {
  currMenu.value = undefined;
  menuEditRef.value?.handleOpen(data ? data.id : undefined);
};

const onSelectChange = (selectedRowKeysValue: string[]) => {
  selectedRowKeys.value = selectedRowKeysValue;
};

const handleFinish: FormProps['onFinish'] = values => {
  fetchMenuList();
};

const resetSearchForm = () => {
  searchForm.name = '';
  fetchMenuList();
};

const menuList = ref<SaveMenuDTO[]>([]);
const loading = ref(false);

const fetchMenuList = async () => {
  loading.value = true;
  try {
    const formData: GetMenuListDTO = {}
    if (searchForm.name) {
      formData.name = searchForm.name;
    }
    const res = await api.getMenuList(formData);
    menuList.value = res.data;
  } catch (error: any) {
    message.error(error?.message || '获取菜单列表失败');
  } finally {
    loading.value = false;
  }
};

const editMenu = (data: SaveMenuDTO) => {
  currMenu.value = data;
  menuEditRef.value?.handleOpen();
};

const deleteMenu = async (data?: SaveMenuDTO) => {
  let ids: (string | number)[] = [];
  if (data) {
    ids.push(data.id as string);
  } else {
    ids = selectedRowKeys.value;
  }
  await api.deleteMenu({ ids });
  message.success('删除成功');
  fetchMenuList();
};

onMounted(() => {
  fetchMenuList();
});
</script>

<style scoped>
.menu-manage-container {
  padding: 24px;
}

.tool-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  margin-top: 10px;
  .right {
    display: flex;
    .btn-item {
    margin-left: 10px;
    }
  }
}

.menu-list {
  margin-top: 16px;
}

.delete-link {
  color: #ff4d4f;
}

.add-btn {
  width: 28px;
}
</style> 