import request from '../utils/request'
import type { ApiResponse } from '../types/api';
import type { 
  Chart, 
  CreateChartData, 
  UpdateChartData, 
  ChartPosition, 
  DataSet, 
  ChartDataRequest, 
  SqlDataResult
} from '../types/chart';

// 创建新图表
export function createChart(data: CreateChartData): Promise<ApiResponse<Chart>> {
  return request.post('/v2/serve/datasources/chart/add', data);
}

// 更新单个图表
export function updateChart(chartId: number, data: UpdateChartData): Promise<ApiResponse<Chart>> {
  return request.put(`/v2/serve/datasources/chart/update/${chartId}`, data, { timeout: 300000 });
}

// 删除单个图表
export function deleteChart(id: number): Promise<ApiResponse<null>> {
  return request.delete(`/v2/serve/datasources/chart/delete/${id}`);
}

// 批量更新图表位置
export function updateChartsPosition(charts: ChartPosition[]): Promise<ApiResponse<Chart[]>> {
  return request.post('/v2/serve/datasources/chart/update_position', { charts }, { timeout: 300000 });
}

// 获取图表SQL数据
export function getChartData(data: ChartDataRequest): Promise<ApiResponse<SqlDataResult>> {
  return request.post('/v1/editor/chart/run', data, { timeout: 300000 });
}

// 根据图表类型获取数据集
export function getChartDataSetList(chartType: string): Promise<ApiResponse<DataSet[]>> {
  return request.get(`/v2/serve/datasources/chart/fromChatList/${chartType}`);
} 