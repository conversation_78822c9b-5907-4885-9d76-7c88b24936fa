<template>
  <common-chart-settings
    :options="props.options"
    chart-type="funnel"
    @update="updateOptions"
  >
    <!-- 样式配置 -->
    <template #style-settings>
      <!-- 漏斗图设置 -->
      <a-collapse-panel key="funnel" header="漏斗图设置">
        <a-form-item label="漏斗宽度">
          <a-input-number 
            v-model:value="funnelWidth"
            :min="10" 
            :max="100"
            :formatter="(value: number) => `${value}%`"
            :parser="(value: string) => value.replace('%', '')"
            style="width: 100%"
            @change="updateFunnelSize"
          />
        </a-form-item>
        
        <a-form-item label="漏斗方向">
          <a-select v-model:value="chartConfig.series[0].sort">
            <a-select-option value="ascending">升序（小到大）</a-select-option>
            <a-select-option value="descending">降序（大到小）</a-select-option>
            <a-select-option value="none">无序（原始顺序）</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="底部对齐">
          <a-radio-group v-model:value="chartConfig.series[0].orient">
            <a-radio-button value="vertical">垂直</a-radio-button>
            <a-radio-button value="horizontal">水平</a-radio-button>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item label="数值显示">
          <a-radio-group v-model:value="labelPosition" @change="updateLabelPosition">
            <a-radio-button value="inside">内部</a-radio-button>
            <a-radio-button value="outside">外部</a-radio-button>
            <a-radio-button value="none">不显示</a-radio-button>
          </a-radio-group>
        </a-form-item>
      </a-collapse-panel>
    </template>
  </common-chart-settings>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue';
import CommonChartSettings from './CommonChartSettings.vue';
import type { ChartOptions } from '@/types/chart';

const props = defineProps({
  options: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update']);

// 漏斗图配置
const funnelWidth = ref(80);
const labelPosition = ref('inside');

// 直接访问config部分的计算属性
const chartConfig = computed({
  get: () => {
    return props.options.config || getDefaultOptions().config;
  },
  set: (newConfig) => {
    updateConfig(newConfig);
  }
});

// 获取默认配置
const getDefaultOptions = () => {
  return {
    config: {
      type: 'funnel',
      title: {
        text: '',
        show: false
      },
      tooltip: {
        trigger: 'item'
      },
      legend: {
        show: true,
        orient: 'horizontal',
        left: 'right',
        data: []
      },
      series: [
        {
          name: '漏斗图',
          type: 'funnel',
          width: '80%',
          sort: 'descending',
          orient: 'vertical',
          label: {
            show: true,
            position: 'inside'
          },
          emphasis: {
            label: {
              fontSize: 20
            }
          },
          data: [
            { value: 100, name: '访问' },
            { value: 80, name: '咨询' },
            { value: 60, name: '订单' },
            { value: 40, name: '付款' },
            { value: 20, name: '成交' }
          ]
        }
      ],
      color: [] // 自定义颜色
    }
  };
};

// 更新配置
const updateOptions = (newOptions: ChartOptions) => {
  emit('update', newOptions);
};

// 更新config部分
const updateConfig = (newConfig: any) => {
  const updatedOptions = {
    ...props.options,
    config: newConfig
  };
  emit('update', updatedOptions);
};

// 更新漏斗图大小
const updateFunnelSize = () => {
  const config = { ...chartConfig.value };
  config.series[0].width = `${funnelWidth.value}%`;
  updateConfig(config);
};

// 更新标签位置
const updateLabelPosition = () => {
  const config = { ...chartConfig.value };
  
  if (labelPosition.value === 'none') {
    config.series[0].label.show = false;
  } else {
    config.series[0].label.show = true;
    config.series[0].label.position = labelPosition.value;
  }
  
  updateConfig(config);
};

// 初始化组件
onMounted(() => {
  // 初始化宽度
  if (chartConfig.value.series?.[0]) {
    const width = chartConfig.value.series[0]?.width;
    if (typeof width === 'string' && width.endsWith('%')) {
      funnelWidth.value = parseInt(width, 10);
    } else {
      funnelWidth.value = 80;
    }
    
    // 初始化标签位置
    if (!chartConfig.value.series[0].label?.show) {
      labelPosition.value = 'none';
    } else {
      labelPosition.value = chartConfig.value.series[0].label.position || 'inside';
    }
  }
});

// 监听配置变化
watch(() => props.options, (newOptions) => {
  if (newOptions?.config?.series?.[0]) {
    // 更新宽度
    const width = newOptions.config.series[0].width;
    if (typeof width === 'string' && width.endsWith('%')) {
      funnelWidth.value = parseInt(width, 10);
    } else {
      funnelWidth.value = 80;
    }
    
    // 更新标签位置
    if (!newOptions.config.series[0].label.show) {
      labelPosition.value = 'none';
    } else {
      labelPosition.value = newOptions.config.series[0].label.position || 'inside';
    }
  }
}, { deep: true });
</script>

<style scoped>
</style> 