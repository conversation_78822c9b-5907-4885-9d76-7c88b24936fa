<template>
  <div class="model-selector">
    <!-- 选择框 -->
    <div class="selector-dropdown" @click="toggleDropdown">
      <div v-if="selectedModel" class="selected-model">
        <div class="model-icon">
          <img :src="getModelIcon(selectedModel)" :alt="selectedModel" />
        </div>
        <div class="model-name">{{ getModelDisplayName(selectedModel) }}</div>
      </div>
      <div v-else class="placeholder">请选择模型</div>
      <div class="dropdown-arrow" :class="{ 'arrow-up': isDropdownOpen }">
        <svg viewBox="0 0 1024 1024" width="12" height="12">
          <path d="M512 714.666667c-8.533333 0-17.066667-2.133333-23.466667-8.533334l-341.333333-341.333333c-12.8-12.8-12.8-32 0-44.8 12.8-12.8 32-12.8 44.8 0l320 320 320-320c12.8-12.8 32-12.8 44.8 0 12.8 12.8 12.8 32 0 44.8l-341.333333 341.333333c-6.4 6.4-14.933333 8.533333-23.466667 8.533334z" fill="currentColor"></path>
        </svg>
      </div>
    </div>

    <!-- 下拉选项列表 -->
    <div v-if="isDropdownOpen" class="dropdown-panel">
      <div class="search-box">
        <input
          type="text"
          v-model="searchText"
          placeholder="搜索模型..."
          @click.stop
        />
      </div>
      <div class="model-list">
        <div
          v-for="model in filteredModels"
          :key="model"
          class="model-item"
          :class="{ active: modelValue === model }"
          @click.stop="selectModel(model)"
        >
          <div class="model-icon">
            <img :src="getModelIcon(model)" :alt="model" />
          </div>
          <div class="model-name">{{ getModelDisplayName(model) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';

// 引入所有模型图标
import jinaIcon from '../../assets/icons/model/jina.png';
import wenxinIcon from '../../assets/icons/model/wenxin.png';
import yiIcon from '../../assets/icons/model/yi.png';
import moonshotIcon from '../../assets/icons/model/moonshot.png';
import baichuanIcon from '../../assets/icons/model/baichuan.png';
import mistralIcon from '../../assets/icons/model/mistral.png';
import claudeIcon from '../../assets/icons/model/claude.png';
import gptIcon from '../../assets/icons/model/gpt.png';
import tiangongIcon from '../../assets/icons/model/tiangong.png';
import sparkIcon from '../../assets/icons/model/spark.png';
import hunyuanIcon from '../../assets/icons/model/hunyuan.png';
import deepseekIcon from '../../assets/icons/model/deepseek.png';
import doubaoIcon from '../../assets/icons/model/doubao.png';
import qwenIcon from '../../assets/icons/model/qwen.png';
import gemmaIcon from '../../assets/icons/model/gemma.png';
import glmIcon from '../../assets/icons/model/glm.png';
import metaIcon from '../../assets/icons/model/meta.png';
import geminiIcon from '../../assets/icons/model/gemini.png';

// 定义组件属性
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  models: {
    type: Array as () => string[],
    default: () => []
  }
});

// 定义事件
const emit = defineEmits(['update:modelValue', 'change']);

// 状态变量
const isDropdownOpen = ref(false);
const searchText = ref('');

// 获取显示用的模型名称
const getModelDisplayName = (model: string) => {
  if (!model) return '';
  
  return model;
};

// 获取模型对应的图标
const getModelIcon = (model: string) => {
  if (!model) return gptIcon;
  
  // 获取纯净的模型名称（去除前缀等）
  const modelName = getModelDisplayName(model);
  const modelLower = modelName.toLowerCase();
  
  // 模型名称到图标的映射
  const iconMap: Record<string, any> = {
    // 常见模型前缀匹配
    'gpt': gptIcon,
    'claude': claudeIcon,
    'mistral': mistralIcon,
    'gemini': geminiIcon,
    'llama': metaIcon,
    'glm': glmIcon,
    'chatglm': glmIcon,
    'gemma': gemmaIcon,
    'qwen': qwenIcon,
    'doubao': doubaoIcon,
    'deepseek': deepseekIcon,
    'hunyuan': hunyuanIcon,
    'spark': sparkIcon,
    'tiangong': tiangongIcon,
    'baichuan': baichuanIcon,
    'wenxin': wenxinIcon,
    'ernie': wenxinIcon,
    'yi': yiIcon,
    'jina': jinaIcon,
    'moonshot': moonshotIcon,
  };
  
  // 尝试匹配模型名称中的前缀
  for (const [prefix, icon] of Object.entries(iconMap)) {
    if (modelLower.startsWith(prefix)) {
      return icon;
    }
  }
  
  // 如果没有匹配到，再尝试部分匹配
  for (const [key, icon] of Object.entries(iconMap)) {
    if (modelLower.includes(key)) {
      return icon;
    }
  }
  
  // 如果仍然没有匹配到，返回默认图标
  return gptIcon;
};

// 点击外部关闭下拉框
const handleClickOutside = (e: MouseEvent) => {
  const target = e.target as HTMLElement;
  if (!target.closest('.model-selector')) {
    isDropdownOpen.value = false;
  }
};

// 过滤模型列表
const filteredModels = computed(() => {
  if (!searchText.value) return props.models;
  const search = searchText.value.toLowerCase();
  return props.models.filter(model => {
    const displayName = getModelDisplayName(model).toLowerCase();
    return displayName.includes(search);
  });
});

// 切换下拉框状态
const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value;
  if (isDropdownOpen.value) {
    searchText.value = '';
  }
};

// 选择模型
const selectModel = (model: string) => {
  emit('update:modelValue', model);
  emit('change', model);
  isDropdownOpen.value = false;
};

// 获取当前选中的模型
const selectedModel = computed(() => {
  return props.modelValue;
});

// 组件生命周期钩子
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.model-selector {
  width: 100%;
  position: relative;
}

/* 选择框样式 */
.selector-dropdown {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 8px 12px;
  min-height: 42px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #fff;
}

.selector-dropdown:hover {
  border-color: #40a9ff;
}

.selected-model {
  display: flex;
  align-items: center;
  flex: 1;
}

.placeholder {
  color: #bfbfbf;
  font-size: 14px;
}

.dropdown-arrow {
  color: #bfbfbf;
  transition: transform 0.3s;
  margin-left: 8px;
}

.arrow-up {
  transform: rotate(180deg);
}

/* 下拉面板样式 */
.dropdown-panel {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  right: 0;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 360px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-box {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.search-box input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  font-size: 14px;
  background-color: #fff;
  outline: none;
  transition: all 0.3s;
}

.search-box input:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.search-box input::placeholder {
  color: #bfbfbf;
}

/* 模型列表样式 */
.model-list {
  overflow-y: auto;
  max-height: 280px;
  padding: 8px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.model-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  border-radius: 4px;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s;
}

.model-item:hover {
  background-color: #f5f5f5;
}

.model-item.active {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
}

.model-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.model-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.model-name {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 适配选中状态和下拉状态样式 */
.selector-dropdown.focus,
.selector-dropdown:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}
</style>
