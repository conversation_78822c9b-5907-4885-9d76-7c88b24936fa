# 数据分析看板

一个基于Vue3、Vite和ECharts的数据分析看板项目。

## 功能特点

- 灵活的看板布局，支持拖拽调整图表大小和位置
- 多种图表类型选择（柱状图、折线图、饼图、散点图）
- 图表配置自动保存和恢复

## 安装依赖

```bash
npm install
```

如果安装过程中出现问题，可能需要使用管理员权限或尝试以下命令：

```bash
npm install --force
```

## 使用的核心库

- Vue 3 - 前端框架
- Vue Grid Layout - 实现拖拽布局
- ECharts - 图表绘制
- Vue ECharts - Vue3对ECharts的封装

## 开发指南

### 开发环境启动

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产构建

```bash
npm run preview
```

## 项目结构

```
src/
├── assets/              # 静态资源
├── components/          # 组件
│   ├── selector/        # 选择器组件
│   │   └── ChartSelector.vue # 图表选择器
│   └── charts/          # 图表组件
│       ├── BarChart.vue
│       ├── LineChart.vue
│       ├── PieChart.vue
│       └── ScatterChart.vue
├── views/               # 页面视图
│   └── dashboard/       # 看板相关页面
│       ├── Dashboard.vue        # 主看板组件
│       ├── DashboardList.vue    # 看板列表页面
│       └── ChartSettingsDrawer.vue # 图表设置抽屉组件
├── App.vue              # 应用入口组件
└── main.ts              # 应用入口文件
```

## 扩展指南

### 添加新图表类型

1. 在`src/components/charts/`目录下创建新的图表组件
2. 在`src/views/dashboard/Dashboard.vue`中导入并注册新组件
3. 在`src/components/selector/ChartSelector.vue`中添加新图表类型选项

## 许可证

MIT
