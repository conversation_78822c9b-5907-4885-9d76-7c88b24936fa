<template>
  <el-scrollbar class="chat-init-scrollbar">
    <div class="chat-init-container" :style="{ minHeight: minHeight + 'px' }">
      <div id="chat-init-body">
        <div class="chat-init-title">智能数据分析平台</div>
        <div class="chat-init-subtitle">Hi~ 我是你的专属智能助手小七，你可以随时向我描述你的需求。</div>
        <el-row :gutter="20" class="chat-init-list">
          <el-col :sm="24" :md="12" v-for="item in appList" :key="item.key" @click="() => goPage(item.path)">
            <div class="chat-init-item">
              <div class="icon">
                <component :is="item.icon" />
              </div>
              <div class="content">
                <div class="title">{{ item.title }}</div>
                <div class="subtitle">{{ item.subtitle }}</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </el-scrollbar>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useConversationStore } from '@/store/conversation';
import { useRouter } from 'vue-router';
import { appList } from '@/config';


const router = useRouter()

const conversationStore = useConversationStore()

const minHeight = ref(0);

const goPage = (path?: string) => {
  if (path === undefined || path === '/chatDashboard') return;
  router.push(path);
  conversationStore.setActiveConversation();
}

onMounted(() => {
  setTimeout(() => {
    minHeight.value = (document.getElementById('chat-init-body')?.clientHeight || 0) + 36;
  })
});
</script>

<style lang="scss" scoped>
.chat-init-scrollbar {
  height: calc(100vh - 165px);
}
.chat-init-container {
  position: relative;
  margin: 0 auto;
  padding-top: 10vh;
  max-width: variable.$chat-width;
  height: calc(90vh - 165px);
  
  .chat-init-title {
    margin: 0 20px;
    font-size: 30px;
    font-weight: 700;
    color: variable.$text-color;
  }
  .chat-init-subtitle {
    margin: 12px 20px 0;
    color: variable.$sub-text-color;
  }
  .chat-init-list {
    margin-top: 20px;
    .el-col {
      margin-bottom: 20px;
    }
    .chat-init-item {
      padding: 20px;
      background-color: #ffffff;
      width: 100%;
      border-radius: 14px;
      border: 1px solid rgba(0, 0, 0, 0.08);
      box-sizing: border-box;
      height: 100%;
      display: flex;
      align-items: center;
      cursor: pointer;
      .icon {
        margin-right: 10px;
        width: 48px;
        height: 48px;
        background: rgba(0, 0, 0, 0.08);
        border-radius: 16px;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          width: 30px;
          height: 30px;
        }
      }
      .content {
        flex: 1;
        .title {
          font-weight: 500;
          font-size: 16px;
          color: variable.$text-color;
        }
        .subtitle {
          margin-top: 10px;
          color: variable.$sub-text-color;
          text-align: left;
        }
      }
      
    }
  }

  .navigation-tags {
    position: absolute;
    bottom: 0;
    width: 100%;
  }
}
</style>