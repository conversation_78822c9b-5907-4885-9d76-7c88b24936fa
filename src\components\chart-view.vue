<template>
  <div class="chat-view-container">
    <a-tabs v-model:activeKey="activeTabKey" v-if="type === 'response_table'">
      <a-tab-pane key="data" tab="Data">
        <a-table v-if="data" :dataSource="data" :columns="columns" :scroll="{ x: columns.length * 150 }" />
      </a-tab-pane>
      <a-tab-pane key="sql" tab="SQL">
        <CodePreview :code="formatSql(sql ?? '', 'mysql')" language="sql" />
      </a-tab-pane>
    </a-tabs>
    <a-tabs v-model:activeKey="activeTabKey" v-else-if="type !== ''">
      <a-tab-pane key="chart" tab="Chart">
        <AutoChart v-if="data && type" :data="data" :chartType="getChartType(type as any)"
                   :messageId="messageId"
                   @chartTypeChange="handleChartTypeChange"/>
      </a-tab-pane>
      <a-tab-pane key="sql" tab="SQL">
        <CodePreview :code="formatSql(sql ?? '', 'mysql')" language="sql" />
      </a-tab-pane>
      <a-tab-pane key="data" tab="Data">
        <a-table v-if="data" :dataSource="data" :columns="columns" :scroll="{ x: columns.length * 150 }" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { getChartType } from '@/components/autoChart/helpers';
import type { AntdTableColumn } from '@/types/app';
import CodePreview from './code-preview.vue';
import AutoChart from './auto-chart.vue';
import { formatSql } from '@/utils';

const props = defineProps({
  answer: {
    type: String,
    required: true
  },
  messageId: {
    type: String,
    default: ''
  },
});

const data = ref<any>();
const sql = ref<string>('');
const type = ref<string>('');
const activeTabKey = ref<string>('');
const columns = ref<AntdTableColumn[]>([]);

const emit = defineEmits(['chartTypeChange']);

const handleChartTypeChange = (chartType: string) => {
  // 触发事件传递选中值
  console.log('chart-view', chartType);
  emit('chartTypeChange', chartType);
}

watch(() => props.answer, 
  (newVal) => {
    const match = newVal.match(/content="([^"]*)"/);
    if (!match) {
      data.value = null;
      sql.value = '';
      type.value = '';
      return;
    }
    // 对匹配到的字符串进行 HTML 实体解码并解析为 JSON 对象
    const decodedString = match[1].replace(/&quot;/g, '"');
    const jsonData = JSON.parse(decodedString);

    data.value = jsonData?.data;
    sql.value = jsonData?.sql;
    type.value = jsonData?.type;

    columns.value = data.value?.[0]
    ? Object.keys(data.value?.[0])?.map(item => {
        return {
          title: item,
          dataIndex: item,
          key: item,
          width: 150,
        };
      })
    : [];

    if (type.value === 'response_table') {
      activeTabKey.value = 'data'; 
    } else if (type.value !== '') {
      activeTabKey.value = 'chart'; 
    }
  },{
    deep: true,
    immediate: true,
  }
)

</script>

