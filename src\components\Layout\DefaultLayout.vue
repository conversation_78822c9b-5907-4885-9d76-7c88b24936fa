<template>
  <div class="layout">
    <a-layout class="main-layout" v-if="!userStore.isExternal && !appStore.isPreview">
      <a-layout-header class="header">
        <div class="logo">
          <img src="../../assets/images/logo.png" alt="AI管理平台" class="logo-image" />
          <span class="logo-text">DataGPT</span>
        </div>
        <div class="nav-menu">
          <a-menu
            mode="horizontal"
            theme="dark"
            :selected-keys="[activeKey]"
          >
            <a-menu-item 
              v-for="menuItem in userMenuList"
              :key="menuItem.key"
              @click="handleMenuSelect(menuItem)">
              <Component :is="menuItem.icon" />
              {{ menuItem.label }}
            </a-menu-item>
          </a-menu>
        </div>
        <div class="header-right">
          <a-dropdown>
            <a class="user-dropdown" @click.prevent>
              <a-avatar size="small" class="avatar">
                {{ firstChar }}
              </a-avatar>
              <span class="username">{{ username }}</span>
            </a>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="handleEditPassword">
                  <form-outlined />
                  修改密码
                </a-menu-item>
                <a-menu-item @click="handleLogout">
                  <logout-outlined />
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>
      <a-layout-content class="content">
        <div v-if="route.path.startsWith('/chat')">
          <a-layout>
            <HistoryList />
            <a-layout>
              <router-view />
            </a-layout>
          </a-layout>
        </div>
        <router-view v-else />
      </a-layout-content>
    </a-layout>
    <a-layout class="main-layout" v-else>
      <div v-if="route.path.startsWith('/chat')">
        <a-layout>
          <HistoryList />
          <a-layout>
            <router-view />
          </a-layout>
        </a-layout>
      </div>
      <router-view v-else />
    </a-layout>
    <PasswordEdit ref="passwordEditRef" />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import HistoryList from '../HistoryList.vue';
import PasswordEdit from './PasswordEdit.vue';
import { useUserStore } from '@/store/user';
import { useAppStore } from '@/store/app';
import type { MenuItem } from './config';
import { userMenuList } from './config';
import { LogoutOutlined, FormOutlined, } from '@ant-design/icons-vue';

const router = useRouter();
const route = useRoute();
const username = ref<string>('用户');
const activeKey = ref('dashboard');
const passwordEditRef = ref();
const userStore = useUserStore();
const appStore = useAppStore();

// 获取用户名首字符用于头像显示
const firstChar = computed(() => {
  return username.value.charAt(0).toUpperCase();
});

const handleEditPassword = () => {
  passwordEditRef.value?.handleOpen();
}

// 退出登录
const handleLogout = () => {
  userStore.logout();
};

// 处理菜单选择
const handleMenuSelect = (item: MenuItem) => {
  activeKey.value = item.key;
  router.push(item.path);
};

// 根据当前路由设置激活菜单项
const setActiveMenu = () => {
  let path = route.path;
  if (path.includes('topic') || path.includes('metadata')) {
    path = '/database';
  }
  const arr = path.split('/');
  if (arr.length > 1) {
    path = arr[0] + '/' + arr[1];
  }
  userMenuList.value.map((item: MenuItem) => {
    if (item.path.includes(path)) {
      activeKey.value = item.key;
    }
  })
};

// 监听路由变化
watch(() => route.path, () => {
  setActiveMenu();
}, {
  immediate: true,
});

watch(() => userStore.userInfo, 
  (newUserInfo) => {
    if (newUserInfo) {
      username.value = newUserInfo.username;
    } else {
      username.value = '用户';
    }
  },
  {
    deep: true,
    immediate: true,
  }
);

onMounted(() => {
  // 设置激活菜单
  setActiveMenu();
});
</script>

<style scoped>
.layout {
  height: 100vh;
  width: 100%;
  overflow: hidden;
  padding: 0;
  margin: 0;
}

.main-layout {
  height: 100vh;
  width: 100%;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #001529;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  padding: 0 24px;
  z-index: 10;
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  height: 64px;
  color: white;
}

.logo {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  color: white;
  white-space: nowrap;
  margin-right: 24px;
}

.logo-image {
  height: 40px;
  margin-right: 10px;
}

.logo-text {
  font-size: 18px;
  font-weight: bold;
}

.nav-menu {
  flex: 1;
}

:deep(.ant-menu-dark) {
  background: transparent;
}

:deep(.ant-menu-horizontal) {
  border-bottom: none;
  line-height: 64px;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.85);
  padding: 0 12px;
}

.avatar {
  background-color: #1890ff;
  color: #fff;
  margin-right: 8px;
}

.username {
  margin-left: 8px;
}

.content {
  padding: 12px;
  background: #f0f2f5;
  margin-top: 64px;
  height: calc(100vh - 64px);
  overflow: auto;
  width: 100%;
  box-sizing: border-box;
}
</style> 