<template>
  <a-layout-sider class="sider">
    <div class="logo-title" v-if="userStore.isExternal">数据分析助手</div>
    <div class="history-title">对话列表</div>
    <a-menu mode="inline" class="history-menu" v-model:selectedKeys="selectedKeys" 
      :style="{ height: userStore.isExternal ? '100vh' : 'calc(100vh - 64px - 24px - 46px)'}">
      <a-menu-item :key="-1">
        <div class="menu-item" @click="goNewChat">
          <div class="new-chat">
            <a-button :icon="h(CommentOutlined)" shape="round" class="new-chat-btn" v-if="!selectedKeys.includes(-1)">新对话</a-button>
            <div class="new-chat-active" v-else>
              <component :is="getAppIcon('chat_with_db_execute')" class="icon" />
              新对话
            </div>
          </div>
        </div>
      </a-menu-item>
      <a-menu-item v-for="(historyItem, historyIndex) in historyStore.filterConversation()" :key="historyIndex">
        <div class="menu-item"
          @mouseenter="hoveredIndex = historyIndex"
          @mouseleave="hoveredIndex = null"
          @click="historyStore.onSelectConversation(historyItem)">
          <component :is="getAppIcon(historyItem.chat_mode || '')" class="icon" />
          <span class="label">{{ historyItem.user_input }}</span>
          <div class="tool-icon" 
            v-if="hoveredIndex === historyIndex"
            @click.stop="historyStore.onCopyUrl(historyItem)">
            <share-alt-outlined />
          </div>
          <div class="tool-icon delete-icon" 
            v-if="hoveredIndex === historyIndex"
            @click.stop="handleDeleteConversation(historyItem, selectedKeys.includes(historyIndex))">
            <delete-outlined />
          </div>
        </div>
      </a-menu-item>
    </a-menu>
  </a-layout-sider>
</template>

<script lang="ts" setup>
import { h, ref, watch } from 'vue';
import { useUserStore } from '@/store/user';
import { useHistoryStore } from '@/store/history';
import type { ConversationDbVO } from '@/types/app';
import { useConversationStore } from '@/store/conversation';
import { ShareAltOutlined, DeleteOutlined, CommentOutlined } from '@ant-design/icons-vue';
import { Modal } from 'ant-design-vue';
import { useRoute, useRouter } from 'vue-router';
import { getAppIcon } from '@/utils';

const userStore = useUserStore();
const historyStore = useHistoryStore();
const selectedKeys = ref<(string | number)[]>([-1]);
const hoveredIndex = ref<number | null>(null); // 新增 hover 状态
const conversationStore = useConversationStore();
const route = useRoute();
const router = useRouter();

const goNewChat = () => {
  router.push('/chatData')
}

const handleDeleteConversation = (historyItem: ConversationDbVO, goChat: boolean) => {
  Modal.confirm({
    title: '提示',
    content: h('div', {}, [
      h('p', '确认删除会话吗？'),
    ]),
    okText: '确认',
    cancelText: '取消',
    onOk() {
      conversationStore.deleteConversation(historyItem, goChat);
    },
  });
}

watch(
  () => [route.params, historyStore.filterConversation()],
  async ([newParams, newConversations]) => {
    if ((newParams as any).conversationId && (newConversations as ConversationDbVO[]).length > 0) {
      for (let i = 0; i < (newConversations as ConversationDbVO[]).length; i++) {
        const item = (newConversations as ConversationDbVO[])[i];
        if ((newParams as any).conversationId === item.conv_uid) {
          conversationStore.setActiveConversation(item);
          selectedKeys.value = [i];
        }
      }
    } else if (!(newParams as any).conversationId) {
      conversationStore.setActiveConversation({});
      selectedKeys.value = [-1];
    }
  },
  { immediate: true, deep: true },
)

</script>

<style lang="scss" scoped>
  .sider {
    text-align: center;
    line-height: 120px;
    background-color: #FFFFFF;
    .logo-title {
      height: 80px;
      font-size: 20px;
      line-height: 80px;
      text-align: left;
      padding-left: 20px;
    }
    .history-title {
      text-align: left;
      padding: 10px 20px;
      line-height: 1.6;
      font-weight: bold;
      font-size: 16px;
    }
    .history-menu {
      height: calc(100vh - 64px - 24px - 46px);
      overflow-x: hidden;
      overflow-y: auto;
      .menu-item {
        display: flex;
        align-items: center;
        .icon {
          width: 1.75rem;
          height: 1.75rem;
          margin-right: 10px;
        }
        .tool-icon {
          margin-left: 4px;
          cursor: pointer;
        }
        .label {
          width: 120px;
          /* 禁止文本换行 */
          white-space: nowrap;
          /* 隐藏溢出内容 */
          overflow: hidden;
          /* 溢出部分显示省略号 */
          text-overflow: ellipsis;
        }
        .new-chat {
          width: 100%;
          .new-chat-btn {
            width: 100%;
          }
          .new-chat-active {
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }
</style>
