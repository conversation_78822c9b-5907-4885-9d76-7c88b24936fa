<template>
  <div class="system-manage-container">
    <a-layout class="system-manage-layout">
      <a-layout-sider>
        <a-menu v-model:selectedKeys="selectedKeys" theme="light" mode="inline" @click="handleClickMenuItem">
          <a-menu-item key="user" v-if="userStore.permissionMap.hasOwnProperty('用户管理')">
            <UserOutlined />
            <span>用户管理</span>
          </a-menu-item>
          <a-menu-item key="role" v-if="userStore.permissionMap.hasOwnProperty('角色管理')">
            <TeamOutlined />
            <span>角色管理</span>
          </a-menu-item>
          <!-- <a-menu-item key="menu">
            <MenuOutlined />
            <span>菜单管理</span>
          </a-menu-item> -->
        </a-menu>
      </a-layout-sider>
      <a-layout>
        <a-layout-content style="margin: 0 16px">
          <component 
            :is="componentMode === 'user' ? UserManagement : componentMode === 'role' ? RoleManagement : componentMode === 'menu' ? MenuManagement : null" />
        </a-layout-content>
      </a-layout>
    </a-layout>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { UserOutlined, TeamOutlined, MenuOutlined } from '@ant-design/icons-vue';
import UserManagement from './components/UserManagement.vue';
import RoleManagement from './components/RoleManagement.vue';
import MenuManagement from './components/MenuManagement.vue';
import { useRoute, useRouter  } from 'vue-router';
import { useUserStore } from '@/store/user';

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const selectedKeys = ref<string[]>(['']);
const componentMode = ref<string>('');

const handleClickMenuItem = ({ key }: { key: string }) => {
  router.replace({
    path: `/system/${key}`
  });
} 

watch(
  () => route.params.mode, 
  (val) => {
    if ((val === 'user' && userStore.permissionMap.hasOwnProperty('用户管理')) ||
        (val === 'role' && userStore.permissionMap.hasOwnProperty('角色管理'))) {
      selectedKeys.value = [val as string];
      componentMode.value = val as string;
    }
  }, 
  {
    immediate: true,
    deep: true,
  }
);
</script>

<style scoped lang="scss">
  :deep(.ant-layout-sider) {
    background: #FFFFFF;
  }
  .system-manage-layout {
    height: calc(100vh - 90px);
  }
</style> 