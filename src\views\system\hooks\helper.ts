import { menuOrderMap } from './config';
import type { SaveMenuDTO, RoleMenuItem } from '@/types/system';
import type { CommonNumberStringMap } from './type';

export type RoleMenuItemMap = {
  [key: number]: SaveMenuDTO | RoleMenuItem;
};

export type MenuItemMap = {
  [key: number]: SaveMenuDTO;
};

export function sortMenuByOrder(menuList: (SaveMenuDTO | RoleMenuItem)[]) {
  const menuItemMap : RoleMenuItemMap = {};
  const result : (SaveMenuDTO | RoleMenuItem)[] = [];
  let orderKey = 10000;
  menuList.forEach((item) => {
    const menuName = item.hasOwnProperty('menu_name') ? (item as RoleMenuItem).menu_name : (item as SaveMenuDTO).name;
    let order;
    if (menuOrderMap.hasOwnProperty(menuName as keyof typeof menuOrderMap)) {
      order = menuOrderMap[menuName as keyof typeof menuOrderMap];
    } else {
      order = orderKey;
      orderKey++;
    }
    menuItemMap[order] = item;
  });
  for (const key in menuItemMap) {
    result.push(menuItemMap[key]);
  }
  return result;
}

export function genMenuMap (menuList: SaveMenuDTO[]) {
  const menuItemMap : MenuItemMap = {};
  menuList.forEach((item) => {
    const menuId = item.id;
    if (menuId !== undefined) {
      menuItemMap[Number(menuId)] = item;
    }
    item?.children?.forEach((item) => {
      const menuId = item.id;
      if (menuId !== undefined) {
        menuItemMap[Number(menuId)] = item;
      }
    });
  });
  return menuItemMap;
}

export function filterParentNode(menuList: SaveMenuDTO[], selectedKeys: (string | number | undefined)[]) {
  const result: (string | number)[] = [];
  const selectedKeyMap : CommonNumberStringMap = {};
  selectedKeys.forEach((item) => {
    selectedKeyMap[item || ''] = true;
  });
  menuList.map((item) => {
    if (item.hasOwnProperty('children') && Array.isArray(item.children) && item.children.length > 0) {
      item.children.map((child) => {
        if (!selectedKeyMap.hasOwnProperty(child.id!)) {
          if (selectedKeyMap.hasOwnProperty(item.id!)) {
            delete selectedKeyMap[item.id!];
          }
        }
      })
    }
  });
  for (const key in selectedKeyMap) {
    result.push(Number(key)); 
  }
  return result;;
}