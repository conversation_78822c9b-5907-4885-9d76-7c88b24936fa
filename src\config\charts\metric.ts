const metricDefaultOptions = {
  dataType: 'static',
  data: {
    columns: ['指标名', '当前值', '单位'],
    values: [
      ['AI提效统计', 4841, '次']
    ]
  },
  config: {
    type: 'metric',
    title: {
      text: 'AI提效统计',
      show: true
    },
    valueSize: 60,
    valueColor: '#333333',
    titleColor: '#666666',
    titleSize: 24,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    valueFormatter: 'thousands'
  },
  dataMapping: {
    metric: {
      titleField: '指标名',
      valueField: '当前值',
      unitField: '单位'
    }
  }
};
export default metricDefaultOptions; 