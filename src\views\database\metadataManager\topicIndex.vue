<template>
  <a-page-header
      style="border: 1px solid rgb(235, 237, 240)"
      title="元数据管理"
      sub-title="主题管理"
      @back="() => router.back()"
  />
  <a-layout-content title="元数据管理">
    <a-card>
      <div class="table-operations">
        <a-button type="primary" @click="showCreateTopic">
          <template #icon>
            <plus-outlined/>
          </template>
          新增主题
        </a-button>
      </div>

      <a-table
          :columns="columns"
          :data-source="topicList"
          :loading="loading"
          row-key="id"
          :scroll="{ x: 900 }"
      >
        <template #action="{ record }">
          <a-row v-if="record.topic_name !== '默认主题'">
            <a-col :span="6">
              <a-button type="link" size="small" @click="handleEdit(record)">修改</a-button>
            </a-col>
            <a-col :span="12">
              <a-button type="link" danger size="small" @click="handleDelete(record)">删除</a-button>
            </a-col>
          </a-row>
          <a-row>
            <a-button type="link" size="small" @click="handleMetaData(record)">配置元数据</a-button>
          </a-row>
          <a-row>
            <a-button type="link" size="small" 
              @click="asyncMetaData(record)"
              :loading="syncLoading[record.id]">
              <span class="async-meta-percent"
                v-if="syncLoading[record.id] || syncPercent[record.id] === 100">{{ syncPercent[record.id] }}%</span>
              <span>
                {{ syncLoading[record.id] ? '同步中' + '.'.repeat(syncSteps) : 
                   syncPercent[record.id] === 100 ? '同步完成' :
                  '同步元数据' }}
                </span>
            </a-button>
          </a-row>
          <a-row>
            <a-button type="link" size="small" @click="handleShowRoleDialog(record)">分配角色</a-button>
          </a-row>
        </template>
      </a-table>
    </a-card>
  </a-layout-content>

  <!-- 统一操作模态框 -->
  <a-modal
      v-model:visible="modalVisible"
      :title="isEdit ? '修改主题' : '新增主题'"
      ok-text="确认"
      cancel-text="取消"
      @ok="handleSubmit"
      :confirm-loading="confirmLoading"
  >
    <a-form
        ref="formRef"
        :model="formState"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
        @finish="handleSubmit"
    >
      <a-form-item
          label="主题名称"
          name="topic_name"
          :rules="[{ required: true, message: '请输入主题名称' }]"
      >
        <a-input v-model:value="formState.topic_name" placeholder="请输入" />
      </a-form-item>

      <a-form-item
          label="主题描述"
          name="topic_desc"
          :rules="[{ required: true, message: '请输入主题描述' }]"
      >
        <a-textarea
            v-model:value="formState.topic_desc"
            :auto-size="{ minRows: 3 }"
            placeholder="请输入"
        />
      </a-form-item>
    </a-form>
  </a-modal>
  <TopicRole ref="topicRoleRef" :id="currTopic?.id || 0" :connectConfigId="currTopic?.connect_config_id || 0" />
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, nextTick } from 'vue'
import { message, Modal } from 'ant-design-vue'
import type { FormInstance } from 'ant-design-vue'
import {CodeOutlined, PlusOutlined} from '@ant-design/icons-vue'
import {useRoute, useRouter} from 'vue-router'
import TopicRole from './TopicRole.vue';
import {addTopic, deleteTopic, getTopicList, initMetadataToVector, updateTopic} from "@/api/metadata.ts";
import type {topicItem} from "@/types/metaData.ts";

interface RouteParams {
  datasourceId?: string | string[]
  database?: string
  db_type?: string;
  host?: string
}

// 路由和路由参数处理
const route = useRoute()
const router = useRouter()

const topicList = ref<topicItem[]>([]);
const syncLoading = ref<Record<number, boolean>>({});
const syncPercent = ref<Record<number, number>>({});
const syncTimer = ref();
const syncSteps = ref<number>(3);
const currTopic = ref<topicItem>();
const topicRoleRef = ref();

const columns = ref([
  {
    title: '数据库',
    dataIndex: 'datasource',
    width: 150, // 设置最大宽度
    ellipsis: true, // 文本溢出显示省略号
    customRender: () => `${routeParams.value.database}(${routeParams.value.host})`
  },
  {
    title: '主题',
    dataIndex: 'topic_name',
    key: 'topic_name',
    width: 150, // 设置最大宽度
    ellipsis: true // 文本溢出显示省略号
  },
  {
    title: '主题描述',
    dataIndex: 'topic_desc',
    key: 'topic_desc',
    width: 250, // 设置最大宽度
    ellipsis: true // 文本溢出显示省略号
  },
  {
    title: '操作',
    width: 180,
    fixed: 'right',
    key: 'action',
    slots: {customRender: 'action'}
  }
])

// 定义类型化查询参数
const routeParams = ref<{
  datasourceId: number
  database: string
  db_type: string;
  host: string
}>({
  datasourceId: 0,
  database: '',
  db_type: '',
  host: ''
})

const loading = ref(false)

// 表单相关
const formRef = ref<FormInstance>()
const formState = reactive<{ 
  topic_name: string | undefined,
  topic_desc: string | undefined,
}>({
  topic_name: '',
  topic_desc: ''
})

// 新增修改
const modalVisible = ref(false)
const isEdit = ref(false)
const currentId = ref<number | undefined>()
const confirmLoading = ref(false)

// 参数校验方法
const validateAndParseParams = (params: RouteParams) => {
  // 参数存在性检查
  if (!params.datasourceId || !params.database || !params.host) {
    message.error('缺少必要的查询参数')
    router.replace('/datasource') // 跳转到数据源管理页
    return false
  }
  // 处理数组参数（多值情况）
  const datasourceId = Array.isArray(params.datasourceId)
      ? params.datasourceId[0]
      : params.datasourceId
  // 数字类型转换校验
  if (isNaN(Number(datasourceId))) {
    message.error('无效的数据源ID格式')
    router.back()
    return false
  }
  // 赋值校验后的参数
  routeParams.value = {
    datasourceId: Number(datasourceId),
    database: params.database,
    db_type: params.db_type || '',
    host: params.host
  }
  return true
}

const fetchMetadata = async () => {
  if (!validateAndParseParams(route.query)) return
  try {
    // 参数校验
    const datasourceId = route.query.datasourceId
    if (!datasourceId) {
      message.error('缺少数据源ID参数')
      router.back()
      return
    }
    loading.value = true
    const res = await getTopicList(routeParams.value.datasourceId)
    console.log('getMetadataTopicList', res)
    // metadataList.value = mockData
    if (res.success) {
      if (res.data ){
        topicList.value = res.data
        res.data.map(item => {
          syncLoading.value[item?.id as number] = false; // 初始化同步状态
          if (!(syncPercent.value[item?.id as number] && syncPercent.value[item?.id as number] === 100)) {
            syncPercent.value[item?.id as number] = 0; // 初始化同步进度
          }
        })
      }
    } else {
      message.error(res.err_msg || '获取数据源列表失败');
    }
    // pagination.value.total = topicList.length
  } catch (error) {
    message.error('获取主题列表失败' + error);
  } finally {
    loading.value = false
  }
}

const showCreateTopic = () => {
  isEdit.value = false
  formRef.value?.resetFields()
  console.log(formState)
  formState.topic_name = undefined;
  formState.topic_desc = undefined;
  modalVisible.value = true
}

const handleSubmit = async () => {
  try {
    try {
      await formRef.value?.validate()
    } catch (error) {
      message.warning('请检查表单输入');
      return
    }
    confirmLoading.value = true

    const params : topicItem = {
      ...formState,
      ...(isEdit.value && { id: currentId.value }),
      ...(!isEdit.value && { connect_config_id: routeParams.value.datasourceId })
    }

    const apiFn = isEdit.value ? updateTopic : addTopic
    const res = await apiFn(params)

    if (res.success) {
      message.success(`${isEdit.value ? '修改' : '新增'}成功`)
      modalVisible.value = false
      formState.topic_name = '';
      formState.topic_desc = '';
      fetchMetadata()
    } else {
      message.error(res.err_msg || '操作失败')
    }
  } catch (error) {
    console.log(error)
    // message.error(`操作失败: ${error}`)
  } finally {
    confirmLoading.value = false
  }
}

// 修改
const handleEdit = (record: topicItem) => {
  isEdit.value = true
  currentId.value = record.id
  formState.topic_name = record.topic_name
  formState.topic_desc = record.topic_desc
  modalVisible.value = true
}

const handleDelete = (record: topicItem) => {
  Modal.confirm({
    title: '确认删除主题？',
    content: `确定要删除主题 [${record.topic_name}] 吗？删除后不可恢复！`,
    okText: '确认删除',
    cancelText: '取消',
    okType: 'danger',
    async onOk() {
      try {
        const ids = [record.id]
        if (ids) {
          const res = await deleteTopic({ids: ids})
          if (res.success) {
            message.success('删除成功')
            fetchMetadata() // 刷新列表
          } else {
            message.error(res.err_msg || '删除失败')
          }
        }
      } catch (error) {
        message.error('删除请求失败: ' + error)
      }
    }
  })
}

const handleMetaData = (record: topicItem) => {
  router.push({
    path: '/metadata',
    query: {
      datasourceId: routeParams.value.datasourceId, 
      database: routeParams.value.database,
      db_type: routeParams.value.db_type,
      topic: record.id, 
      name: record.topic_name,
    }
  })
}

const handleShowRoleDialog = async (record: topicItem) => {
  console.log(record); 
  currTopic.value = record;
  await nextTick();
  topicRoleRef.value?.handleOpen();
}

const asyncMetaData = async (record: topicItem) => {
  try {
    syncLoading.value[record?.id as number] = true;
    syncPercent.value[record?.id as number] = 0;
    const loadingTimer = setInterval(() => {
      // 生成 5 到 20 之间的随机数作为进度增加值
      const randomIncrement = Math.floor(Math.random() * 16) + 5; 
      syncPercent.value[record?.id as number] = Math.min(syncPercent.value[record?.id as number] + randomIncrement, 99);
      // 当进度达到 100% 时，清除定时器
    }, 300); // 每 300 毫秒更新一次进度
    const res = await initMetadataToVector(routeParams.value.datasourceId, record.id as string | number);
    clearInterval(loadingTimer);
    if (res.success) {
      message.success('同步向量库成功')
      fetchMetadata() // 刷新列表
      syncPercent.value[record?.id as number] = 100;
      setTimeout(() => {
        syncPercent.value[record?.id as number] = 0;
      }, 1000);
    } else {
      message.error(res.err_msg || '删除失败')
    }
  } catch (error) {
    message.error('删除请求失败: ' + error);
  } finally {
    syncLoading.value[record?.id as number] = false;
  }

}

onMounted(() => {
  if (!route.query.datasourceId) {
    message.warning('请从有效入口进入页面')
    router.push('/datasource')
    return
  }
  fetchMetadata()
  syncTimer.value = setInterval(() => {
    if (syncSteps.value === 3) {
      syncSteps.value = 1;
    } else {
      syncSteps.value++;
    }
  }, 300)
})
</script>

<style scoped>
.table-operations {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
}

.async-meta-percent {
  margin-right: 10px;
}
</style>