<template>
  <div v-if="isProlog && content" class="message bot-message">
    <img :src="profileOriginSrc" class="chat-avatar bot-avatar"/>
    <div class="message-content">
      <div v-html="content"/>
    </div>
  </div>
  <div v-if="!isProlog" class="message bot-message">
    <img :src="profileOriginSrc" class="chat-avatar bot-avatar"/>
    <div class="message-content">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import profileOriginSrc from '@/assets/images/profile-origin.jpg';

defineProps({
  isProlog: {
    type: Boolean,
    default: false
  },
  content: {
    type: String,
    default: ''
  }
})
</script>
