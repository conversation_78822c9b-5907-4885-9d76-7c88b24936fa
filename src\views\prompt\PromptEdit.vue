<template>
  <a-page-header
    style="border: 1px solid rgb(235, 237, 240)"
    :title="title"
    @back="handleBack"
  >
    <template #extra>
      <a-button key="1" type="primary" @click="handleSubmit">{{ submitText }}</a-button>
    </template>
  </a-page-header>
  <div class="prompt-edit-container">
    <div class="left">
      <a-textarea 
        class="prompt-edit-input"
        :style="{
          height: `calc(100vh - 64px - 40px - 74px - ${llmHeight}px)`,
          minHeight: `calc(100vh - 64px - 40px - 74px - 350px)`,
        }"
        v-model:value="promptValue" 
        placeholder="请输入提示词" 
        :rows="4" />
      <a-card title="LLM OUT" class="llm-out-card" v-if="history.length > 0" id="llm-out-container">
        <md-rendered-msg :answer="history?.[0]?.context.replace(/\\n/gm, '\n')" />
      </a-card>
    </div>
    <div class="right">
      <a-card :header="null" class="info-form-card">
        <a-form
          ref="formRef"
          :model="infoForm"
          :rules="infoRules"
        >
          <div class="info-line-1">
            <div class="info-line-item">
              <a-form-item label="类型" name="type">
                <a-select
                  v-model:value="infoForm.type"
                  style="width: 100%"
                  placeholder="请选择类型"
                  :options="typeOptions"
                  @change="handleTypeChange"
                ></a-select>
              </a-form-item>
            </div>
            <div class="info-line-item">
              <a-form-item class="scene-input" name="scene">
                <a-select
                  v-model:value="infoForm.scene"
                  style="width: 100%"
                  placeholder="请选择场景"
                  :options="sceneOptions"
                  @change="handleSceneChange"
                ></a-select>
              </a-form-item>
            </div>
          </div>
          <a-form-item label="编码" name="code" v-if="route.path === '/promptEdit'">
            <a-input v-model:value="promptCode" disabled />
          </a-form-item>
          <a-form-item label="名称" name="name">
            <a-input v-model:value="infoForm.name" placeholder="请输入prompt名称" />
          </a-form-item>
        </a-form>
      </a-card>
      <a-card title="输入参数" class="input-card">
        <a-form
          ref="midFormRef"
          class="mid-form"
          v-if="inputVariablesObj.length > 0"
        >
          <a-form-item :label="variableItem" :name="variableItem" v-for="variableItem in inputVariablesObj" :key="variableItem">
            <a-input v-model:value="midFormObj[variableItem]" placeholder="请输入" />
          </a-form-item>
        </a-form>
      </a-card>
      <a-card title="输出结构" class="output-card">
        <JsonViewer :value="responseTemplateJson" boxed sort theme="light"/>
        <a-form
          ref="outputFormRef"
          :model="outputForm"
          class="output-form"
        >
          <a-form-item label="模型" name="model">
            <a-select
              :dropdownMatchSelectWidth="false"
              v-model:value="outputForm.model"
              style="width: 100%"
            >
              <a-select-option v-for="modelItem in modelList" :key="modelItem" :value="modelItem">
                <div class="model-select-option" style="display: flex; align-items: center;">
                  <img :src="getModelIcon(modelItem)" style="width: 20px; height: 20px; margin-right: 4px;" />
                  <div>{{ modelItem }}</div>
                </div>
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="温度" name="temperature">
            <div style="display: flex; align-items: center;">
              <div style="padding: 0 10px; width: 200px;">
                <a-slider v-model:value="outputForm.temperature" :min="0" :max="1.0" :step="0.1" />
              </div>
              <a-input-number v-model:value="outputForm.temperature" :min="0" :max="1.0" :step="0.1" />
            </div>
          </a-form-item>
          <a-form-item label="语言" name="language">
            <a-select
              v-model:value="outputForm.language"
              placeholder="请选择语言"
              :options="languageOptions"
            ></a-select>
          </a-form-item>
          <a-form-item label="用户输入" name="user_input">
            <a-input v-model:value="outputForm.userInput" placeholder="请输入" />
          </a-form-item>
        </a-form>
        <div class="output-footer">
          <a-button type="primary" :loading="llmLoading" @click="onLLMTest">LLM测试</a-button>
          <a-button type="primary" @click="handleOutputVerify">输出验证</a-button>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted, watch } from 'vue';
  import type { UnwrapRef } from 'vue';
  import { message } from 'ant-design-vue';
  import type { FormInstance } from 'ant-design-vue';
  import type { Rule } from 'ant-design-vue/es/form';
  import { useRoute, useRouter } from 'vue-router';
  import { EventStreamContentType, fetchEventSource } from '@microsoft/fetch-event-source';
  import type { AddPromptDTO, DebugParams } from '@/types/prompt';
  import MdRenderedMsg from "@/components/md-rendered-msg.vue";
  // @ts-ignore
  import { JsonViewer } from "vue3-json-viewer";
  import type { Option } from '@/types/app';
  import "vue3-json-viewer/dist/index.css";
  import { getModelIcon } from '@/utils';
  import { typeOptions } from './config';
  import * as apiChat from '@/api/chat';
  import * as api from '@/api/prompt';

  const route = useRoute();
  const router = useRouter();

  const promptValue = ref<string>('');
  const inputVariables = ref<string>('');
  const responseTemplate = ref<string>('');
  const modelList = ref<string[]>([]);
  const llmLoading = ref<boolean>(false);
  const llmHeight = ref<number>(0);
  const verifyLoading = ref<boolean>(false);

  const title = computed(() => {
    return route.path === '/promptEdit' ? '编辑提示词' : '新增提示词';
  });

  const submitText  = computed(() => {
    return route.path === '/promptEdit' ? '更新' : '保存';
  });

  const responseTemplateJson = computed(() => {
    try {
      return JSON.parse(responseTemplate.value) ?? {};
    } catch (_error) {
      return {};
    }
  });

  const inputVariablesObj = computed(() => {
    try {
      let arr = JSON.parse(inputVariables.value);
      if (Array.isArray(arr)) {
        return arr.filter(item => item !== 'out_schema');
      }
      return [];
    } catch (_error) {
      return [];
    }
  });

  interface InfoForm {
    type: string | null;
    scene: string | null;
    name: string | null;
  }

  const infoForm: UnwrapRef<InfoForm> = reactive({
    type: null,
    scene: null,
    name: null,
  });

  const infoRules: Record<string, Rule[]> = {
    type: [{ required: true, message: '请选择类型', trigger: 'blur' }],
    scene: [{ required: true, message: '请选择场景', trigger: 'blur' }],
    name: [{ required: true, message: '请输入prompt名称', trigger: 'blur' }],
  };

  
  interface OutputForm {
    model: string | null;
    temperature: number | null;
    language: string | null;
    userInput: string | null;
  }

  const outputForm: UnwrapRef<OutputForm> = reactive({
    model: null,
    temperature: 0.5,
    language: 'en',
    userInput: null,
  });

  const languageOptions = [
    { label: '英文', value: 'en' },
    { label: '中文', value: 'zh' },
  ];

  const sceneOptions = ref<Option[]>();
  const formRef = ref<FormInstance>();
  const midFormRef = ref<FormInstance>();
  const outputFormRef = ref<FormInstance>();
  const promptCode = ref<string>();
  const midFormObj = ref<any>({});
  const history = ref<Record<string, any>[]>([]);

  const handleBack = () => {
    router.back();
  };

  const handleTypeChange = async () => {
    infoForm.scene = '';
    let res = await api.getScenes(infoForm.type || '');
    sceneOptions.value = [];
    res.data.forEach((item) => {
      sceneOptions.value?.push({
        label: item.name,
        value: item.name,
      }) 
    })
  }

  const handleSceneChange = async () => {
    let res = await api.getPromptTemplate(infoForm.type || '', infoForm.scene || '');
    promptValue.value = res.data.template;
    inputVariables.value = JSON.stringify(res.data.input_variables);
    responseTemplate.value = JSON.stringify(res.data.response_format);
  }

  const handleSubmit = async () => {
    if (!formRef.value) return;
    try {
      // 执行表单校验
      await formRef.value.validate();
    } catch (_error) {
      message.error('表单校验失败，请检查输入内容');
      return;
    }
    // 校验成功，提交数据
    const isEdit = route.path === '/promptEdit';
    const data: AddPromptDTO = {
      chat_scene: infoForm.scene || '',
      content: promptValue.value,
      input_variables: inputVariables.value,
      model: outputForm.model || '',
      prompt_desc: '',
      prompt_language: outputForm.language || '',
      prompt_name: infoForm.name || '',
      prompt_type: infoForm.type || '',
      response_schema: responseTemplate.value,
      sub_chat_scene: '',
      // todo: 后续需要从登录信息中获取
      user_name: 'dbgpt',
    };
    if (isEdit) {
      data.prompt_code = promptCode.value;
      await api.updatePrompt(data);
    } else {
      await api.createPrompt(data);
    }
    message.success(isEdit ? '提示词编辑成功' : '提示词新增成功');
    router.back();
  }

  const handleGetModels = async () => {
    let res = await apiChat.getModels();
    modelList.value = res.data as string[];
    outputForm.model = (res.data as any)[0];
  }

  const onLLMTest = async () => {
    if (llmLoading.value) {
      return;
    }
    if (!Object.values(midFormObj.value).every(value => !!value)) {
      message.warning('请填写完整的输入参数');
      return;
    }
    if (!outputForm.userInput) {
      message.warning('请填写用户输入内容');
      return;
    }
    if (!formRef.value) return;
    try {
      // 执行表单校验
      await formRef.value.validate();
    } catch (_error) {
      message.error('表单校验失败，请检查输入内容');
      return;
    }
    const params: DebugParams = {
      sub_chat_scene: '',
      model: outputForm.model || '',
      chat_scene: infoForm.scene || '',
      prompt_name: infoForm.name || '',
      prompt_type: infoForm.type || '',
      content: promptValue.value,
      response_schema: responseTemplate.value,
      input_variables: inputVariables.value,
      prompt_language: outputForm.language as any,
      prompt_desc: '',
      prompt_code: promptCode.value || '',
      temperature: outputForm.temperature || 0.5,
      debug_model: outputForm.model || '',
      input_values: {
        ...midFormObj.value,
      },
      user_input: outputForm.userInput,
    };
    const tempHistory: Record<string, any>[] = [{ role: 'view', context: '' }];
    const index = tempHistory.length - 1;
    try {
      llmLoading.value = true;

      const apiUrl = import.meta.env.MODE === 'production' 
        ? `${import.meta.env.VITE_ROOT_API_BASE_URL}/prompt/template/debug`
        : '/root-api/prompt/template/debug';

      await fetchEventSource('/root-api/prompt/template/debug', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // [HEADER_USER_ID_KEY]: getUserId() ?? '',
        },
        body: JSON.stringify(params),
        openWhenHidden: true,
        async onopen(response) {
          if (response.ok && response.headers.get('content-type') === EventStreamContentType) {
            return;
          }
        },
        onclose() {
          llmLoading.value = false;
        },
        onerror(err) {
          throw new Error(err);
        },
        onmessage: event => {
          let message = event.data;
          if (!message) return;
          try {
            message = JSON.parse(message).vis;
          } catch {
            (message as any).replaceAll('\\n', '\n');
          }
          if (message === '[DONE]') {
            llmLoading.value = false;
          } else if (message?.startsWith('[ERROR]')) {
            llmLoading.value = false;
            tempHistory[index].context = message?.replace('[ERROR]', '');
          } else {
            tempHistory[index].context = message;
            history.value = [...tempHistory];
          }
          llmHeight.value = document.getElementById('llm-out-container')?.clientHeight || 0;
        },
      });
    } catch {
      llmLoading.value = false;
      tempHistory[index].context = 'Sorry, we meet some error, please try again later';
      history.value = [...tempHistory];
    }
  }

  const handleOutputVerify = async () => {
    if (verifyLoading.value) {
      return;
    }
    if (history.value.length === 0 || (history.value.length > 0 && !history.value[0]?.context)) {
      message.warning('请先进行LLM测试');
      return;
    }
    await run();
  }

  const run = async () => {
    if (verifyLoading.value) {
      return;
    }
    try {
      verifyLoading.value = true;
      let res = await api.verifyPrompt({
        llm_out: history.value[0].context,
        prompt_type: infoForm.type || '',
        chat_scene: infoForm.scene || '',
      });
      if (res.data) {
        message.success('验证通过');
      }
    } catch (error) {
      console.error(error)
    } finally {
      verifyLoading.value = false;
    }
  }

  watch(
    inputVariablesObj, 
    (newVal) => {
      midFormObj.value = {};
      newVal.forEach((item) => {
        midFormObj.value[item] = '';
      })
    },
    {
      deep: true,
      immediate: true,
    }
  );

  onMounted(async () => {
    await handleGetModels();
    if (route.path === '/promptEdit') {
      let currEditPrompt = localStorage.getItem('editPrompt');
      if (currEditPrompt) {
        let currEditPromptObj = JSON.parse(currEditPrompt);
        // console.log(currEditPromptObj);
        promptCode.value = currEditPromptObj.prompt_code;
        outputForm.model = currEditPromptObj.model;
        outputForm.language = currEditPromptObj.prompt_language;
        infoForm.name = currEditPromptObj.prompt_name;
        promptValue.value = currEditPromptObj.content;
        infoForm.type = currEditPromptObj.prompt_type;
        await handleTypeChange();
        infoForm.scene = currEditPromptObj.chat_scene;
        await handleSceneChange();
      }
    }
  });
</script>

<style scoped lang="scss">
  .prompt-edit-container {
    display: flex;
    .left {
      width: calc(100% - 400px - 20px);
      .prompt-edit-input {
        height: calc(100vh - 64px - 40px - 74px);
      }
      .llm-out-card {
        text-align: left;
        margin-top: 10px;
        max-height: 350px;
        overflow-y: auto;
      }
    }
    .right {
      margin-left: 20px;
      width: 400px;
      height: calc(100vh - 64px - 40px - 74px);
      overflow-y: auto;
      .info-form-card {
        width: 100%;
        text-align: left;
        .info-line-1 {
          display: flex;
          .info-line-item {
            width: 50%;
          }
          .scene-input {
            margin-left: 10px;
          }
        }
      }
      .input-card {
        margin-top: 10px;
        text-align: left;
        .mid-form {
          margin-top: 10px;
        }
      }
      .output-card {
        margin-top: 10px;
        text-align: left;
        .output-form {
          margin-top: 20px;
        }
        .output-footer {
          margin-top: 20px;
          display: flex;
          justify-content: space-between;
        }
      }
    }
  }
</style> 