import ColorfulData from '@/components/icons/colorful-data.vue'
import ColorfulDB from '@/components/icons/colorful-db.vue'
import ColorfulDashboard from '@/components/icons/colorful-dashboard.vue'
import ColorfulChat from '@/components/icons/colorful-chat.vue'

export const appList = [
  {
    key: 'chatDashboard',
    title: 'Chat Dashboard',
    subtitle: 'Provide you with professional data analysis reports through natural language',
    prolog: '通过自然语言为您提供专业的数据分析报告',
    icon: ColorfulDashboard,
    path: '/chatDashboard',
    appCode: 'chat_dashboard',
    mode: 'chat_dashboard',
  },
  {
    key: 'chatData',
    // title: 'Chat Data',
    // subtitle: 'Have a conversation with your private data through natural language',
    title: '数据分析助手',
    subtitle: '通过自然语言与数据进行对话',
    prolog: '通过自然语言与你的私人数据进行对话',
    icon: ColorfulData,
    path: '/chatData',
    // appCode: 'chat_with_db_execute',
    appCode: '0e221f70-3131-11f0-8513-9f8972b3f61a',
    mode: 'chat_with_db_execute',
  },
  {
    key: 'chatDb',
    title: 'Chat DB',
    subtitle: 'Database Metadata Q&A',
    prolog: '数据库元数据问答',
    icon: ColorfulDB,
    path: '/chatDb',
    appCode: 'chat_with_db_qa',
    mode: 'chat_with_db_qa',
  },
  {
    key: 'chatNormal',
    title: 'Chat Normal',
    subtitle: 'Native LLM dialogue',
    prolog: '原生大语言模型对话',
    icon: ColorfulChat,
    path: '/chatNormal',
    appCode: 'chat_normal',
    mode: 'chat_normal',
  },
]
