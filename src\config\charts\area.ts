import legendOptions from '../e-charts/legend';
import tooltipOptions from '../e-charts/tooltip';

const areaDefaultOptions = {
  dataType: 'static',
  data: {
    columns: ['日期', '数值'],
    values: [
      ['周一', 820],
      ['周二', 932],
      ['周三', 901],
      ['周四', 934],
      ['周五', 1290],
      ['周六', 1330],
      ['周日', 1320]
    ]
  },
  config: {
    type: 'area',
    tooltip: tooltipOptions,
    legend: legendOptions,
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: []
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        type: 'line',
        areaStyle: {},
        data: []
      }
    ]
  },
  dataMapping: {
    xField: '日期',
    yFields: ['数值'],
    seriesNames: ['数值']
  }
};
export default areaDefaultOptions; 