<template>
  <common-chart-settings
    :options="props.options"
    :chart-type="chartType"
    @update="updateOptions"
  >
    <!-- 样式配置 -->
    <template #style-settings>
      <!-- 通用提示 -->
      <a-alert
        type="info"
        show-icon
        :message="`${chartTypeLabel}高级设置`"
        :description="`此图表类型的特定设置正在开发中，您可以通过JSON编辑器直接修改配置。`"
        style="margin-bottom: 16px;"
      />
      
      <!-- JSON编辑器 -->
      <a-form-item label="JSON配置">
        <a-textarea
          v-model:value="jsonConfig"
          :auto-size="{ minRows: 10, maxRows: 20 }"
          placeholder="请输入JSON配置"
          @blur="updateFromJson"
        />
      </a-form-item>
      
      <a-form-item v-if="jsonError">
        <a-alert
          type="error"
          show-icon
          message="JSON格式错误"
          :description="jsonError"
        />
      </a-form-item>

      <!-- 颜色与背景 -->
      <a-divider orientation="left">颜色与背景</a-divider>
      <a-form-item label="背景颜色">
        <a-input 
          v-model:value="chartConfig.backgroundColor" 
          placeholder="例如: #ffffff" 
          :addon-before="'颜色'"
          @change="updateConfig"
        />
      </a-form-item>

      <!-- 动画设置 -->
      <a-divider orientation="left">动画设置</a-divider>
      <a-form-item label="开启动画">
        <a-switch v-model:checked="chartConfig.animation" @change="updateConfig" />
      </a-form-item>
      <a-form-item v-if="chartConfig.animation" label="动画时长">
        <a-input-number 
          v-model:value="chartConfig.animationDuration" 
          :min="100" 
          :max="5000"
          :step="100"
          @change="updateConfig" 
        />
      </a-form-item>

      <!-- 提示框设置 -->
      <a-divider orientation="left">提示框设置</a-divider>
      <a-form-item label="显示提示框">
        <a-switch v-model:checked="tooltipShow" @change="updateTooltip" />
      </a-form-item>
      <a-form-item v-if="tooltipShow" label="提示框触发">
        <a-select v-model:value="tooltipTrigger" @change="updateTooltip">
          <a-select-option value="item">数据项</a-select-option>
          <a-select-option value="axis">坐标轴</a-select-option>
          <a-select-option value="none">无</a-select-option>
        </a-select>
      </a-form-item>
    </template>
  </common-chart-settings>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue';
import CommonChartSettings from './CommonChartSettings.vue';
import type { ChartOptions } from '@/types/chart';

const props = defineProps({
  options: {
    type: Object,
    required: true
  },
  chartType: {
    type: String,
    default: 'default'
  },
  chartId: {
    type: Number,
  }
});

const emit = defineEmits(['update']);

// JSON编辑器的值
const jsonConfig = ref('');
const jsonError = ref('');

// 提示框设置
const tooltipShow = ref(true);
const tooltipTrigger = ref('item');

// 根据图表类型显示不同的标签
const chartTypeLabel = computed(() => {
  const typeMap: Record<string, string> = {
    bar: '柱状图',
    line: '折线图',
    pie: '饼图',
    scatter: '散点图',
    radar: '雷达图',
    funnel: '漏斗图',
    gauge: '仪表盘',
    heatmap: '热力图',
    treemap: '树图',
    wordCloud: '词云图',
    default: '图表'
  };
  
  return typeMap[props.chartType] || '图表';
});

// 直接访问config部分的计算属性
const chartConfig = computed({
  get: () => {
    return props.options.config || getDefaultOptions().config;
  },
  set: (newConfig) => {
    updateConfig(newConfig);
  }
});

// 获取默认配置
const getDefaultOptions = () => {
  return {
    config: {
      type: props.chartType,
      title: {
        text: '',
        show: false
      },
      backgroundColor: '#ffffff',
      animation: true,
      animationDuration: 1000,
      tooltip: {
        show: true,
        trigger: 'item'
      }
    }
  };
};

// 更新配置
const updateOptions = (newOptions: ChartOptions) => {
  emit('update', newOptions);
};

// 更新config部分
const updateConfig = (newConfig: any) => {
  const updatedOptions = {
    ...props.options,
    config: newConfig
  };
  emit('update', updatedOptions);
};

// 更新JSON显示
const updateJson = () => {
  try {
    jsonConfig.value = JSON.stringify(chartConfig.value, null, 2);
    jsonError.value = '';
  } catch (error) {
    jsonError.value = '无法将配置转换为JSON';
  }
};

// 从JSON更新配置
const updateFromJson = () => {
  try {
    const newConfig = JSON.parse(jsonConfig.value);
    updateConfig(newConfig);
    jsonError.value = '';
  } catch (error) {
    jsonError.value = '无效的JSON格式: ' + (error as Error).message;
  }
};

// 提示框相关
const updateTooltip = () => {
  chartConfig.value.tooltip = {
    ...(chartConfig.value.tooltip || {}),
    show: tooltipShow.value,
    trigger: tooltipTrigger.value
  };
  updateConfig(chartConfig.value);
};

// 初始化
onMounted(() => {
  // 更新JSON编辑器内容
  updateJson();
  // 初始化提示框
  if (chartConfig.value.tooltip) {
    tooltipShow.value = chartConfig.value.tooltip.show !== false;
    tooltipTrigger.value = chartConfig.value.tooltip.trigger || 'item';
  }
});

// 监听chartConfig变化更新JSON编辑器
watch(() => chartConfig.value, () => {
  updateJson();
}, { deep: true });
</script>

<style scoped>
/* 无需额外样式 */
</style> 