<template>
  <BaseChart 
    :chart-id="chartId" 
    :dashboard-id="dashboardId"
    :show-title="showTitle"
    @data-loaded="onDataLoaded"
    @error="onError"
  >
    <template #default="slotProps">
      <div class="chart-wrapper" v-if="showChat">
        <v-chart :option="processedOptions" autoresize />
      </div>
    </template>
  </BaseChart>
</template>

<script setup lang="ts">
import { ref, markRaw } from 'vue';
import VChart from 'vue-echarts';
import BaseChart from './BaseChart.vue';

// 定义props
const props = defineProps({
  chartId: {
    type: Number,
    required: true
  },
  dashboardId: {
    type: Number,
    required: true
  },
  showTitle: {
    type: Boolean,
    default: true
  }
});

// 处理后的图表ECharts配置
const processedOptions = ref<any>({});
const showChat = ref<boolean>(false);

/**
 * 处理水平柱状图数据
 */
const processHorizontalBarData = (rawData: any): any => {
  const { config = {}, data, dataMapping } = rawData;
  console.log('rawData', rawData);
  
  // 如果没有数据，返回基础配置
  if (!data?.columns || !data?.values || data.values.length === 0) {
    return false;
  }

  const { columns, values } = data;
  
  // 使用映射逻辑或默认逻辑
  let categoryField = 0; // 默认第一列为类别数据，对应y轴
  let valueField = 1; // 默认第二列为值数据，对应x轴
  let seriesName = '数量'; // 默认系列名称
  
  // 如果提供了映射配置，使用映射配置
  if (dataMapping) {
    // 查找yField对应的列索引（类别字段，对应y轴）
    if (dataMapping.yField) {
      const categoryFieldIndex = columns.findIndex((col: string) => col === dataMapping.yField);
      if (categoryFieldIndex !== -1) {
        categoryField = categoryFieldIndex;
      }
    }

    if (dataMapping.yFields && dataMapping.yFields.length > 0) {
      const categoryFieldIndex = columns.findIndex((col: string) => col === dataMapping.yFields[0]);
      if (categoryFieldIndex !== -1) {
        categoryField = categoryFieldIndex;
      }
    }
    
    // 查找xField对应的列索引（值字段，对应x轴）
    // if (dataMapping.xField) {
    //   const valueFieldIndex = columns.findIndex((col: string) => col === dataMapping.xField);
    //   if (valueFieldIndex !== -1) {
    //     valueField = valueFieldIndex;
    //   }
    // }

    if (dataMapping.yFields && dataMapping.yFields.length > 0) {
      const categoryFieldIndex = columns.findIndex((col: string) => col === dataMapping.yFields[0]);
      if (categoryFieldIndex !== -1) {
        valueField = categoryFieldIndex;
      }
    }
    
    // 使用提供的系列名称
    if (dataMapping.seriesNames && Array.isArray(dataMapping.seriesNames) && dataMapping.seriesNames.length > 0) {
      seriesName = dataMapping.seriesNames[0];
    }
  }
  
  // 获取类别数据（对应y轴）并确保转换为字符串
  const categoryData = values.map((row: any[]) => String(row[categoryField]));
  
  // 获取值数据（对应x轴）并确保数值类型转换
  const valueData = values.map((row: any[]) => {
    const value = Number(row[valueField]);
    return isNaN(value) ? 0 : value;
  });
  
  // console.log(values);
  // 构建返回配置
  return {
    ...config,
    title: {
      show: false
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: categoryData,
      axisLabel: {
        show: true,
        interval: 0
      }
    },
    series: [{
      name: seriesName,
      type: 'bar',
      data: valueData
    }]
  };
};

// 数据加载完成
const onDataLoaded = (rawData: any) => {
  try {
    // 直接处理原始数据
    const processedConfig = processHorizontalBarData(rawData);
    console.log('processedConfig', processedConfig);
    if (!processedConfig) {
      showChat.value = false;
      return;
    }

    // 使用markRaw避免Vue对复杂对象进行递归响应式处理
    processedOptions.value = markRaw(processedConfig);
    showChat.value = true;
  } catch (error) {
    console.error('处理水平柱状图数据出错', error);
    processedOptions.value = {};
    showChat.value = false;
  }
};

// 数据加载错误
const onError = (error: string) => {
  console.error('图表数据加载错误', error);
};
</script>

<style scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;
}
</style> 