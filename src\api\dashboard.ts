import request from '../utils/request'
import type { ApiResponse } from '../types/api';
import type { DashboardGroup, Dashboard } from '../types/dashboard';
import type { Chart } from '../types/chart';

// 获取看板分组
export function getDashboardGroups(): Promise<ApiResponse<DashboardGroup[]>> {
  return request.get('/v2/serve/datasources/dashboardGroup/list');
}

// 创建看板分组
export function createDashboardGroup(data: { title: string }): Promise<ApiResponse<DashboardGroup>> {
  return request.post('/v2/serve/datasources/dashboardGroup/add', data);
}

// 更新看板分组
export const updateDashboardGroup = (data: { id: number; title: string }): Promise<ApiResponse<DashboardGroup>> => {
  return request.put(`/v2/serve/datasources/dashboardGroup/update/${data.id}`, {
    title: data.title
  });
};

// 删除看板分组
export const deleteDashboardGroup = (id: number): Promise<ApiResponse<null>> => {
  return request.delete(`/v2/serve/datasources/dashboardGroup/delete/${id}`);
};

// 获取看板列表
export function getDashboardList(groupId: number): Promise<ApiResponse<Dashboard[]>> {
  return request.get(`/v2/serve/datasources/dashboard/list/${groupId}`);
}

// 获取看板详情
export function getDashboardDetail(id: number): Promise<ApiResponse<Dashboard>> {
  return request.get(`/v2/serve/datasources/dashboard/detail/${id}`);
}

// 创建看板
export function createDashboard(data: { title: string, dashboard_group_id: number, description?: string }): Promise<ApiResponse<Dashboard>> {
  return request.post('/v2/serve/datasources/dashboard/add', {
    'dashboard_group_id': data.dashboard_group_id,
    'title': data.title,
    'description': data.description
  });
}

// 保存看板
export function saveDashboard(data: { 
  id: number, 
  title?: string, 
  description?: string,
  charts?: Chart[]
}): Promise<ApiResponse<null>> {
  return request.put(`/v2/serve/datasources/dashboard/update/${data.id}`, {
    'title': data.title,
    'description': data.description,
    'charts': data.charts
  }, { timeout: 300000 });
}

// 删除看板
export const deleteDashboard = (id: number): Promise<ApiResponse<null>> => {
  return request.delete(`/v2/serve/datasources/dashboard/delete/${id}`);
}; 
