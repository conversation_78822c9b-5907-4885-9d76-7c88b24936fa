<template>
  <common-chart-settings
    :options="props.options"
    chart-type="area"
    @update="updateOptions"
  >
    <!-- 样式配置 -->
    <template #style-settings>
      <!-- 坐标轴设置 -->
      <a-collapse-panel key="axis" header="坐标轴设置">
        <a-form-item label="X轴名称">
          <a-input v-model:value="chartConfig.xAxis.name" placeholder="请输入X轴名称" />
        </a-form-item>
        
        <a-form-item label="Y轴名称">
          <a-input v-model:value="chartConfig.yAxis.name" placeholder="请输入Y轴名称" />
        </a-form-item>
        
        <a-form-item label="显示网格线">
          <a-switch v-model:checked="chartConfig.grid.show" />
        </a-form-item>
      </a-collapse-panel>
      
      <!-- 面积图样式设置 -->
      <a-collapse-panel key="area" header="面积图样式">
        <a-form-item label="平滑曲线">
          <a-switch 
            v-model:checked="smooth" 
            @change="updateSmooth" 
          />
        </a-form-item>
        
        <a-form-item label="面积透明度">
          <a-slider 
            v-model:value="areaOpacity" 
            :min="0" 
            :max="1" 
            :step="0.1"
            @change="updateAreaOpacity" 
          />
        </a-form-item>
        
        <a-form-item label="显示数据点">
          <a-switch 
            v-model:checked="showSymbol" 
            @change="updateShowSymbol" 
          />
        </a-form-item>
        
        <a-form-item label="数据点大小" v-if="showSymbol">
          <a-input-number 
            v-model:value="symbolSize" 
            :min="2" 
            :max="20"
            @change="updateSymbolSize" 
          />
        </a-form-item>
      </a-collapse-panel>
    </template>
  </common-chart-settings>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import CommonChartSettings from './CommonChartSettings.vue';
import type { ChartOptions } from '../../types/chart';

const props = defineProps({
  options: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update']);

// 面积图样式设置
const smooth = ref(false);
const areaOpacity = ref(0.3);
const showSymbol = ref(true);
const symbolSize = ref(6);

// 默认面积图配置
const defaultAreaConfig = {
  type: 'area',
  title: { text: '', show: false },
  tooltip: {},
  legend: { show: true, orient: 'horizontal', left: 'right' },
  grid: { show: false, left: '3%', right: '4%', bottom: '3%', containLabel: true },
  xAxis: { name: '', position: 'bottom', data: [] },
  yAxis: { name: '', type: 'value' },
  series: [{
    name: '系列1',
    type: 'line',
    data: [],
    smooth: false,
    showSymbol: true,
    symbolSize: 6,
    areaStyle: {
      opacity: 0.3
    },
    lineStyle: {
      color: '#1890ff',
      width: 2,
      type: 'solid'
    }
  }]
};

// 直接访问config部分的计算属性
const chartConfig = computed({
  get: () => {
    return props.options.config || defaultAreaConfig;
  },
  set: (newConfig) => {
    const updatedOptions = {
      ...props.options,
      config: newConfig
    };
    emit('update', updatedOptions);
  }
});

// 更新配置
const updateOptions = (newOptions: ChartOptions) => {
  emit('update', newOptions);
};

// 初始化组件，同步样式控制变量
onMounted(() => {
  // 初始化样式控制变量
  if (chartConfig.value.series?.[0]) {
    smooth.value = chartConfig.value.series[0].smooth || false;
    showSymbol.value = chartConfig.value.series[0].showSymbol !== false;
    symbolSize.value = chartConfig.value.series[0].symbolSize || 6;
    
    if (chartConfig.value.series[0].areaStyle) {
      areaOpacity.value = chartConfig.value.series[0].areaStyle.opacity !== undefined 
        ? chartConfig.value.series[0].areaStyle.opacity 
        : 0.3;
    }
  }
});

// 监听props变化，更新本地控制变量
watch(() => props.options, (newOptions) => {
  if (newOptions?.config?.series?.[0]) {
    smooth.value = newOptions.config.series[0].smooth || false;
    showSymbol.value = newOptions.config.series[0].showSymbol !== false;
    symbolSize.value = newOptions.config.series[0].symbolSize || 6;
    
    if (newOptions.config.series[0].areaStyle) {
      areaOpacity.value = newOptions.config.series[0].areaStyle.opacity !== undefined 
        ? newOptions.config.series[0].areaStyle.opacity 
        : 0.3;
    }
  }
}, { deep: true });

// 更新平滑曲线
const updateSmooth = () => {
  const config = { ...chartConfig.value };
  config.series.forEach((series: any) => {
    series.smooth = smooth.value;
  });
  chartConfig.value = config;
};

// 更新面积透明度
const updateAreaOpacity = () => {
  const config = { ...chartConfig.value };
  config.series.forEach((series: any) => {
    if (!series.areaStyle) {
      series.areaStyle = { opacity: areaOpacity.value };
    } else {
      series.areaStyle.opacity = areaOpacity.value;
    }
  });
  chartConfig.value = config;
};

// 更新显示数据点
const updateShowSymbol = () => {
  const config = { ...chartConfig.value };
  config.series.forEach((series: any) => {
    series.showSymbol = showSymbol.value;
  });
  chartConfig.value = config;
};

// 更新数据点大小
const updateSymbolSize = () => {
  const config = { ...chartConfig.value };
  config.series.forEach((series: any) => {
    series.symbolSize = symbolSize.value;
  });
  chartConfig.value = config;
};
</script>

<style scoped>
.chart-settings {
  padding: 10px;
}
</style> 