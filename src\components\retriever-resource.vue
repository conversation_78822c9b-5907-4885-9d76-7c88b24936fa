<template>
  <div v-if="message.retrieverResource != null && message.retrieverResource.length > 0">
    <van-divider content-position="left" dashed style="padding:8px 0 0 0;margin:0;border-color: #333333">
      引用
    </van-divider>
    <!-- <template v-if="getAppInfo() !== undefined && getAppInfo().appType == AppType.DB_ASSISTANT">
      <van-button plain size="small" @click="itemClick(message.retrieverResource[0])">数据库
      </van-button>
    </template> -->
    <template>
      <div v-for="doc in uniqueByProperty<RetrieverResourceVO, 'docName'>(message.retrieverResource, 'docName')" @click="itemClick(doc)">
        <el-link class="ref-block">{{ doc.docName }}</el-link>
      </div>
    </template>
    <!-- <el-dialog
      v-model="showPop"
      :title="displayVo?.docName"
      :width="'50%'"
    >
      <div class="doc" v-for="segment in getSegmentByDocName(message.retrieverResource,displayVo?.docName)">
        <div class="doc-tags" v-if="getAppInfo().appType !== AppType.DB_ASSISTANT">
          <el-tag plain type="primary">#{{ segment.sort }}</el-tag>
          <el-tag plain type="primary">相关度:{{ segment.score }}</el-tag>
        </div>
        <el-text>{{ segment?.content }}</el-text>
      </div>
    </el-dialog> -->
  </div>
</template>

<script setup lang="ts">
import {ref} from 'vue'
import type { AppVo, RetrieverResourceVO } from "@/types/app";
// import { getAppInfoByRoute } from '@/utils';

enum AppType {
  /** 对话型应用 */
  TALK = 'talk',
  /** 文本生产型应用 */
  TEXT_GEN = 'text_generation',
  DB_ASSISTANT = 'db_assistant',
}

const props = defineProps({
  message: {
    required: true,
    type: Object
  }
})

const showPop = ref(false)
const displayVo = ref<RetrieverResourceVO>()

// const getAppInfo = () => getAppInfoByRoute();

const itemClick = function (item: RetrieverResourceVO) {
  displayVo.value = item
  showPop.value = true
}

function getSegmentByDocName(list: RetrieverResourceVO[], docName?: string) {
  return list.filter((item) => {
    return item.docName === docName
  })
}

// 去重
function uniqueByProperty<T, K extends keyof T>(array: T[], key: K): T[] {
  const map = new Map<T[K], T>();
  for (const item of array) {
    map.set(item[key], item);
  }
  return Array.from(map.values());
}

function autoWidth() {
  return document.body.clientWidth < 1024 ? '100%' : '50%'
}
</script>
<style scoped lang="scss">
.ref-block {
  color: #999999;
  font-weight: 400;
  margin-bottom: 6px;
}

.doc {
  border-top: 1px solid #eeeeee;
  padding: 10px 0;
  &-tags {
    margin-bottom: 10px;
    .el-tag {
      margin-right: 6px;
    }
  }
}

</style>