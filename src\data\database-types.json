{"success": true, "err_code": null, "err_msg": null, "data": {"types": [{"name": "tugraph", "label": "TuGraph datasource", "description": "TuGraph is a high-performance graph database jointly developed by Ant Group and Tsinghua University.", "parameters": [{"required": true, "is_array": false, "param_name": "host", "param_class": "dbgpt_ext.datasource.conn_tugraph.TuGraphParameters", "param_type": "string", "label": "host", "description": "TuGraph 服务器主机", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 0}, {"required": true, "is_array": false, "param_name": "user", "param_class": "dbgpt_ext.datasource.conn_tugraph.TuGraphParameters", "param_type": "string", "label": "user", "description": "TuGraph 服务器用户", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 1}, {"required": false, "is_array": false, "param_name": "password", "param_class": "dbgpt_ext.datasource.conn_tugraph.TuGraphParameters", "param_type": "string", "label": "password", "description": "数据库密码，您可以直接输入密码，当然也可以使用环境变量，例如 ${env:DBGPT_DB_PASSWORD}。", "default_value": "${env:DBGPT_DB_PASSWORD}", "valid_values": null, "ext_metadata": {"tags": "privacy"}, "nested_fields": null, "param_order": 2}, {"required": false, "is_array": false, "param_name": "port", "param_class": "dbgpt_ext.datasource.conn_tugraph.TuGraphParameters", "param_type": "integer", "label": "port", "description": "TuGraph 服务器端口，默认为 7687", "default_value": 7687, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 3}, {"required": false, "is_array": false, "param_name": "database", "param_class": "dbgpt_ext.datasource.conn_tugraph.TuGraphParameters", "param_type": "string", "label": "database", "description": "数据库名称，默认为 'default'", "default_value": "default", "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 4}]}, {"name": "spark", "label": "Apache Spark datasource", "description": "Unified engine for large-scale data analytics.", "parameters": [{"required": true, "is_array": false, "param_name": "path", "param_class": "dbgpt_ext.datasource.conn_spark.SparkParameters", "param_type": "string", "label": "path", "description": "数据源的文件路径。", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 0}]}, {"name": "sqlite", "label": "SQLite datasource", "description": "Lightweight embedded relational database with simplicity and portability.", "parameters": [{"required": true, "is_array": false, "param_name": "path", "param_class": "dbgpt_ext.datasource.rdbms.conn_sqlite.SQLiteConnectorParameters", "param_type": "string", "label": "path", "description": "SQLite 数据库文件路径。使用 ':memory:' 表示内存数据库", "default_value": null, "valid_values": null, "ext_metadata": {"required": true}, "nested_fields": null, "param_order": 0}, {"required": false, "is_array": false, "param_name": "check_same_thread", "param_class": "dbgpt_ext.datasource.rdbms.conn_sqlite.SQLiteConnectorParameters", "param_type": "boolean", "label": "check_same_thread", "description": "是否检查同一线程，默认为 False。设置为 False 可允许跨线程共享连接。", "default_value": false, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 1}, {"required": false, "is_array": false, "param_name": "driver", "param_class": "dbgpt_ext.datasource.rdbms.conn_sqlite.SQLiteConnectorParameters", "param_type": "string", "label": "driver", "description": "驱动名称，默认是 sqlite", "default_value": "sqlite", "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 2}]}, {"name": "clickhouse", "label": "Clickhouse datasource", "description": "Columnar database for high-performance analytics and real-time queries.", "parameters": [{"required": true, "is_array": false, "param_name": "host", "param_class": "dbgpt_ext.datasource.rdbms.conn_clickhouse.ClickhouseParameters", "param_type": "string", "label": "host", "description": "数据库主机，例如 localhost", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 0}, {"required": true, "is_array": false, "param_name": "port", "param_class": "dbgpt_ext.datasource.rdbms.conn_clickhouse.ClickhouseParameters", "param_type": "integer", "label": "port", "description": "Database port, e.g., 8123", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 1}, {"required": true, "is_array": false, "param_name": "user", "param_class": "dbgpt_ext.datasource.rdbms.conn_clickhouse.ClickhouseParameters", "param_type": "string", "label": "user", "description": "用于连接数据库的用户", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 2}, {"required": true, "is_array": false, "param_name": "database", "param_class": "dbgpt_ext.datasource.rdbms.conn_clickhouse.ClickhouseParameters", "param_type": "string", "label": "database", "description": "数据库名称", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 3}, {"required": false, "is_array": false, "param_name": "engine", "param_class": "dbgpt_ext.datasource.rdbms.conn_clickhouse.ClickhouseParameters", "param_type": "string", "label": "engine", "description": "存储引擎，例如 MergeTree", "default_value": "MergeTree", "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 4}, {"required": false, "is_array": false, "param_name": "password", "param_class": "dbgpt_ext.datasource.rdbms.conn_clickhouse.ClickhouseParameters", "param_type": "string", "label": "password", "description": "数据库密码，您可以直接输入密码，当然也可以使用环境变量，例如 ${env:DBGPT_DB_PASSWORD}。", "default_value": "${env:DBGPT_DB_PASSWORD}", "valid_values": null, "ext_metadata": {"tags": "privacy"}, "nested_fields": null, "param_order": 5}, {"required": false, "is_array": false, "param_name": "http_pool_maxsize", "param_class": "dbgpt_ext.datasource.rdbms.conn_clickhouse.ClickhouseParameters", "param_type": "integer", "label": "http_pool_maxsize", "description": "HTTP 连接池最大大小", "default_value": 16, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 6}, {"required": false, "is_array": false, "param_name": "http_pool_num_pools", "param_class": "dbgpt_ext.datasource.rdbms.conn_clickhouse.ClickhouseParameters", "param_type": "integer", "label": "http_pool_num_pools", "description": "HTTP 连接池数量", "default_value": 12, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 7}, {"required": false, "is_array": false, "param_name": "connect_timeout", "param_class": "dbgpt_ext.datasource.rdbms.conn_clickhouse.ClickhouseParameters", "param_type": "integer", "label": "connect_timeout", "description": "数据库连接超时，默认 15 秒", "default_value": 15, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 8}, {"required": false, "is_array": false, "param_name": "distributed_ddl_task_timeout", "param_class": "dbgpt_ext.datasource.rdbms.conn_clickhouse.ClickhouseParameters", "param_type": "integer", "label": "distributed_ddl_task_timeout", "description": "分布式 DDL 任务超时，默认 300 秒", "default_value": 300, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 9}]}, {"name": "doris", "label": "Apache Doris datasource", "description": "A new-generation open-source real-time data warehouse.", "parameters": [{"required": true, "is_array": false, "param_name": "host", "param_class": "dbgpt_ext.datasource.rdbms.conn_doris.DorisParameters", "param_type": "string", "label": "host", "description": "数据库主机，例如：localhost", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 0}, {"required": true, "is_array": false, "param_name": "port", "param_class": "dbgpt_ext.datasource.rdbms.conn_doris.DorisParameters", "param_type": "integer", "label": "port", "description": "数据库端口，例如：3306", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 1}, {"required": true, "is_array": false, "param_name": "user", "param_class": "dbgpt_ext.datasource.rdbms.conn_doris.DorisParameters", "param_type": "string", "label": "user", "description": "用于连接数据库的用户", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 2}, {"required": true, "is_array": false, "param_name": "database", "param_class": "dbgpt_ext.datasource.rdbms.conn_doris.DorisParameters", "param_type": "string", "label": "database", "description": "数据库名称", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 3}, {"required": false, "is_array": false, "param_name": "driver", "param_class": "dbgpt_ext.datasource.rdbms.conn_doris.DorisParameters", "param_type": "string", "label": "driver", "description": "Doris 的驱动名称，默认为 doris。", "default_value": "doris", "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 4}, {"required": false, "is_array": false, "param_name": "password", "param_class": "dbgpt_ext.datasource.rdbms.conn_doris.DorisParameters", "param_type": "string", "label": "password", "description": "数据库密码，你可以直接写入密码，当然也可以使用环境变量，例如：${env:DBGPT_DB_PASSWORD}", "default_value": "${env:DBGPT_DB_PASSWORD}", "valid_values": null, "ext_metadata": {"tags": "privacy"}, "nested_fields": null, "param_order": 5}, {"required": false, "is_array": false, "param_name": "pool_size", "param_class": "dbgpt_ext.datasource.rdbms.conn_doris.DorisParameters", "param_type": "integer", "label": "pool_size", "description": "连接池大小，默认为 5", "default_value": 5, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 6}, {"required": false, "is_array": false, "param_name": "max_overflow", "param_class": "dbgpt_ext.datasource.rdbms.conn_doris.DorisParameters", "param_type": "integer", "label": "max_overflow", "description": "最大溢出连接数，默认为 10", "default_value": 10, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 7}, {"required": false, "is_array": false, "param_name": "pool_timeout", "param_class": "dbgpt_ext.datasource.rdbms.conn_doris.DorisParameters", "param_type": "integer", "label": "pool_timeout", "description": "连接池超时时间，默认为 30 秒", "default_value": 30, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 8}, {"required": false, "is_array": false, "param_name": "pool_recycle", "param_class": "dbgpt_ext.datasource.rdbms.conn_doris.DorisParameters", "param_type": "integer", "label": "pool_recycle", "description": "连接池回收时间，默认为 3600 秒", "default_value": 3600, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 9}, {"required": false, "is_array": false, "param_name": "pool_pre_ping", "param_class": "dbgpt_ext.datasource.rdbms.conn_doris.DorisParameters", "param_type": "boolean", "label": "pool_pre_ping", "description": "连接池预检查，默认开启", "default_value": true, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 10}]}, {"name": "duckdb", "label": "DuckDB datasource", "description": "In-memory analytical database with efficient query processing.", "parameters": [{"required": true, "is_array": false, "param_name": "path", "param_class": "dbgpt_ext.datasource.rdbms.conn_duckdb.DuckDbConnectorParameters", "param_type": "string", "label": "path", "description": "DuckDB 文件路径。", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 0}, {"required": false, "is_array": false, "param_name": "driver", "param_class": "dbgpt_ext.datasource.rdbms.conn_duckdb.DuckDbConnectorParameters", "param_type": "string", "label": "driver", "description": "DuckDB 的驱动名称，默认为 duckdb。", "default_value": "duckdb", "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 1}]}, {"name": "hive", "label": "Apache Hive datasource", "description": "A distributed fault-tolerant data warehouse system.", "parameters": [{"required": true, "is_array": false, "param_name": "host", "param_class": "dbgpt_ext.datasource.rdbms.conn_hive.HiveParameters", "param_type": "string", "label": "host", "description": "Hive 服务器主机", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 0}, {"required": false, "is_array": false, "param_name": "port", "param_class": "dbgpt_ext.datasource.rdbms.conn_hive.HiveParameters", "param_type": "integer", "label": "port", "description": "Hive 服务器端口，默认为 10000", "default_value": 10000, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 1}, {"required": false, "is_array": false, "param_name": "database", "param_class": "dbgpt_ext.datasource.rdbms.conn_hive.HiveParameters", "param_type": "string", "label": "database", "description": "数据库名称，默认为 'default'", "default_value": "default", "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 2}, {"required": false, "is_array": false, "param_name": "auth", "param_class": "dbgpt_ext.datasource.rdbms.conn_hive.HiveParameters", "param_type": "string", "label": "auth", "description": "认证模式：NONE、NOSASL、LDAP、KERBEROS、CUSTOM", "default_value": "NONE", "valid_values": ["NONE", "NOSASL", "LDAP", "KERBEROS", "CUSTOM"], "ext_metadata": {}, "nested_fields": null, "param_order": 3}, {"required": false, "is_array": false, "param_name": "username", "param_class": "dbgpt_ext.datasource.rdbms.conn_hive.HiveParameters", "param_type": "string", "label": "username", "description": "用于认证的用户名", "default_value": "", "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 4}, {"required": false, "is_array": false, "param_name": "password", "param_class": "dbgpt_ext.datasource.rdbms.conn_hive.HiveParameters", "param_type": "string", "label": "password", "description": "LDAP 或 CUSTOM 认证的密码", "default_value": "", "valid_values": null, "ext_metadata": {"tags": "privacy"}, "nested_fields": null, "param_order": 5}, {"required": false, "is_array": false, "param_name": "kerberos_service_name", "param_class": "dbgpt_ext.datasource.rdbms.conn_hive.HiveParameters", "param_type": "string", "label": "kerberos_service_name", "description": "Kerberos 服务名称", "default_value": "hive", "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 6}, {"required": false, "is_array": false, "param_name": "transport_mode", "param_class": "dbgpt_ext.datasource.rdbms.conn_hive.HiveParameters", "param_type": "string", "label": "transport_mode", "description": "传输模式：二进制或 HTTP", "default_value": "binary", "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 7}, {"required": false, "is_array": false, "param_name": "driver", "param_class": "dbgpt_ext.datasource.rdbms.conn_hive.HiveParameters", "param_type": "string", "label": "driver", "description": "Hive 的驱动程序名称，默认为 hive。", "default_value": "hive", "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 8}]}, {"name": "mssql", "label": "MSSQL datasource", "description": "Powerful, scalable, secure relational database system by Microsoft.", "parameters": [{"required": true, "is_array": false, "param_name": "host", "param_class": "dbgpt_ext.datasource.rdbms.conn_mssql.MSSQLParameters", "param_type": "string", "label": "host", "description": "数据库主机，例如：localhost", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 0}, {"required": true, "is_array": false, "param_name": "port", "param_class": "dbgpt_ext.datasource.rdbms.conn_mssql.MSSQLParameters", "param_type": "integer", "label": "port", "description": "数据库端口，例如：3306", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 1}, {"required": true, "is_array": false, "param_name": "user", "param_class": "dbgpt_ext.datasource.rdbms.conn_mssql.MSSQLParameters", "param_type": "string", "label": "user", "description": "用于连接数据库的用户", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 2}, {"required": true, "is_array": false, "param_name": "database", "param_class": "dbgpt_ext.datasource.rdbms.conn_mssql.MSSQLParameters", "param_type": "string", "label": "database", "description": "数据库名称", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 3}, {"required": false, "is_array": false, "param_name": "driver", "param_class": "dbgpt_ext.datasource.rdbms.conn_mssql.MSSQLParameters", "param_type": "string", "label": "driver", "description": "MSSQL 的驱动名称，默认是 mssql+pymssql。", "default_value": "mssql+pymssql", "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 4}, {"required": false, "is_array": false, "param_name": "password", "param_class": "dbgpt_ext.datasource.rdbms.conn_mssql.MSSQLParameters", "param_type": "string", "label": "password", "description": "数据库密码，你可以直接写入密码，当然也可以使用环境变量，例如：${env:DBGPT_DB_PASSWORD}", "default_value": "${env:DBGPT_DB_PASSWORD}", "valid_values": null, "ext_metadata": {"tags": "privacy"}, "nested_fields": null, "param_order": 5}, {"required": false, "is_array": false, "param_name": "pool_size", "param_class": "dbgpt_ext.datasource.rdbms.conn_mssql.MSSQLParameters", "param_type": "integer", "label": "pool_size", "description": "连接池大小，默认为 5", "default_value": 5, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 6}, {"required": false, "is_array": false, "param_name": "max_overflow", "param_class": "dbgpt_ext.datasource.rdbms.conn_mssql.MSSQLParameters", "param_type": "integer", "label": "max_overflow", "description": "最大溢出连接数，默认为 10", "default_value": 10, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 7}, {"required": false, "is_array": false, "param_name": "pool_timeout", "param_class": "dbgpt_ext.datasource.rdbms.conn_mssql.MSSQLParameters", "param_type": "integer", "label": "pool_timeout", "description": "连接池超时时间，默认为 30 秒", "default_value": 30, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 8}, {"required": false, "is_array": false, "param_name": "pool_recycle", "param_class": "dbgpt_ext.datasource.rdbms.conn_mssql.MSSQLParameters", "param_type": "integer", "label": "pool_recycle", "description": "连接池回收时间，默认为 3600 秒", "default_value": 3600, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 9}, {"required": false, "is_array": false, "param_name": "pool_pre_ping", "param_class": "dbgpt_ext.datasource.rdbms.conn_mssql.MSSQLParameters", "param_type": "boolean", "label": "pool_pre_ping", "description": "连接池预检查，默认开启", "default_value": true, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 10}]}, {"name": "mysql", "label": "MySQL datasource", "description": "Fast, reliable, scalable open-source relational database management system.", "parameters": [{"required": true, "is_array": false, "param_name": "host", "param_class": "dbgpt_ext.datasource.rdbms.conn_mysql.MySQLParameters", "param_type": "string", "label": "host", "description": "数据库主机，例如：localhost", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 0}, {"required": true, "is_array": false, "param_name": "port", "param_class": "dbgpt_ext.datasource.rdbms.conn_mysql.MySQLParameters", "param_type": "integer", "label": "port", "description": "数据库端口，例如：3306", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 1}, {"required": true, "is_array": false, "param_name": "user", "param_class": "dbgpt_ext.datasource.rdbms.conn_mysql.MySQLParameters", "param_type": "string", "label": "user", "description": "用于连接数据库的用户", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 2}, {"required": true, "is_array": false, "param_name": "database", "param_class": "dbgpt_ext.datasource.rdbms.conn_mysql.MySQLParameters", "param_type": "string", "label": "database", "description": "数据库名称", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 3}, {"required": false, "is_array": false, "param_name": "driver", "param_class": "dbgpt_ext.datasource.rdbms.conn_mysql.MySQLParameters", "param_type": "string", "label": "driver", "description": "MySQL 的驱动名称，默认是 mysql+pymysql。", "default_value": "mysql+pymysql", "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 4}, {"required": false, "is_array": false, "param_name": "password", "param_class": "dbgpt_ext.datasource.rdbms.conn_mysql.MySQLParameters", "param_type": "string", "label": "password", "description": "数据库密码，你可以直接写入密码，当然也可以使用环境变量，例如：${env:DBGPT_DB_PASSWORD}", "default_value": "${env:DBGPT_DB_PASSWORD}", "valid_values": null, "ext_metadata": {"tags": "privacy"}, "nested_fields": null, "param_order": 5}, {"required": false, "is_array": false, "param_name": "pool_size", "param_class": "dbgpt_ext.datasource.rdbms.conn_mysql.MySQLParameters", "param_type": "integer", "label": "pool_size", "description": "连接池大小，默认为 5", "default_value": 5, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 6}, {"required": false, "is_array": false, "param_name": "max_overflow", "param_class": "dbgpt_ext.datasource.rdbms.conn_mysql.MySQLParameters", "param_type": "integer", "label": "max_overflow", "description": "最大溢出连接数，默认为 10", "default_value": 10, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 7}, {"required": false, "is_array": false, "param_name": "pool_timeout", "param_class": "dbgpt_ext.datasource.rdbms.conn_mysql.MySQLParameters", "param_type": "integer", "label": "pool_timeout", "description": "连接池超时时间，默认为 30 秒", "default_value": 30, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 8}, {"required": false, "is_array": false, "param_name": "pool_recycle", "param_class": "dbgpt_ext.datasource.rdbms.conn_mysql.MySQLParameters", "param_type": "integer", "label": "pool_recycle", "description": "连接池回收时间，默认为 3600 秒", "default_value": 3600, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 9}, {"required": false, "is_array": false, "param_name": "pool_pre_ping", "param_class": "dbgpt_ext.datasource.rdbms.conn_mysql.MySQLParameters", "param_type": "boolean", "label": "pool_pre_ping", "description": "连接池预检查，默认开启", "default_value": true, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 10}]}, {"name": "oceanbase", "label": "OceanBase datasource", "description": "An Ultra-Fast & Cost-Effective Distributed SQL Database.", "parameters": [{"required": true, "is_array": false, "param_name": "host", "param_class": "dbgpt_ext.datasource.rdbms.conn_oceanbase.OceanBaseParameters", "param_type": "string", "label": "host", "description": "数据库主机，例如：localhost", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 0}, {"required": true, "is_array": false, "param_name": "port", "param_class": "dbgpt_ext.datasource.rdbms.conn_oceanbase.OceanBaseParameters", "param_type": "integer", "label": "port", "description": "数据库端口，例如：3306", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 1}, {"required": true, "is_array": false, "param_name": "user", "param_class": "dbgpt_ext.datasource.rdbms.conn_oceanbase.OceanBaseParameters", "param_type": "string", "label": "user", "description": "用于连接数据库的用户", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 2}, {"required": true, "is_array": false, "param_name": "database", "param_class": "dbgpt_ext.datasource.rdbms.conn_oceanbase.OceanBaseParameters", "param_type": "string", "label": "database", "description": "数据库名称", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 3}, {"required": false, "is_array": false, "param_name": "driver", "param_class": "dbgpt_ext.datasource.rdbms.conn_oceanbase.OceanBaseParameters", "param_type": "string", "label": "driver", "description": "OceanBase 的驱动名称，默认为 mysql+ob。", "default_value": "mysql+ob", "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 4}, {"required": false, "is_array": false, "param_name": "password", "param_class": "dbgpt_ext.datasource.rdbms.conn_oceanbase.OceanBaseParameters", "param_type": "string", "label": "password", "description": "数据库密码，你可以直接写入密码，当然也可以使用环境变量，例如：${env:DBGPT_DB_PASSWORD}", "default_value": "${env:DBGPT_DB_PASSWORD}", "valid_values": null, "ext_metadata": {"tags": "privacy"}, "nested_fields": null, "param_order": 5}, {"required": false, "is_array": false, "param_name": "pool_size", "param_class": "dbgpt_ext.datasource.rdbms.conn_oceanbase.OceanBaseParameters", "param_type": "integer", "label": "pool_size", "description": "连接池大小，默认为 5", "default_value": 5, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 6}, {"required": false, "is_array": false, "param_name": "max_overflow", "param_class": "dbgpt_ext.datasource.rdbms.conn_oceanbase.OceanBaseParameters", "param_type": "integer", "label": "max_overflow", "description": "最大溢出连接数，默认为 10", "default_value": 10, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 7}, {"required": false, "is_array": false, "param_name": "pool_timeout", "param_class": "dbgpt_ext.datasource.rdbms.conn_oceanbase.OceanBaseParameters", "param_type": "integer", "label": "pool_timeout", "description": "连接池超时时间，默认为 30 秒", "default_value": 30, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 8}, {"required": false, "is_array": false, "param_name": "pool_recycle", "param_class": "dbgpt_ext.datasource.rdbms.conn_oceanbase.OceanBaseParameters", "param_type": "integer", "label": "pool_recycle", "description": "连接池回收时间，默认为 3600 秒", "default_value": 3600, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 9}, {"required": false, "is_array": false, "param_name": "pool_pre_ping", "param_class": "dbgpt_ext.datasource.rdbms.conn_oceanbase.OceanBaseParameters", "param_type": "boolean", "label": "pool_pre_ping", "description": "连接池预检查，默认开启", "default_value": true, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 10}]}, {"name": "postgresql", "label": "PostreSQL datasource", "description": "Powerful open-source relational database with extensibility and SQL standards.", "parameters": [{"required": true, "is_array": false, "param_name": "host", "param_class": "dbgpt_ext.datasource.rdbms.conn_postgresql.PostgreSQLParameters", "param_type": "string", "label": "host", "description": "数据库主机，例如：localhost", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 0}, {"required": true, "is_array": false, "param_name": "port", "param_class": "dbgpt_ext.datasource.rdbms.conn_postgresql.PostgreSQLParameters", "param_type": "integer", "label": "port", "description": "数据库端口，例如：3306", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 1}, {"required": true, "is_array": false, "param_name": "user", "param_class": "dbgpt_ext.datasource.rdbms.conn_postgresql.PostgreSQLParameters", "param_type": "string", "label": "user", "description": "用于连接数据库的用户", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 2}, {"required": true, "is_array": false, "param_name": "database", "param_class": "dbgpt_ext.datasource.rdbms.conn_postgresql.PostgreSQLParameters", "param_type": "string", "label": "database", "description": "数据库名称", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 3}, {"required": false, "is_array": false, "param_name": "driver", "param_class": "dbgpt_ext.datasource.rdbms.conn_postgresql.PostgreSQLParameters", "param_type": "string", "label": "driver", "description": "Postgres 驱动程序名称，默认为 postgresql+psycopg2。", "default_value": "postgresql+psycopg2", "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 4}, {"required": false, "is_array": false, "param_name": "password", "param_class": "dbgpt_ext.datasource.rdbms.conn_postgresql.PostgreSQLParameters", "param_type": "string", "label": "password", "description": "数据库密码，你可以直接写入密码，当然也可以使用环境变量，例如：${env:DBGPT_DB_PASSWORD}", "default_value": "${env:DBGPT_DB_PASSWORD}", "valid_values": null, "ext_metadata": {"tags": "privacy"}, "nested_fields": null, "param_order": 5}, {"required": false, "is_array": false, "param_name": "pool_size", "param_class": "dbgpt_ext.datasource.rdbms.conn_postgresql.PostgreSQLParameters", "param_type": "integer", "label": "pool_size", "description": "连接池大小，默认为 5", "default_value": 5, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 6}, {"required": false, "is_array": false, "param_name": "max_overflow", "param_class": "dbgpt_ext.datasource.rdbms.conn_postgresql.PostgreSQLParameters", "param_type": "integer", "label": "max_overflow", "description": "最大溢出连接数，默认为 10", "default_value": 10, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 7}, {"required": false, "is_array": false, "param_name": "pool_timeout", "param_class": "dbgpt_ext.datasource.rdbms.conn_postgresql.PostgreSQLParameters", "param_type": "integer", "label": "pool_timeout", "description": "连接池超时时间，默认为 30 秒", "default_value": 30, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 8}, {"required": false, "is_array": false, "param_name": "pool_recycle", "param_class": "dbgpt_ext.datasource.rdbms.conn_postgresql.PostgreSQLParameters", "param_type": "integer", "label": "pool_recycle", "description": "连接池回收时间，默认为 3600 秒", "default_value": 3600, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 9}, {"required": false, "is_array": false, "param_name": "pool_pre_ping", "param_class": "dbgpt_ext.datasource.rdbms.conn_postgresql.PostgreSQLParameters", "param_type": "boolean", "label": "pool_pre_ping", "description": "连接池预检查，默认开启", "default_value": true, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 10}, {"required": false, "is_array": false, "param_name": "schema", "param_class": "dbgpt_ext.datasource.rdbms.conn_postgresql.PostgreSQLParameters", "param_type": "string", "label": "schema", "description": "数据库模式，默认为 'public'", "default_value": "public", "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 11}]}, {"name": "starrocks", "label": "StarRocks datasource", "description": "An Open-Source, High-Performance Analytical Database.", "parameters": [{"required": true, "is_array": false, "param_name": "host", "param_class": "dbgpt_ext.datasource.rdbms.conn_starrocks.StarRocksParameters", "param_type": "string", "label": "host", "description": "数据库主机，例如：localhost", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 0}, {"required": true, "is_array": false, "param_name": "port", "param_class": "dbgpt_ext.datasource.rdbms.conn_starrocks.StarRocksParameters", "param_type": "integer", "label": "port", "description": "数据库端口，例如：3306", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 1}, {"required": true, "is_array": false, "param_name": "user", "param_class": "dbgpt_ext.datasource.rdbms.conn_starrocks.StarRocksParameters", "param_type": "string", "label": "user", "description": "用于连接数据库的用户", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 2}, {"required": true, "is_array": false, "param_name": "database", "param_class": "dbgpt_ext.datasource.rdbms.conn_starrocks.StarRocksParameters", "param_type": "string", "label": "database", "description": "数据库名称", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 3}, {"required": false, "is_array": false, "param_name": "driver", "param_class": "dbgpt_ext.datasource.rdbms.conn_starrocks.StarRocksParameters", "param_type": "string", "label": "driver", "description": "StarRocks 的驱动程序名称，默认为 starrocks。", "default_value": "starrocks", "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 4}, {"required": false, "is_array": false, "param_name": "password", "param_class": "dbgpt_ext.datasource.rdbms.conn_starrocks.StarRocksParameters", "param_type": "string", "label": "password", "description": "数据库密码，你可以直接写入密码，当然也可以使用环境变量，例如：${env:DBGPT_DB_PASSWORD}", "default_value": "${env:DBGPT_DB_PASSWORD}", "valid_values": null, "ext_metadata": {"tags": "privacy"}, "nested_fields": null, "param_order": 5}, {"required": false, "is_array": false, "param_name": "pool_size", "param_class": "dbgpt_ext.datasource.rdbms.conn_starrocks.StarRocksParameters", "param_type": "integer", "label": "pool_size", "description": "连接池大小，默认为 5", "default_value": 5, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 6}, {"required": false, "is_array": false, "param_name": "max_overflow", "param_class": "dbgpt_ext.datasource.rdbms.conn_starrocks.StarRocksParameters", "param_type": "integer", "label": "max_overflow", "description": "最大溢出连接数，默认为 10", "default_value": 10, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 7}, {"required": false, "is_array": false, "param_name": "pool_timeout", "param_class": "dbgpt_ext.datasource.rdbms.conn_starrocks.StarRocksParameters", "param_type": "integer", "label": "pool_timeout", "description": "连接池超时时间，默认为 30 秒", "default_value": 30, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 8}, {"required": false, "is_array": false, "param_name": "pool_recycle", "param_class": "dbgpt_ext.datasource.rdbms.conn_starrocks.StarRocksParameters", "param_type": "integer", "label": "pool_recycle", "description": "连接池回收时间，默认为 3600 秒", "default_value": 3600, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 9}, {"required": false, "is_array": false, "param_name": "pool_pre_ping", "param_class": "dbgpt_ext.datasource.rdbms.conn_starrocks.StarRocksParameters", "param_type": "boolean", "label": "pool_pre_ping", "description": "连接池预检查，默认开启", "default_value": true, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 10}]}, {"name": "vertica", "label": "Vertica datasource", "description": "Vertica is a strongly consistent, ACID-compliant, SQL data warehouse, built for the scale and complexity of today`s data-driven world.", "parameters": [{"required": true, "is_array": false, "param_name": "host", "param_class": "dbgpt_ext.datasource.rdbms.conn_vertica.VerticaParameters", "param_type": "string", "label": "host", "description": "数据库主机，例如：localhost", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 0}, {"required": true, "is_array": false, "param_name": "port", "param_class": "dbgpt_ext.datasource.rdbms.conn_vertica.VerticaParameters", "param_type": "integer", "label": "port", "description": "数据库端口，例如：3306", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 1}, {"required": true, "is_array": false, "param_name": "user", "param_class": "dbgpt_ext.datasource.rdbms.conn_vertica.VerticaParameters", "param_type": "string", "label": "user", "description": "用于连接数据库的用户", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 2}, {"required": true, "is_array": false, "param_name": "database", "param_class": "dbgpt_ext.datasource.rdbms.conn_vertica.VerticaParameters", "param_type": "string", "label": "database", "description": "数据库名称", "default_value": null, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 3}, {"required": false, "is_array": false, "param_name": "driver", "param_class": "dbgpt_ext.datasource.rdbms.conn_vertica.VerticaParameters", "param_type": "string", "label": "driver", "description": "Vertica 的驱动名称，默认为 vertica+vertica_python。", "default_value": "vertica+vertica_python", "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 4}, {"required": false, "is_array": false, "param_name": "password", "param_class": "dbgpt_ext.datasource.rdbms.conn_vertica.VerticaParameters", "param_type": "string", "label": "password", "description": "数据库密码，你可以直接写入密码，当然也可以使用环境变量，例如：${env:DBGPT_DB_PASSWORD}", "default_value": "${env:DBGPT_DB_PASSWORD}", "valid_values": null, "ext_metadata": {"tags": "privacy"}, "nested_fields": null, "param_order": 5}, {"required": false, "is_array": false, "param_name": "pool_size", "param_class": "dbgpt_ext.datasource.rdbms.conn_vertica.VerticaParameters", "param_type": "integer", "label": "pool_size", "description": "连接池大小，默认为 5", "default_value": 5, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 6}, {"required": false, "is_array": false, "param_name": "max_overflow", "param_class": "dbgpt_ext.datasource.rdbms.conn_vertica.VerticaParameters", "param_type": "integer", "label": "max_overflow", "description": "最大溢出连接数，默认为 10", "default_value": 10, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 7}, {"required": false, "is_array": false, "param_name": "pool_timeout", "param_class": "dbgpt_ext.datasource.rdbms.conn_vertica.VerticaParameters", "param_type": "integer", "label": "pool_timeout", "description": "连接池超时时间，默认为 30 秒", "default_value": 30, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 8}, {"required": false, "is_array": false, "param_name": "pool_recycle", "param_class": "dbgpt_ext.datasource.rdbms.conn_vertica.VerticaParameters", "param_type": "integer", "label": "pool_recycle", "description": "连接池回收时间，默认为 3600 秒", "default_value": 3600, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 9}, {"required": false, "is_array": false, "param_name": "pool_pre_ping", "param_class": "dbgpt_ext.datasource.rdbms.conn_vertica.VerticaParameters", "param_type": "boolean", "label": "pool_pre_ping", "description": "连接池预检查，默认开启", "default_value": true, "valid_values": null, "ext_metadata": {}, "nested_fields": null, "param_order": 10}]}]}}