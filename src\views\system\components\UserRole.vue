<template>
  <div class="role-manage-container">
    <div class="tool-line">
      <div class ="right">
        <a-button type="primary" @click="handleOpenEdit">
          <plus-outlined /> 添加角色
        </a-button>
        <a-popconfirm
          title="确定要删除角色吗?"
          ok-text="确定"
          cancel-text="取消"
          :disabled="selectedRowKeys.length === 0"
          @confirm="deleteRole()"
        >
          <a-button type="primary" danger class="btn-item" :disabled="selectedRowKeys.length === 0" >批量删除</a-button>
        </a-popconfirm>
      </div>
    </div>
    <a-divider>角色列表</a-divider>
    <div class="role-list">
      <a-table
        :columns="columns"
        :data-source="roleList"
        :loading="loading"
        rowKey="id"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag color="green" v-if="record.status === 0">正常</a-tag>
            <a-tag color="red" v-else>停用</a-tag>
          </template>
          <template v-else-if="column.key === 'operation'">
            <a-space>
              <a-popconfirm
                title="确定要删除此角色吗?"
                ok-text="确定"
                cancel-text="取消"
                @confirm="deleteRole(record)"
              >
                <a class="delete-link">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>
    <UserRoleDialog ref="userRoleDialogRef" 
      :roleIds="roleIds" 
      :userId="props.id" 
      @refreshData="handleRefreshData()"
      @refreshDataForCreate="handleRefreshDataForCreate "/>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import UserRoleDialog from './UserRoleDialog.vue';
import type { UserRoleItem, SaveRoleDTO } from '@/types/system';
import { useRouter } from 'vue-router';
import * as api from '@/api/system';

const router = useRouter();

const props = defineProps({
  id: {
    type: Number,
    default: false
  }
});

// 表格列定义
const columns = [
  {
    title: '名称',
    dataIndex: 'role_name',
    key: 'role_name',
  },
  {
    title: '操作',
    key: 'operation',
    width: 150
  },
];

const roleIds = computed(() => {
  const ids: (number | string | undefined)[] = [];
  roleList.value.map((item) => {
    ids.push(item.role_id);
  });
  return ids;
})

const selectedRowKeys = ref<string[]>([]); // 选中的行键数组
const userRoleDialogRef = ref();

const onSelectChange = (selectedRowKeysValue: string[]) => {
  selectedRowKeys.value = selectedRowKeysValue;
};

// 提示词列表和加载状态
const roleList = ref<UserRoleItem[]>([]);
const loading = ref(false);

// 获取提示词列表
const fetchRoleList = async () => {
  loading.value = true;
  try {
    const res = await api.getUserRoleList({ 
      user_id: props.id, 
    });
    roleList.value = res.data;
  } catch (error) {
    console.error('获取用户角色列表失败', error);
  } finally {
    loading.value = false;
  }
};

const resetRoleList = () => {
  roleList.value = [];
  selectedRowKeys.value = [];
}

const handleOpenEdit = () => {
  userRoleDialogRef.value?.handleOpen();
};

// 删除提示词
const deleteRole = async (data?: UserRoleItem) => {
  let ids: (string | number)[] = [];
  if (data) {
    ids.push(data.id);
  } else {
    ids = selectedRowKeys.value;
  }
  if (props.id) {
    await api.deleteUserRole(props.id, { ids });
    message.success('删除成功');
    fetchRoleList();
  } else {
    roleList.value = roleList.value.filter(item => !ids.includes(item.id));
  }
  if (!data) {
    selectedRowKeys.value = [];
  }
};

const handleRefreshData = () => {
  fetchRoleList();
  selectedRowKeys.value = [];
}

const handleRefreshDataForCreate = (addRoleList: SaveRoleDTO[]) => {
  addRoleList.map((item) => {
    roleList.value.push({
      id: item.id || '',
      role_id: item.id || '',
      role_name: item.name || '',
    })
  })
}

defineExpose({
  roleList,
  fetchRoleList,
  resetRoleList,
});
</script>

<style scoped>
.role-manage-container {
  padding: 24px;
}

.tool-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  margin-top: 10px;
  .right {
    display: flex;
    .btn-item {
    margin-left: 10px;
    }
  }
}

.role-list {
  margin-top: 16px;
}

.delete-link {
  color: #ff4d4f;
}
</style> 