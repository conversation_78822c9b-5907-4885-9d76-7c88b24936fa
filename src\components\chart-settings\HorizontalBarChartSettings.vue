<template>
  <common-chart-settings
    :options="props.options"
    chart-type="horizontal-bar"
    @update="updateOptions"
  >
    <!-- 样式配置 -->
    <template #style-settings>
      <!-- 坐标轴设置 -->
      <a-collapse-panel key="axis" header="坐标轴设置">
        <a-form-item label="X轴名称">
          <a-input v-model:value="chartConfig.xAxis.name" placeholder="请输入X轴名称" />
        </a-form-item>
        
        <a-form-item label="Y轴名称">
          <a-input v-model:value="chartConfig.yAxis.name" placeholder="请输入Y轴名称" />
        </a-form-item>
        
        <a-form-item label="Y轴位置">
          <a-select v-model:value="chartConfig.yAxis.position">
            <a-select-option value="left">左侧</a-select-option>
            <a-select-option value="right">右侧</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="显示网格线">
          <a-switch v-model:checked="chartConfig.grid.show" />
        </a-form-item>
      </a-collapse-panel>
      
      <!-- 柱状样式设置 -->
      <a-collapse-panel key="bar" header="柱状样式">
        <a-form-item label="柱子宽度">
          <a-input-number 
            v-model:value="barWidth" 
            :min="10" 
            :max="50"
            @change="updateBarWidth" 
          />
        </a-form-item>
        
        <a-form-item label="柱间距离">
          <a-input-number 
            v-model:value="barGap" 
            :min="0" 
            :max="1"
            :step="0.1"
            :formatter="(value: number) => `${value * 100}%`"
            :parser="(value: string) => parseFloat(value.replace('%', '')) / 100"
            @change="updateBarGap" 
          />
        </a-form-item>
        
        <a-form-item label="显示标签">
          <a-switch v-model:checked="labelShow" @change="updateLabelShow" />
        </a-form-item>
        
        <a-form-item label="标签位置" v-if="labelShow">
          <a-select v-model:value="labelPosition" @change="updateLabelPosition">
            <a-select-option value="inside">内部</a-select-option>
            <a-select-option value="outside">外部</a-select-option>
            <a-select-option value="right">右侧</a-select-option>
          </a-select>
        </a-form-item>
      </a-collapse-panel>
    </template>
  </common-chart-settings>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue';
import CommonChartSettings from './CommonChartSettings.vue';
import type { ChartOptions } from '../../types/chart';

const props = defineProps({
  options: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update']);

// 柱状图样式设置
const barWidth = ref(30);
const barGap = ref(0.3);
const labelShow = ref(false);
const labelPosition = ref('right');

// 默认水平柱状图配置
const defaultHorizontalBarConfig = {
  type: 'horizontalBar',
  title: { text: '水平条形图' },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  legend: {
    show: true,
    orient: 'horizontal',
    data: ['系列1']
  },
  grid: {
    show: false,
    left: 40,
    right: 20,
    bottom: 30,
    top: 60,
    containLabel: true
  },
  xAxis: {
    type: 'value',
    name: '数值'
  },
  yAxis: {
    type: 'category',
    name: '类目',
    position: 'left',
    data: ['类目1', '类目2', '类目3', '类目4', '类目5']
  },
  series: [
    {
      name: '系列1',
      type: 'bar',
      data: [18, 23, 29, 15, 25],
      barWidth: 30,
      barGap: 0.3,
      label: {
        show: false,
        position: 'right'
      },
      itemStyle: {
        color: '#5470c6'
      }
    }
  ]
};

// 直接访问config部分的计算属性
const chartConfig = computed({
  get: () => {
    return props.options.config || defaultHorizontalBarConfig;
  },
  set: (newConfig) => {
    const updatedOptions = {
      ...props.options,
      config: newConfig
    };
    emit('update', updatedOptions);
  }
});

// 更新配置
const updateOptions = (newOptions: ChartOptions) => {
  emit('update', newOptions);
};

// 初始化组件
onMounted(() => {
  // 初始化样式控制变量
  if (chartConfig.value.series?.[0]) {
    barWidth.value = chartConfig.value.series[0].barWidth || 30;
    barGap.value = chartConfig.value.series[0].barGap || 0.3;
    
    if (chartConfig.value.series[0].label) {
      labelShow.value = chartConfig.value.series[0].label.show || false;
      labelPosition.value = chartConfig.value.series[0].label.position || 'right';
    }
  }
});

// 监听props变化，更新本地控制变量
watch(() => props.options, (newOptions) => {
  if (newOptions?.config?.series?.[0]) {
    barWidth.value = newOptions.config.series[0].barWidth || 30;
    barGap.value = newOptions.config.series[0].barGap || 0.3;
    
    if (newOptions.config.series[0].label) {
      labelShow.value = newOptions.config.series[0].label.show || false;
      labelPosition.value = newOptions.config.series[0].label.position || 'right';
    }
  }
}, { deep: true });

// 更新柱子宽度
const updateBarWidth = () => {
  const config = { ...chartConfig.value };
  config.series.forEach((series: any) => {
    series.barWidth = barWidth.value;
  });
  chartConfig.value = config;
};

// 更新柱间距离
const updateBarGap = () => {
  const config = { ...chartConfig.value };
  config.series.forEach((series: any) => {
    series.barGap = barGap.value;
  });
  chartConfig.value = config;
};

// 更新标签显示
const updateLabelShow = () => {
  const config = { ...chartConfig.value };
  config.series.forEach((series: any) => {
    if (!series.label) {
      series.label = { show: labelShow.value, position: labelPosition.value };
    } else {
      series.label.show = labelShow.value;
    }
  });
  chartConfig.value = config;
};

// 更新标签位置
const updateLabelPosition = () => {
  const config = { ...chartConfig.value };
  config.series.forEach((series: any) => {
    if (!series.label) {
      series.label = { show: labelShow.value, position: labelPosition.value };
    } else {
      series.label.position = labelPosition.value;
    }
  });
  chartConfig.value = config;
};
</script>

<style scoped>
.chart-settings-container {
  padding: 16px;
}
</style> 