<template>
  <ChatScrollView title="数据分析助手">
    <!-- <MessageBot :isProlog="true" content="通过自然语言与你的私人数据进行对话" /> -->
  </ChatScrollView>
  <ChatInput :isNew="true"  @loading="handleLoading"/>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ChatInput from '../chat/components/chat-input.vue';
import MessageBot from '@/components/message-bot.vue';
import ChatScrollView from '@/components/chat-scroll-view.vue';

const pageLoading = ref<boolean>(false);

const handleLoading = () => {
  pageLoading.value = true;
}
</script>
