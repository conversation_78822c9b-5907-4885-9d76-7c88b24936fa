<template>
  <div class="database-selector">
    <!-- 选择框 -->
    <div class="selector-dropdown" @click="toggleDropdown" :class="{ 'is-open': isDropdownOpen }">
      <div v-if="selectedDatabase" class="selected-database">
        <div class="database-icon">
          <img :src="getDatabaseIcon(selectedDatabase)" :alt="selectedDatabase" />
        </div>
        <div class="database-name">{{ selectedDatabase }}</div>
      </div>
      <div v-else class="placeholder">请选择数据库类型</div>
      <div class="dropdown-arrow" :class="{ 'arrow-up': isDropdownOpen }">
        <svg viewBox="0 0 1024 1024" width="12" height="12">
          <path d="M512 714.666667c-8.533333 0-17.066667-2.133333-23.466667-8.533334l-341.333333-341.333333c-12.8-12.8-12.8-32 0-44.8 12.8-12.8 32-12.8 44.8 0l320 320 320-320c12.8-12.8 32-12.8 44.8 0 12.8 12.8 12.8 32 0 44.8l-341.333333 341.333333c-6.4 6.4-14.933333 8.533333-23.466667 8.533334z" fill="currentColor"></path>
        </svg>
      </div>
    </div>

    <!-- 下拉选项列表 -->
    <div v-if="isDropdownOpen" class="dropdown-panel">
      <div class="search-box">
        <input
          type="text"
          v-model="searchText"
          placeholder="搜索数据库类型..."
          @click.stop
        />
      </div>
      <div class="database-grid">
        <div
          v-for="database in filteredDatabases"
          :key="database.name"
          class="database-item"
          :class="{ active: modelValue === database.name }"
          @click.stop="selectDatabase(database)"
        >
          <div class="database-icon">
            <img :src="getDatabaseIcon(database.name)" :alt="database.name" />
          </div>
          <div class="database-name">{{ database.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import type { DatabaseTypeItem } from '../../types/database';

// 导入所有数据库图标
import accessIcon from '../../assets/icons/database/access.png';
import snowflakeIcon from '../../assets/icons/database/snowflake.png';
import firebirdIcon from '../../assets/icons/database/firebird.png';
import kingbaseIcon from '../../assets/icons/database/kingbase.png';
import damengIcon from '../../assets/icons/database/dameng.png';
import gaussdbIcon from '../../assets/icons/database/gaussdb.png';
import tidbIcon from '../../assets/icons/database/tidb.png';
import prestodbIcon from '../../assets/icons/database/prestodb.png';
import kylinIcon from '../../assets/icons/database/kylin.png';
import teradataIcon from '../../assets/icons/database/teradata.png';
import mariadbIcon from '../../assets/icons/database/mariadb.png';
import verticaIcon from '../../assets/icons/database/vertica.png';
import db2Icon from '../../assets/icons/database/db2.png';
import hanaIcon from '../../assets/icons/database/hana.png';
import couchbaseIcon from '../../assets/icons/database/couchbase.png';
import cassandraIcon from '../../assets/icons/database/cassandra.png';
import dorisIcon from '../../assets/icons/database/doris.png';
import clickhouseIcon from '../../assets/icons/database/clickhouse.png';
import hiveIcon from '../../assets/icons/database/hive.png';
import sqliteIcon from '../../assets/icons/database/sqlite.png';
import sqlserverIcon from '../../assets/icons/database/sqlserver.png';
import oracleIcon from '../../assets/icons/database/oracle.png';
import mysqlIcon from '../../assets/icons/database/mysql.png';
import postgresqlIcon from '../../assets/icons/database/postgresql.png';
import oceanbaseIcon from '../../assets/icons/database/oceanbase.png';
import tugraphIcon from '../../assets/icons/database/tugraph.png';
import sparkIcon from '../../assets/icons/database/spark.png';
import duckdbIcon from '../../assets/icons/database/duckdb.png';
import starrocksIcon from '../../assets/icons/database/starrocks.png';

// 定义组件属性
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  databases: {
    type: Array as () => DatabaseTypeItem[],
    default: () => []
  }
});

// 定义事件
const emit = defineEmits(['update:modelValue', 'change']);

// 状态变量
const isDropdownOpen = ref(false);
const searchText = ref('');

// 获取数据库对应的图标
const getDatabaseIcon = (database: string) => {
  if (!database) return mysqlIcon;
  
  const databaseLower = database.toLowerCase();
  
  const iconMap: Record<string, any> = {
    'mysql': mysqlIcon,
    'postgresql': postgresqlIcon,
    'sqlserver': sqlserverIcon,
    'oracle': oracleIcon,
    'sqlite': sqliteIcon,
    'clickhouse': clickhouseIcon,
    'doris': dorisIcon,
    'hive': hiveIcon,
    'oceanbase': oceanbaseIcon,
    'starrocks': starrocksIcon,
    'vertica': verticaIcon,
    'cassandra': cassandraIcon,
    'couchbase': couchbaseIcon,
    'db2': db2Icon,
    'hana': hanaIcon,
    'mariadb': mariadbIcon,
    'teradata': teradataIcon,
    'kylin': kylinIcon,
    'prestodb': prestodbIcon,
    'tidb': tidbIcon,
    'gaussdb': gaussdbIcon,
    'dameng': damengIcon,
    'kingbase': kingbaseIcon,
    'firebird': firebirdIcon,
    'snowflake': snowflakeIcon,
    'access': accessIcon,
    'spark': sparkIcon,
    'mssql': sqlserverIcon,
    'tugraph': tugraphIcon,
    'duckdb': duckdbIcon
  };
  
  // 先尝试直接匹配
  if (iconMap[databaseLower]) {
    return iconMap[databaseLower];
  }
  
  // 如果没有直接匹配，尝试部分匹配
  for (const [key, icon] of Object.entries(iconMap)) {
    if (databaseLower.includes(key)) {
      return icon;
    }
  }
  
  return mysqlIcon; // 默认使用mysql图标
};

// 获取显示用的数据库名称
const getDatabaseDisplayName = (database: string) => {
  if (!database) return '';
  
  // 返回原始数据库名称
  return database;
};

// 点击外部关闭下拉框
const handleClickOutside = (e: MouseEvent) => {
  const target = e.target as HTMLElement;
  if (!target.closest('.database-selector')) {
    isDropdownOpen.value = false;
  }
};

// 过滤数据库列表
const filteredDatabases = computed(() => {
  if (!searchText.value) return props.databases;
  const search = searchText.value.toLowerCase();
  return props.databases.filter(database => {
    const name = database.name.toLowerCase();
    const description = database.description ? database.description.toLowerCase() : '';
    return name.includes(search) || description.includes(search);
  });
});

// 切换下拉框状态
const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value;
  if (isDropdownOpen.value) {
    searchText.value = '';
  }
};

// 选择数据库
const selectDatabase = (database: DatabaseTypeItem) => {
  emit('update:modelValue', database.name);
  emit('change', database);
  isDropdownOpen.value = false;
};

// 获取当前选中的数据库
const selectedDatabase = computed(() => {
  return props.modelValue;
});

// 组件生命周期钩子
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.database-selector {
  width: 100%;
  position: relative;
  font-family: Arial, sans-serif;
}

/* 选择框样式 */
.selector-dropdown {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 8px 12px;
  min-height: 42px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #fff;
}

.selector-dropdown:hover, .selector-dropdown.is-open {
  border-color: #40a9ff;
}

.selected-database {
  display: flex;
  align-items: center;
  flex: 1;
}

.placeholder {
  color: #bfbfbf;
  font-size: 14px;
}

.dropdown-arrow {
  color: #bfbfbf;
  transition: transform 0.3s;
  margin-left: 8px;
}

.arrow-up {
  transform: rotate(180deg);
}

/* 下拉面板样式 */
.dropdown-panel {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  right: 0;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 400px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-box {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.search-box input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  font-size: 14px;
  background-color: #fff;
  outline: none;
  transition: all 0.3s;
}

.search-box input:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.search-box input::placeholder {
  color: #bfbfbf;
}

/* 数据库网格样式 */
.database-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  padding: 16px;
  overflow-y: auto;
  max-height: 320px;
}

.database-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
  background-color: #fff;
  border: 1px solid #f0f0f0;
}

.database-item:hover {
  background-color: #f5f5f5;
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.database-item.active {
  background-color: #e6f7ff;
  border-color: #91d5ff;
}

.database-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

.database-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.database-name {
  font-size: 13px;
  color: #333;
  margin-top: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}

/* 适配选中状态和下拉状态样式 */
.selector-dropdown.focus,
.selector-dropdown:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}
</style> 