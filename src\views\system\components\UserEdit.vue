<template>
  <a-modal 
    v-model:visible="showDialog"
    :title="`${props.user?.id ? '编辑' : '创建'}用户`" 
    @ok="handleOk" 
    @cancel="handleCancel" 
    :width="800"
    cancelText="取消" 
    okText="确定"
    :confirm-loading="isSubmit"
    :cancel-button-props="{ disabled: isSubmit }"
  >
    <div class="user-edit-container">
      <a-form
        ref="userFormRef"
        :model="userForm"
        name="basic"
        :label-col="{ span: 2 }"
        :wrapper-col="{ span: 22 }"
        autocomplete="off"
        :key="formKey"
      >
        <a-form-item
          label="用户名"
          name="username"
          :rules="[{ required: true, message: '请输入用户名', trigger: 'blur' }, { validator: checkUsername, trigger: 'blur' }]"
        >
          <a-input v-model:value="userForm.username" maxlength="50" />
        </a-form-item>
        <a-form-item
          label="昵称"
          name="nickname"
          :rules="[{ required: true, message: '请输入昵称' }]"
        >
          <a-input v-model:value="userForm.nickname" maxlength="50" />
        </a-form-item>
        <a-form-item
          label="邮箱"
          name="email"
          :rules="[{ required: true, message: '请输入邮箱', trigger: 'blur' },  { validator: checkEmail, trigger: 'blur' }]"
        >
          <a-input v-model:value="userForm.email" />
        </a-form-item>
        <a-form-item
          label="手机号"
          name="phone"
          :rules="[{ required: true, message: '请输入手机号', trigger: 'blur' }, { validator: checkPhone, trigger: 'blur' }]"
        >
          <a-input v-model:value="userForm.phone" maxlength="50" />
        </a-form-item>
        <a-form-item
          label="密码"
          name="password"
          :rules="[{ required: true, message: '请输入密码' }]"
        >
          <a-input type="password" v-model:value="userForm.password" :key="inputKey" maxlength="100" />
        </a-form-item>
        <a-form-item
          :label="userForm.statusBoolean ? '正常' : '停用'"
          name="status"
        >
          <a-switch v-model:checked="userForm.statusBoolean" />
        </a-form-item>
      </a-form>
      <UserRole :id="(props.user?.id as any)" ref="userRoleRef" />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue';
import type { PropType } from 'vue';
import { message } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';
import { isValidUsername, isValidPhoneNumber, isValidEmailManas, getNowDate } from '@/utils';
import type { SaveUserDTO, UserRoleItem } from '@/types/system';
import UserRole from './UserRole.vue';
import { cloneDeep } from 'lodash-es';
import * as api from '@/api/system';

const props = defineProps({
  user: {
    type: Object as PropType<SaveUserDTO>,
    default: false
  }
});

const emit = defineEmits([
  'refreshData'
]);

const userFormRef = ref();
const userRoleRef = ref();

const userForm = reactive<SaveUserDTO & { statusBoolean?: boolean, roles?: (string | number)[] }>({
  id: undefined,
  username: undefined,
  nickname: undefined,
  email: undefined,
  password: undefined,
  phone: undefined,
  status: 'enabled',
  statusBoolean: true,
  user_source: 0,
});
const inputKey = ref<number>(0);
const showDialog = ref<boolean>(false);
const isSubmit = ref<boolean>(false);
const formKey = ref<number>(0);

const checkUsername = (_rule: Rule, value: string) => {
  if (!value) {
    return Promise.reject('');
  }
  return isValidUsername(value) ? Promise.resolve() : Promise.reject('用户名格式错误');
}

const checkPhone = (_rule: Rule, value: string) => {
  if (!value) {
    return Promise.reject('');
  }
  return isValidPhoneNumber(value) ? Promise.resolve() : Promise.reject('手机号格式错误');
}

const checkEmail = (_rule: Rule, value: string) => {
  if (!value) {
    return Promise.reject('');
  }
  const res = isValidEmailManas(value);
  if (res.valid) {
    return Promise.resolve();
  } else {
    return Promise.reject((res as any).msg);
  }
}

const resetUserForm = () => {
  userForm.id = undefined;
  userForm.username = undefined;
  userForm.nickname = undefined;
  userForm.email = undefined;
  userForm.password = undefined;
  userForm.phone = undefined;
  userForm.status = 'enabled';
  userForm.statusBoolean = true;
  userForm.user_source = 0;
}

const handleCancel = () => {
  userRoleRef.value?.resetRoleList();
  showDialog.value = false;
  resetUserForm();
}

const handleOk = async () => {
  if (isSubmit.value) {
    return;
  }
  try {
    // 执行表单校验
    await userFormRef.value.validate();
  } catch (_error) {
    message.error('表单校验失败，请检查输入内容');
    return;
  }
  try {
    isSubmit.value = true;
    const formData  = cloneDeep(userForm);
    formData.status = formData.statusBoolean? 'enabled' : 'disabled';
    delete formData.statusBoolean;
    if (!props.user?.id) {
      const date = getNowDate();
      formData.create_time = date;
      formData.update_time = date;
      formData.roles = [];
      userRoleRef.value?.roleList.map((item: UserRoleItem) => {
        (formData.roles ??= []).push(item.id);
      })
    }
    if (props.user?.id) {
      await api.updateUser(formData)
    } else {
      await api.createUser(formData)
    }
    message.success(`${props.user?.id ? '更新' : '添加'}成功`);
    userRoleRef.value?.resetRoleList();
    emit('refreshData');
    showDialog.value = false;
    resetUserForm();
  } finally {
    isSubmit.value = false;
  }
}

const handleOpen = async () => {
  showDialog.value = true;
  inputKey.value ++;
  formKey.value ++; // 触发表单重新渲染
  await nextTick(); // 等待 DOM 更新
  if (props.user) {
    userForm.id = props.user.id;
    userForm.username = props.user.username;
    userForm.nickname = props.user.nickname;
    userForm.email = props.user.email;
    userForm.password = props.user.password;
    userForm.phone = props.user.phone;
    userForm.status = props.user.status;
    userForm.statusBoolean = props.user.status === 'enabled';
    userRoleRef.value?.fetchRoleList();
  }
}

defineExpose({
  handleOpen,
});
</script>

<style scoped lang="scss">

</style> 