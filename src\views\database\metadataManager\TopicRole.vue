<template>
  <a-modal 
    v-model:visible="showDialog"
    title="分配角色" 
    @ok="handleOk" 
    @cancel="handleCancel" 
    :width="800"
    cancelText="取消" 
    okText="确定">
    <div class="role-manage-container">
      <div class="tool-line">
        <div class ="right">
          <a-button type="primary" @click="handleOpenEdit">
            <plus-outlined /> 添加角色
          </a-button>
          <a-popconfirm
            title="确定要删除角色吗?"
            ok-text="确定"
            cancel-text="取消"
            :disabled="selectedRowKeys.length === 0"
            @confirm="deleteRole()"
          >
            <a-button type="primary" danger class="btn-item" :disabled="selectedRowKeys.length === 0" >批量删除</a-button>
          </a-popconfirm>
        </div>
      </div>
      <a-divider>角色列表</a-divider>
      <div class="role-list">
        <a-table
          :columns="columns"
          :data-source="roleList"
          :loading="loading"
          rowKey="id"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag color="green" v-if="record.status === 0">正常</a-tag>
              <a-tag color="red" v-else>停用</a-tag>
            </template>
            <template v-else-if="column.key === 'operation'">
              <a-space>
                <a-popconfirm
                  title="确定要删除此角色吗?"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="deleteRole(record)"
                >
                  <a class="delete-link">删除</a>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
      <TopicRoleDialog 
        ref="topicRoleDialogRef" 
        :roleIds="roleIds" 
        :id="props.id" 
        :connectConfigId="props.connectConfigId" 
        @refreshData="handleRefreshData()"/>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import TopicRoleDialog from './TopicRoleDialog.vue';
import type { UserRoleItem } from '@/types/system';
import type { TopicRoleItem } from '@/types/metaData';
import { useRouter } from 'vue-router';
import * as api from '@/api/metadata';

const router = useRouter();

const props = defineProps({
  id: {
    type: [Number, String],
  },
  connectConfigId: {
    type: [Number, String],
  }
});

// 表格列定义
const columns = [
  {
    title: '名称',
    dataIndex: 'role_name',
    key: 'role_name',
  },
  {
    title: '操作',
    key: 'operation',
    width: 150
  },
];

const roleIds = computed(() => {
  const ids: (number | string | undefined)[] = [];
  roleList.value.map((item) => {
    ids.push(item.role_id);
  });
  return ids;
})

const selectedRowKeys = ref<string[]>([]); // 选中的行键数组
const topicRoleDialogRef = ref();
const showDialog =  ref<boolean>(false);

const onSelectChange = (selectedRowKeysValue: string[]) => {
  selectedRowKeys.value = selectedRowKeysValue;
};

// 提示词列表和加载状态
const roleList = ref<UserRoleItem[]>([]);
const loading = ref(false);

// 获取提示词列表
const fetchRoleList = async () => {
  loading.value = true;
  try {
    const res = await api.getMetadataRoleList({ 
      topic_id: props.id || '', 
      connect_config_id: props.connectConfigId || '', 
    });
    roleList.value = res.data;
  } catch (error) {
    console.error('获取主题角色列表失败', error);
  } finally {
    loading.value = false;
  }
};

const resetRoleList = () => {
  roleList.value = [];
  selectedRowKeys.value = [];
}

const handleOpenEdit = () => {
  topicRoleDialogRef.value?.handleOpen();
};

// 删除提示词
const deleteRole = async (data?: TopicRoleItem) => {
  let ids: (string | number)[] = [];
  if (data) {
    ids.push(data.id);
  } else {
    ids = selectedRowKeys.value;
  }
  await api.deleteMetadataRole({ ids });
  message.success('删除成功');
  fetchRoleList();
};

const handleRefreshData = () => {
  fetchRoleList();
  selectedRowKeys.value = [];
}

const handleOk = () => {
  showDialog.value = false;
}

const handleCancel = () => {
  showDialog.value = false;
}

const handleOpen = () => {
  showDialog.value = true;
  fetchRoleList();
}

defineExpose({
  handleOpen,
  fetchRoleList,
  resetRoleList,
});
</script>

<style scoped>
.role-manage-container {
  padding: 24px;
}

.tool-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  margin-top: 10px;
  .right {
    display: flex;
    .btn-item {
    margin-left: 10px;
    }
  }
}

.role-list {
  margin-top: 16px;
}

.delete-link {
  color: #ff4d4f;
}
</style> 