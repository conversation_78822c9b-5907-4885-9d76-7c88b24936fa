<template>
  <div class="suggested-questions">
    <div class="suggested-item" v-for="question in getFilterList(questions || [])" @click="() => onClick(question)">
      {{ question }}
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';

defineProps({
  questions: {
    type: Array as PropType<Array<string>>,
    default: () => []
  }
})

const emits = defineEmits([
  'click'
]);

const getFilterList = (list: string[]) => {
  // 过滤空字符串
  return list.map(item => item)
}

const onClick = (value: string) => {
  emits('click', value)
}
</script>