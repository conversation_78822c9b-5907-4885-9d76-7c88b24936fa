<template>
  <ChatScrollView ref="scrollViewRef" :title="title" :pageLoading="pageLoading">
    <!-- <MessageBot :isProlog="true" :content="getPrologByRoute()" /> -->
    <!-- 建议问 -->
    <!-- <SuggestedQuestions :questions="getAppInfo().suggestedQuestion?.map(i => i.question)" @click="changeQuestion" /> -->

    <!-- 聊天消息记录 -->
    <template v-for="(message, index) in getMessageStore().messageList" :key="message.id">
      <!-- 用户 -->
      <MessageUser :content="message.user_input" @setInputMessage="setInputMessage" />
      <!--机器人-->
      <MessageBot>
        <Thinking v-if="index === getMessageStore().messageList.length - 1 && getMessageStore().loading" />
        <template v-if="message.answer||message.id||message.reasonerContent">
          <WebSearchResult v-if="message.webSearchResult && message.webSearchResult.length > 0"
                            :web-search-result="message.webSearchResult"></WebSearchResult>
          <p class="think" v-if="message.reasonerContent">
            {{ message.reasonerContent?.trim() }}
          </p>
          <div v-if="message?.topicName" class="topic-tip">数据分析主题：{{ message?.topicName }}</div>
          <md-rendered-msg :answer="message.answer || ''" :type="message.type"/>
          <chart-view :answer="message.answer || ''"
                      :messageId="message.id || (Number(index) + 1) + ''"
                      @chartTypeChange="(type) => handleChartTypeChange(message.id || '', type)"/>
          <echarts-msg style="margin-top: 20px;" v-if="message.echartsCode"
                        :code="message.echartsCode"></echarts-msg>
          <retriever-resource :message="message"></retriever-resource>
        </template>
        <!-- 点赞等 按钮组 -->
        <ChatActions 
          v-if="!(index === getMessageStore().messageList.length - 1 && getMessageStore().loading)" 
          :id="message.id || (Number(index) + 1) + ''"
          :content="getContentOnly(message.answer || '')" 
          :likes="message.feedback || undefined"
          @refreshFeedback="handleRefreshFeedback"
          :chartType="getChartTypeByMessageId(message.id || '')"
          :selectedParamId="(selectedParamId as any)"
        />
      </MessageBot>
      <!--猜你想问-->
      <!-- <SuggestedQuestions 
        v-if="getAppInfo()?.suggestedQuestionsAfterAnswer == 1 && message.retrieverResource && message.retrieverResource.length > 0"
        :questions="message.suggestedQuestions" @click="changeQuestion" /> -->
    </template>
  </ChatScrollView>
  <!-- 输入框 -->
  <ChatInput ref="chatInputRef" @sendMessageSuccess="scrollToBottom" @computedScroll="computedScroll"
             @paramsIdChange="handleParamsIdChange"/>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, provide, computed } from 'vue';
import ChatInput from '../components/chat-input.vue'
import RetrieverResource from "@/components/retriever-resource.vue";
import EchartsMsg from "@/components/echarts-msg.vue";
import MdRenderedMsg from "@/components/md-rendered-msg.vue";
import WebSearchResult from "@/components/web-search-result.vue";
import ChatActions from "@/components/chat-actions.vue";
import ChartView from '@/components/chart-view.vue';
import { useMessageDynamicStore } from '@/store/message_dynamic';
import { useRoute } from 'vue-router';
import { NEW_CONVERSATION } from '@/constant';
import ChatScrollView from '@/components/chat-scroll-view.vue';
import MessageBot from '@/components/message-bot.vue';
import MessageUser from '@/components/message-user.vue';
import Thinking from '@/components/thinking.vue';
// import SuggestedQuestions from "@/components/suggested-questions.vue";
import { getContentOnly, getPrologByRoute } from '@/utils';
import type { Feedback, Option } from '@/types/app';

defineProps({
  title: {
    type: String,
    default: ''
  }
})

// const chartType = ref('');
const chartTypes = ref<Record<string, string>>({});
const selectedParamId = ref<string | undefined>(undefined);
const pageLoading = ref<boolean>(true);
const route = useRoute()
const getConversationId = () => route.params.conversationId as string;

const getMessageStore = () => useMessageDynamicStore.getStore(getConversationId())

const chatInputRef = ref();
const scrollViewRef = ref();

const scrollToBottom = () => {
  scrollViewRef.value?.setInputContainerHeight();
  scrollViewRef.value?.scrollToBottom();
}

const computedScroll = () => {
  scrollViewRef.value?.setInputContainerHeight();
  scrollViewRef.value?.computedScroll();
}

const handleChartTypeChange = (messageId: string, type: string) => {
  console.log('chat', type);
  // chartType.value = type;
  chartTypes.value[messageId.toString()] = type;
}

const getChartTypeByMessageId = (messageId: string) => {
  return chartTypes.value[messageId.toString()] || '';
}

const handleParamsIdChange = (id: string) => {
  selectedParamId.value = id;
}

// 添加消息上下文提供逻辑
const conversationId = computed(() => getConversationId());
const provideMessageContext = (messageId: string) => {
  provide('messageId', messageId);
  provide('conversationId', conversationId);
};

watch(
  () => getMessageStore().messageList.length,
  async (val) => {
    if (val > 0) {
      pageLoading.value = false;
      scrollToBottom()
    }
  },
  { immediate: true },
)

watch(
  () => route.params,
  async (val) => {
    if (val.conversationId) {
      if (getMessageStore().messageList.length === 0) {
        await getMessageStore().getMessageList(val.conversationId as string)
        scrollToBottom()
      }
      scrollToBottom()
    }
  },
  { immediate: true, deep: true },
)

const setInputMessage = (value: string) => {
  getMessageStore().inputMessage = value;
}

const handleRefreshFeedback = (id: number, data?: Feedback) => {
  getMessageStore().messageList.forEach((item, index) => {
    if ((Number(item.id) === Number(id)) || ((Number(index) + 1) === Number(id))) {
      if (data) {
        item.feedback = data;
      } else {
        item.feedback = null;
      }
    }
  })
}

//点击开场白推荐问
const changeQuestion = (question: string, appCode: string, chatMode: string, modelName: string, selectParams: string, topic: string, topicList: Option[], datasource: string) => {
  chatInputRef.value.sendMessage(question, appCode, chatMode, modelName, selectParams, topic, topicList, datasource);
}

const newConversationSend = () => {
  // 如果是创建新对话，先获取缓存中的初始信息，发送消息后删除缓存
  const data = JSON.parse(localStorage.getItem(NEW_CONVERSATION) || '{}')
  if (data.message) {
    setTimeout(() => {
      changeQuestion(
        data.message as string,
        data.appInfo.app_code,
        data.conversationInfo.chat_mode,
        data.modelName,
        data.selectParams,
        data.topic,
        data.topicList,
        data.datasource,
      )
      localStorage.removeItem(NEW_CONVERSATION)
    }, 500);
  }
}

onMounted(() => {
  newConversationSend();
});

</script>
<style scoped lang="scss">
  .topic-tip {
    color: #286DFF;
    font-size: 14px;
    height: 32px;
    line-height: 32px;
    font-weight: bold;
  }
</style>