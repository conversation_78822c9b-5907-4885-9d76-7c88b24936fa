import { defineStore } from 'pinia'
import { ref } from 'vue';
import type { MessageVo, InputsData, Option } from "@/types/app";
import * as api from "@/api/chat";
import { fetchEventSource } from '@microsoft/fetch-event-source';
import  { WsDataTypeEnum as WsDataType } from "@/types/enum";
import { useConversationStore } from './conversation';
import { LOGIN_ACCESS_TOKEN } from '@/constant';
import router from '@/router';

export const createMessageStore = (key: string) => {
  return defineStore(key, () => {
    const conversationStore = useConversationStore();
    
    // 这是发送信息时候记录的appId和conversationId，在重命名的时候会用到，避免因页面切换造成的id错乱
    const appId = ref<string>('');
    const conversationId = ref<string>('');
  
    const inputMessage = ref('');
    const enableWebSearch = ref<boolean>(router.currentRoute.value.path.includes('ppt'));
    const loading = ref<boolean>(false);
    const messageList = ref<MessageVo[]>([]);

    const chartOptions = ref<Map<string, any>>(new Map());

    const ctrlAbout = ref();
  
    const setLoading = (val: boolean) => {
      loading.value = val;
    }
  
    const getMessageList = async (id?: string) => {
      if(!id) return
      const res = await api.getMessageList(id);
      messageList.value = [];
      let messageMap: any = {};
      for (let i = 0; i < (res.data as any).length; i++) {
        if ((res.data as any)[i].role !== 'ai') {
          if (!messageMap.hasOwnProperty((res.data as any)[i].order)) {
            messageMap[(res.data as any)[i].order] = {
              user_input: '',
              answer: '',
              feedback: (res.data as any)[i].feedback,
              id: (res.data as any)[i].order,
            }
          }
          if ((res.data as any)[i].role === 'human') {
            messageMap[(res.data as any)[i].order].user_input = (res.data as any)[i].context;
          } else if ((res.data as any)[i].role === 'view') {
            messageMap[(res.data as any)[i].order].answer = (res.data as any)[i].context;
          }
        } 
      }
      for (let i in messageMap) {
        messageList.value.push(messageMap[i]);
      }
    }
  
    const getMessageById = async (id: string) => {
      const message = messageList.value.find(item => item.requestId === id);
      return message;
    }
  
    const addMessage = (msg: MessageVo) => {
      messageList.value.push(msg);
    }
  
    const updateAnswer = (id: string, answer: MessageVo) => {
      console.log(id, answer)
    }

    const reset = () => {
      messageList.value = [];
      loading.value = false;
    }

    // 发送消息
    const sendMessage = async (options: {
      conversationId: string;
      value?: string;
      enableWebSearch?: boolean;
      inputs?: InputsData,
      appCode?: string,
      chatCode?: string,
      maxTokens?: number,
      modelName?: string,
      temperature?: number,
      selectParams?: string,
      topic?: string,
      topicList?: Option[],
      datasource?: string | number,
      sendMessageSuccess?: () => void,
      computedScroll: () => void
    }) => {
      if(loading.value) return;
      const _message = options.value || inputMessage.value || ((options.inputs && options.inputs.query) || '') || ((options.hasOwnProperty('inputs') && (options as any)?.inputs.var) ? (options as any)?.inputs.var : '');
      const _enableWebSearch = options.enableWebSearch || enableWebSearch.value;
      if (_message.trim() === '') return;

      setLoading(true)
      conversationId.value = options.conversationId;

      let form: MessageVo = {
        conv_uid: options.conversationId,
        user_input: _message.trim(),
        answer: undefined,
        enableWebSearch: _enableWebSearch || false,
        app_code: options.appCode,
        chat_mode: options.chatCode,
        max_new_tokens: options.maxTokens,
        model_name: options.modelName,
        temperature: options.temperature,
      }
      if (options.selectParams) {
        form.select_param = options.selectParams;
      }
      if (options.topic) {
        form.topic = options.topic;
      }
      if (options.datasource) {
        form.datasource_id = options.datasource;
      }
      if (options.inputs) {
        if (options.inputs.role && options.inputs.roles && options.inputs.file) {
          form.inputs = {
            role: options.inputs.role,
            roles: options.inputs.roles,
            file: options.inputs.file
          }
        } else {
          form.inputs = options.inputs;
        }
      }
      // 清空输入框
      inputMessage.value = '';
      //加入聊天消息
      addMessage(form)
      //执行发送成功的回调
      options.sendMessageSuccess && options.sendMessageSuccess()
      // 自动选主题
      if (options?.topicList && options?.topicList.length > 0) {
        let topicSelect: number | string = options.topic || '';
        if (Number(options.topic) === -1) {
          const res = await api.intentRecognition({
            datasource_id: options.datasource || '',
            topic: options.topic || '',
            select_input: _message.trim(),
          });
          topicSelect = Number(res.data);
          if (Number(res.data) === 0) {
            messageList.value[messageList.value.length - 1].topicName = '默认主题';
            options?.topicList.map(item => {
              if (item.label === '默认主题') {
                form.topic = item.value;
              }
            });
          }
          if (!messageList.value[messageList.value.length - 1].topicName) {
            options?.topicList.map(item => {
              if (Number(item.value) === Number(topicSelect)) {
                messageList.value[messageList.value.length - 1].topicName = item.label;
                form.topic = item.value;
              }
            });
          }
        } else {
          options?.topicList.map(item => {
            if (Number(item.value) === Number(options.topic)) {
              messageList.value[messageList.value.length - 1].topicName = item.label;
            }
          });
        }
      }
      //发送到服务器
      await postEventSource(form, options.computedScroll)
    };

    async function postEventSource(postData: MessageVo, computedScroll: () => void) {
      ctrlAbout.value = new AbortController()
      try {
        const apiUrl = import.meta.env.MODE === 'production' 
        ? `${import.meta.env.VITE_API_BASE_URL}/v1/chat/completions`
        : '/api/v1/chat/completions';

        const token = localStorage.getItem(LOGIN_ACCESS_TOKEN);

        await fetchEventSource(
          apiUrl, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`,
              },
              body: JSON.stringify(postData),
              signal: ctrlAbout.value.signal,
              openWhenHidden: true,
              async onopen(response) {
              },
              onclose() {
                setLoading(false);
              },
              onmessage(msg) {
                console.log(msg)
                if (msg.event !== '[START]' && msg.event !== '[END]' && msg.event !== 'tts') {
                  processAnswerData(msg.data, computedScroll)
                }
              },
              onerror(err) {
                stopReceive(); // 在错误发生时中止请求,不重试
                console.log(err);
                throw err
              }
            })
      } catch (error) {
        stopReceive(); // 在错误发生时中止请求
        console.error("请求发生错误:", error);
      }
    }

    function processAnswerData(newData: string, computedScroll: () => void) {
      try {
        // console.log(newData);

        let itemToUpdate = messageList.value[messageList.value.length - 1];
        // if (itemToUpdate.answer === newData || (itemToUpdate.answer + '```' === newData)) {
        //   setLoading(false);
        // }
        itemToUpdate.answer = newData;
        computedScroll && computedScroll();
      } catch (error) {
      }
    }

    const stopReceive = () => {
      ctrlAbout.value.abort();
      setLoading(false)
    }

    const setChartConfig = (key: string, encode: any) => {
      chartOptions.value.set(key, encode);
    };

    const getChartConfig = (key: string) => {
      return chartOptions.value.get(key);
    };
  
    return {
      conversationId,
      loading,
      setLoading,
      messageList,
      getMessageList,
      addMessage,
      updateAnswer,
      getMessageById,
      reset,
      sendMessage,
      inputMessage,
      enableWebSearch,
      stopReceive,
      setChartConfig,
      getChartConfig
    }
  })
};
