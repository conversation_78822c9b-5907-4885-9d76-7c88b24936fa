import { MockMethod } from 'vite-plugin-mock'

// 用户登录接口
const login = {
  url: '/api/user/login',
  method: 'post',
  response: ({ body }) => {
    const { username, password } = body
    
    if (username === 'admin' && password === 'admin') {
      return {
        success: true,
        err_code: null,
        err_msg: null,
        data: {
          token: 'mock-token-admin',
          username: 'admin',
          avatar: 'A',
          roles: ['admin']
        }
      }
    } else {
      return {
        success: false,
        err_code: 401,
        err_msg: '用户名或密码错误',
        data: null
      }
    }
  }
}

// 获取用户信息接口
const getUserInfo = {
  url: '/api/user/info',
  method: 'get',
  response: (request) => {
    const token = request.headers?.authorization?.replace('Bearer ', '')
    
    if (token === 'mock-token-admin') {
      return {
        success: true,
        err_code: null,
        err_msg: null,
        data: {
          id: '1',
          username: 'admin',
          avatar: 'A',
          roles: ['admin'],
          permissions: ['*:*:*'] // 所有权限
        }
      }
    } else {
      return {
        success: false,
        err_code: 401,
        err_msg: 'token无效或已过期',
        data: null
      }
    }
  }
}

// 退出登录接口
const logout = {
  url: '/api/user/logout',
  method: 'post',
  response: () => {
    return {
      success: true,
      err_code: null,
      err_msg: null,
      data: null
    }
  }
}

export default [
  login,
  getUserInfo,
  logout
] as MockMethod[]
