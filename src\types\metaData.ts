import type {DatabaseTypeParam} from "@/types/database.ts";

export type datasourcesResponse = {
    success: boolean;
    err_code: number | string;
    err_msg: string;
    data: [
        {
            db_name: string;
            db_data: [
                {
                    id: number | string;
                    connect_config_id: number | string;
                    db_name: string;
                    tables_name: string;
                    columns_name: string;
                    columns_type: string;
                    columns_remark: string;
                },
            ];
        },
    ];
};

export type datasourcesItem = {
    tables_name: string;
    tables_columns: {
        connect_config_id: number | string;
        db_name: string;
        db_type: string;
        tables_name: string;
        columns_name: string;
        columns_type: string;
        columns_remark: string;
    }[];
}

export interface tableColumnItem {
    connect_config_id: number | string;
    db_name: string;
    db_type: string;
    tables_name: string;
    columns_name: string;
    columns_type: string;
    columns_remark: string;
}
// export interface datasourcesItem {
//     tables_name: string;
//     tables_columns: {
//         connect_config_id: number | string;
//         db_name: string;
//         db_type: string;
//         tables_name: string;
//         columns_name: string;
//         columns_type: string;
//         columns_remark?: string;
//     }
// }

export type MetadataRequest = {
    datasource_id: number | string;
    topic: number | string;
    sort_by?: string | null;
    order?: string | null;
    tables_name_search?: string | null;
    columns_name_search?: string | null;
};

export interface MetadataResponse {
    id: number | string | null;
    topic: number | string;
    topic_id: number | string;
    db_name: string;
    tables_name: string;
    tables_remark: string;
    columns_name: string;
    columns_type: string;
    columns_remark: string;
    connect_config_id: string;
    foreign_key_id: string;
    foreign_key_table: string;
    foreign_key_cascade?: string[];
}

export type metadataResponse = {
    success: boolean;
    err_code: number | string;
    err_msg: string;
    data: [
        {
            id: number | string;
            connect_config_id: number | string;
            db_name: string;
            tables_name: string;
            columns_name: string;
            columns_type: string;
            columns_remark: string;
            foreign_key_table: string;
            foreign_key_id: number | string;
        },
    ];
};

export type PostMetadataParams = {
    topicId: number | string;
    data: [
        {
            id: number | string;
            connect_config_id: number | string;
            db_type: string;
            db_name: string;
            tables_name: string;
            tables_remark: string;
            columns_name: string;
            columns_type: string;
            columns_remark: string;
            foreign_key_table?: string;
            foreign_key_id?: number | string;
        },
    ];
};

export type UpdateMetadataParams = {
    id?: number | string;
    connect_config_id?: number | string;
    db_name?: string;
    tables_name?: string;
    tables_remark?: string;
    columns_name?: string;
    columns_type?: string;
    columns_remark?: string;
    foreign_key_table?: string;
    foreign_key_id?: number | string;
    topic_id?: number | string;
};

export type DeleteMetadataParams = {
    ids: string[];
};

export interface topicItem {
    id?: number;
    connect_config_id?: number | string;
    topic_name: string | undefined;
    topic_desc: string | undefined;
}

export interface deleteTopicRequest {
    ids: (number | undefined)[];
}

export interface asyncMetaDataItem {
    datasource_id: number | string;
    topic: number | string;
}

export interface TableColumn {
    title: string;
    dataIndex: string;
    width?: string | number;
    editable?: boolean;
    align?: 'left' | 'right' | 'center';
    fixed?: 'left' | 'right';
    customRender?: (data: {
        text: any;
        record: Record<string, any>;
        index: number;
    }) => any;
}

export type TableColumnKey = keyof EditableRowData;
export interface EditableRowData {
    columns_name: string;
    columns_type: string;
    columns_remark: string;
}

export interface AddColumnSexegesisDTO {
    datasource_id: string | number;
    topic: string | number;
}

export interface GetMetadataRoleListDTO {
    // role_id: string | number;
    connect_config_id: string | number;
    topic_id: string | number;
}

export interface AddMetadataRoleDTO {
    role_ids: (string | number)[];
    connect_config_id: string | number;
    topic_id: string | number;
}

export interface DeleteMetadataRoleDTO {
    ids: (string | number)[];
}

export interface TopicRoleItem {
    id: string | number;
    role_id: string | number;
    role_name: string;
    topic_id: string | number;
    connect_config_id: string | number;
}

export interface GetForeignKeyVO {
    table_name: string;
    table_remark: string;
    primary_keys: string[];
}