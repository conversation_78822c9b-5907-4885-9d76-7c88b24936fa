

interface TokenDto {
    clientId: string;
    grantType: string;
    appCode:string
}

interface TokenVo {
    scope: string;
    openid: string;
    access_token: string;
    refresh_token: string;
    expire_in: number;
    refresh_expire_in: string;
    client_id: string;
}

interface InputsData {
    [key: string]: string; // 允许任意字符串作为键，值为字符串
}

interface AppVo {
    id?: string;
    appName?: string;
    appDesc?: string;
    code?: string;
    enableSite?: boolean;
    enableApi?: boolean;
    datasetIds?: string[];
    prolog?: string;
    promptText?: string;
    appType?: string;
    queryVarId?: number;
    suggestedQuestion?: { id: string, appId: string, question: string }[],
    suggestedQuestionsAfterAnswer?: number
    promptVariable?: PromptVariableVO[]
    enableWebSearch?:boolean
}


interface PromptVariableVO {

    id: string | number;
    appId: string | number;

    /**
     * 变量key
     */
    varKey: string;

    /**
     * key对应的字段名称
     */
    varName: string;

    /**
     * 是否必填，0-不必填(默认)，1-必填
     */
    isRequired: number;

    /**
     * 类型：text, paragraph, dropdown
     */
    type: string;

    /** 判断cell编辑用 */
    editingCell?: string | null;
}

interface AppInfoDTO {
    chat_scene: string;
    app_code?: string;
}

interface AppInfoVO {
    app_code: string;
    app_describe: string;
    app_name: string;
    created_at: string;
    hot_value: number;
    icon: string;
    is_collected: string;
    keep_end_rounds: number;
    keep_start_rounds: number;
    language: string;
    owner_avatar_url: string;
    owner_name: string;
    published: string;
    sys_code: string;
    team_mode: string;
    updated_at: string;
    user_code: string;
    user_icon: string;
    user_name: string;
}

interface GetParamsDTO {
    chat_mode: string;
}

interface GetParamsVO {
    param: string;
    type: string;
    id: number;
}

interface AddFeedbackDTO {
    conv_uid: string;
    feedback_type: string;
    message_id: string;
    reason_types?: string[];
    remark?: string;
}

interface CancelFeedbackDTO {
    conv_uid: string;
    message_id: string;
}

interface GetMessageListVO {
    context: string;
    feedback: Feedback;
    model_name: string;
    order: number;
    role: string;
    time_stamp: number;
}

interface ConversaionDbDTO {
    chat_mode: string;
}

interface ConversationDbVO {
    app_code?: string;
    chat_mode?: string;
    conv_uid?: string;
    model_name?: string;
    select_param?: string;
    topic?: string;
    sys_code?: string;
    user_input?: string;
    user_name?: string;
}

interface conversationVo extends PageQuery {
    id: string;
    appId?: string;
    conversationId?: string;
    conversationTitle?: string;
    userId?: any;
    topping?: boolean;
    createBy?: number;
    createByName?: string;
    createTime?: string;
    msgNum?: number;
    showPopover?: boolean;
    selected?: boolean;
    inputs?: string;
}

interface AntdTableColumn {
    title?: string;
    dataIndex?: string;
    key?: string;
}

interface Feedback {
    conv_uid: string;
    feedback_type: string;
    gmt_created: string;
    gmt_modified: string;
    id: number;
    knowledge_space: null;
    message_id: string;
    ques_type: string;
    question: string;
    reason: string[];
    reason_type: string;
    reason_types: string[];
    remark: string;
    score: number;
    user_code: string;
    user_name: string;
}

interface MessageVo {
    answer?: string;
    answerPpt?: string;
    createTime?: string;
    id?: string;
    appId?: string | number;
    requestId?: string;
    msgToken?: number;
    reDatetime?: string;
    type?: string;
    conversationId?: string;
    query?: string;
    thumbsUp?: number;
    thumbsDown?: number;
    echartsCode?: string;
    sqlData?: string;
    retrieverResource?: RetrieverResourceVO[];
    suggestedQuestions?: string[];
    inputs?: object;
    reasonerContent?: string;
    reasoner?: string;
    enableWebSearch?: boolean;
    webSearchResult?: WebSearchResultVO[],
    showGeneratePpt?: boolean;
    likes?: number;
    pptKey?: string;
    loading?: boolean;
    remark?: string;
    conv_uid?: string;
    user_input?: string;
    app_code?: string;
    chat_mode?: string;
    max_new_tokens?: number;
    model_name?: string;
    temperature?: number;
    context?: string;
    feedback?: Feedback | null;
    model_name?: string;
    select_param?: string;
    order?: number;
    role?: string;
    time_stamp?: number;
    topic?: string;
    topicList?: Option[];
    datasource_id?: string | number;
    topicName?: string;
}

interface RetrieverResourceVO {
    /**
     *
     */
    id: string | number;

    /**
     * 消息id
     */
    messageId: string | number;

    /**
     * 排序
     */
    sort: number;

    /**
     * 知识库id
     */
    datasetId: string | number;

    /**
     * 知识库名称
     */
    datasetName: string;

    /**
     * 文档id
     */
    docId: string | number;

    /**
     * 文档名称
     */
    docName: string;

    /**
     * 分段id
     */
    segmantId: string | number;

    /**
     * 分数
     */
    score: number;

    /**
     * 命中次数
     */
    hitCount: number;

    /**
     * 内容
     */
    content: string;

    /**
     * 字数
     */
    wordCount: number;

    /**
     * 向量哈希
     */
    indexNodeHash: string;

    /**
     * 段落编号
     */
    segmentSno: number;

}

interface PromptVariableVO {
    /**
     *
     */
    id: string | number;

    /**
     *
     */
    appId: string | number;

    /**
     * 变量key
     */
    varKey: string;

    /**
     * key对应的字段名称
     */
    varName: string;

    /**
     * 是否必填，0-不必填(默认)，1-必填
     */
    isRequired: number;

    /**
     * 类型：text, paragraph, dropdown
     */
    type: string;

    /** 判断cell编辑用 */
    editingCell?: string | null;
}


interface PromptVariableVO {
    /**
     *
     */
    id: string | number;

    /**
     *
     */
    appId: string | number;

    /**
     * 变量key
     */
    varKey: string;

    /**
     * key对应的字段名称
     */
    varName: string;

    /**
     * 是否必填，0-不必填(默认)，1-必填
     */
    isRequired: number;

    /**
     * 类型：text, paragraph, dropdown
     */
    type: string;

    /** 判断cell编辑用 */
    editingCell?: string | null;
}

interface PromptVariableForm {
    /**
     *
     */
    id?: string | number;

    /**
     *
     */
    appId?: string | number;

    /**
     * 变量key
     */
    varKey?: string;

    /**
     * key对应的字段名称
     */
    varName?: string;

    /**
     * 是否必填，0-不必填(默认)，1-必填
     */
    isRequired?: number;

    /**
     * 类型：text, paragraph, dropdown
     */
    type?: string;

}

interface PromptVariableQuery {

    /**
     *
     */
    appId?: string | number;

    /**
     * 变量key
     */
    varKey?: string;

    /**
     * key对应的字段名称
     */
    varName?: string;

    /**
     * 是否必填，0-不必填(默认)，1-必填
     */
    isRequired?: number;

    /**
     * 类型：text, paragraph, dropdown
     */
    type?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}

interface UploadVO {
    fileName?: string;
    ossId: string;
    url: string;
}

interface InputsData {
    var?: string,
    role?: string,
    roles?: string,
    file?: string,
    query?: string,
}

/** websocket传输流数据的类型 */
export enum WsDataType {
    // 表示后端已收到前端发来的query msg
    ACK = 'acknowledgment',
    // 表示生产回答已完成，返回数据是生产完成后的文本
    TEXT_MSG = 'textMessage',
    // 表示返回数据是正在生成的流内容
    STREAM_DATA = "streamingData",
    // 表示返回数据是报错信息
    ERR = "error",
    // 表示返回数据是报错信息
    SQLDATA = "sqlData",
    //Echarts代码
    ECHARTS_CODE = "echartsCode",
    // 表示返回数据是追问
    TEXT_QA = "questionsAfter"
}

interface OssInfo {
    createBy: string;
    createByName: null | string;
    createTime: string;
    fileName: string;
    fileSuffix: string;
    originalName: string;
    ossId: string;
    service: string;
    url: string;
}

interface RegisterDTO {
    tenantId: string;
    userType: string;
    clientId: string;
    phonenumber: string;
    username: string;
    nickName: string;
    password: string;
    remark: string;
    code: string;
    uuid: string;
}

interface CaptchaVO {
    captchaEnabled: boolean;
    img: string;
    uuid: string;
}

interface AccountLoginDTO {
    clientId?: string;
    grantType?: string;
    tenantId?: string;
    username?: string;
    password?: string;
    code?: string;
    uuid?: string;
    smsCode?: string;
    phonenumber?: string;
}

interface AccountLoginVO {
    access_token: string;
    client_id: string;
    expire_in: number;
    openid: string;
    refresh_expire_in: number;
    refresh_token: string;
    scope: string;
}

interface SmsCodeDTO {
    phonenumber: string;
}

interface RoleItem {
    createTime: string;
    dataScope: string;
    deptCheckStrictly: string;
    flag: boolean;
    menuCheckStrictly: string;
    remark: string;
    roleId: number;
    roleKey: string;
    roleName: string;
    roleSort: number;
    status: string;
    superAdmin: boolean;
}

interface UserInfo {
    avatar: string;
    createTime: string;
    dept: string;
    deptId: string;
    email: string;
    loginDate: string;
    loginIp: string;
    nickName: string;
    phonenumber: string;
    postIds: string;
    remark: string;
    roleId: number;
    roleIds: RoleItem[];
    sex: string;
    status: string;
    tenantId: string;
    userId: string;
    userName: string;
    userType: string;
}

interface UserProfileVO {
    postGroup: string;
    roleGroup: string;
    user: UserInfo;
}

interface Option {
    value: string;
    label: string;
    children?: Option[];
}

interface PaginationDTO {
    page: number;
    page_size: number;
}

interface GetMetadataTopicListVO {
    connect_config_id: string;
    id: number;
    topic_desc: string;
    topic_name: string;
}

interface IntentRecognitionDTO {
    datasource_id: number | string;
    topic: number | string;
    select_input: string;
}

interface DeleteConversationDTO {
    con_uid: string;
}

interface saveChatDTO {
    name: string;
    type: string;
    sql: string;
    connect_config_id: string;
    chart_config: string;
}