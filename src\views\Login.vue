<template>
  <div class="login-container">
    <div class="login-form">
      <h1 class="login-title">DataGPT</h1>
      <a-form
        :model="formState"
        name="loginForm"
        @finish="onFinish"
        autocomplete="off"
        layout="vertical"
      >
        <a-form-item
          label="用户名"
          name="username"
          :rules="[{ required: true, message: '请输入用户名!' }]"
        >
          <a-input v-model:value="formState.username" placeholder="请输入用户名" size="large" />
        </a-form-item>

        <a-form-item
          label="密码"
          name="password"
          :rules="[{ required: true, message: '请输入密码!' }]"
        >
          <a-input-password v-model:value="formState.password" placeholder="请输入密码" size="large" />
        </a-form-item>

        <a-form-item>
          <a-button type="primary" html-type="submit" size="large" block :loading="loading">
            登录
          </a-button>
        </a-form-item>
      </a-form>
      <!-- <div class="login-tip">
        <p>用户名：admin，密码：admin</p>
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { login } from '../api/user';
import type { LoginParams } from '../types/user';
import type { ApiResponse } from '../types/api';
import { useUserStore } from '@/store/user';
import * as api from '@/api/user';

const router = useRouter();
const loading = ref(false);
const userStore = useUserStore();

interface FormState {
  username: string;
  password: string;
}

const formState = reactive<FormState>({
  username: '',
  password: ''
});

const onFinish = async (values: FormState) => {
  if (loading.value) {
    return;
  }
  try {
    loading.value = true;
    userStore.login(values.username, values.password);
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: left;
  height: 100vh;
  width: 100vw;
  background-color: #f0f2f5;
  background-image: radial-gradient(circle at center, rgba(255,255,255,0.7) 0%, rgba(240,242,245,0.9) 100%);
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
}

.login-form {
  width: 100%;
  max-width: 400px;
  padding: 40px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.1);
}

.login-title {
  text-align: center;
  margin-bottom: 30px;
  color: #1890ff;
  font-size: 28px;
  font-weight: bold;
}

.login-tip {
  margin-top: 16px;
  text-align: center;
  color: #999;
  font-size: 14px;
}
</style> 