<template>
  <common-chart-settings
    :options="props.options"
    chart-type="metric"
    @update="updateOptions"
  >
    <!-- 样式配置 -->
    <template #style-settings>
      <!-- 指标样式设置 -->
      <a-divider orientation="left">指标样式</a-divider>
      
      <a-form-item label="指标名称">
        <a-input v-model:value="chartConfig.name" placeholder="请输入指标名称" />
      </a-form-item>
      
      <a-form-item label="显示前缀">
        <a-switch v-model:checked="showPrefix" @change="updateFormat" />
      </a-form-item>
      
      <a-form-item label="前缀内容" v-if="showPrefix">
        <a-input v-model:value="prefix" placeholder="例如: ¥、$" @change="updateFormat" />
      </a-form-item>
      
      <a-form-item label="显示后缀">
        <a-switch v-model:checked="showSuffix" @change="updateFormat" />
      </a-form-item>
      
      <a-form-item label="后缀内容" v-if="showSuffix">
        <a-input v-model:value="suffix" placeholder="例如: %、元" @change="updateFormat" />
      </a-form-item>
      
      <a-form-item label="数值精度">
        <a-input-number
          v-model:value="precision"
          :min="0"
          :max="10"
          @change="updateFormat"
        />
      </a-form-item>
      
      <a-form-item label="数值大小">
        <a-input-number
          v-model:value="chartConfig.valueStyle.fontSize"
          :min="12"
          :max="100"
          :step="2"
          @change="updateConfig"
        />
      </a-form-item>
      
      <a-form-item label="数值颜色">
        <a-input
          v-model:value="chartConfig.valueStyle.color"
          placeholder="例如: #1890ff"
          :addon-before="'颜色'"
          @change="updateConfig"
        />
      </a-form-item>
      
      <a-form-item label="文本对齐">
        <a-select v-model:value="chartConfig.align" @change="updateConfig">
          <a-select-option value="left">左对齐</a-select-option>
          <a-select-option value="center">居中</a-select-option>
          <a-select-option value="right">右对齐</a-select-option>
        </a-select>
      </a-form-item>
      
      <a-form-item label="显示趋势">
        <a-switch v-model:checked="chartConfig.showTrend" />
      </a-form-item>
      
      <a-form-item label="比较基准值" v-if="chartConfig.showTrend">
        <a-input-number
          v-model:value="chartConfig.baseValue"
          @change="updateConfig"
          style="width: 100%"
        />
      </a-form-item>

      <!-- 颜色配置 -->
      <a-divider orientation="left">颜色与背景</a-divider>
      <a-form-item label="背景颜色">
        <a-input 
          v-model:value="chartConfig.backgroundColor" 
          placeholder="例如: #ffffff" 
          :addon-before="'颜色'"
          @change="updateConfig"
        />
      </a-form-item>

      <!-- 动画配置 -->
      <a-divider orientation="left">动画设置</a-divider>
      <a-form-item label="开启动画">
        <a-switch v-model:checked="chartConfig.animation" @change="updateConfig" />
      </a-form-item>
      <a-form-item v-if="chartConfig.animation" label="动画时长">
        <a-input-number 
          v-model:value="chartConfig.animationDuration" 
          :min="100" 
          :max="5000"
          :step="100"
          @change="updateConfig" 
        />
      </a-form-item>

      <!-- 提示框配置 -->
      <a-divider orientation="left">提示框设置</a-divider>
      <a-form-item label="显示提示框">
        <a-switch v-model:checked="tooltipShow" @change="updateTooltip" />
      </a-form-item>
      <a-form-item v-if="tooltipShow" label="提示框触发">
        <a-select v-model:value="tooltipTrigger" @change="updateTooltip">
          <a-select-option value="item">数据项</a-select-option>
          <a-select-option value="axis">坐标轴</a-select-option>
          <a-select-option value="none">无</a-select-option>
        </a-select>
      </a-form-item>
    </template>
  </common-chart-settings>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import CommonChartSettings from './CommonChartSettings.vue';
import type { ChartOptions } from '@/types/chart';

const props = defineProps({
  options: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update']);

// 格式化设置
const showPrefix = ref(false);
const showSuffix = ref(false);
const prefix = ref('');
const suffix = ref('');
const precision = ref(2);

// 提示框设置
const tooltipShow = ref(true);
const tooltipTrigger = ref('item');

// 直接访问config部分的计算属性
const chartConfig = computed({
  get: () => {
    return props.options.config || getDefaultOptions().config;
  },
  set: (newConfig) => {
    updateConfig(newConfig);
  }
});

// 获取默认配置
const getDefaultOptions = (): any => {
  return {
    dataType: 'static',
    data: {
      columns: [],
      values: []
    },
    config: {
      type: 'metric',
      title: {
        text: '',
        show: false
      },
      name: '指标名称',
      value: 0,
      prefix: '',
      suffix: '',
      precision: 2,
      align: 'center',
      showTrend: false,
      baseValue: 0,
      valueStyle: {
        fontSize: 36,
        color: '#1890ff'
      },
      backgroundColor: '#ffffff',
      animation: true,
      animationDuration: 1000,
      tooltip: {
        show: true,
        trigger: 'item'
      }
    }
  };
};

// 更新配置
const updateOptions = (newOptions: ChartOptions) => {
  emit('update', newOptions);
};

// 更新config部分
const updateConfig = (newConfig: any) => {
  const updatedOptions = {
    ...props.options,
    config: newConfig
  };
  emit('update', updatedOptions);
};

// 更新格式设置
const updateFormat = () => {
  const config = { ...chartConfig.value };
  
  if (showPrefix.value) {
    config.prefix = prefix.value;
  } else {
    config.prefix = '';
  }
  
  if (showSuffix.value) {
    config.suffix = suffix.value;
  } else {
    config.suffix = '';
  }
  
  config.precision = precision.value;
  
  updateConfig(config);
};

// 提示框相关
const updateTooltip = () => {
  chartConfig.value.tooltip = {
    ...(chartConfig.value.tooltip || {}),
    show: tooltipShow.value,
    trigger: tooltipTrigger.value
  };
  updateConfig(chartConfig.value);
};

// 初始化组件
onMounted(() => {
  // 确保必要的属性存在
  const config = { ...chartConfig.value };
  
  if (!config.title) {
    config.title = { text: '', show: false };
  }
  
  if (!config.valueStyle) {
    config.valueStyle = { fontSize: 36, color: '#1890ff' };
  }
  if (!config.backgroundColor) {
    config.backgroundColor = '#ffffff';
  }
  if (config.animation === undefined) {
    config.animation = true;
  }
  if (!config.animationDuration) {
    config.animationDuration = 1000;
  }
  if (!config.tooltip) {
    config.tooltip = { show: true, trigger: 'item' };
  }
  tooltipShow.value = config.tooltip.show !== false;
  tooltipTrigger.value = config.tooltip.trigger || 'item';
  updateConfig(config);
});
</script>

<style scoped>
.chart-settings-container {
  padding: 0 10px;
}

.settings-footer {
  padding: 10px 0;
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.size-display {
  text-align: right;
  color: #999;
  margin-top: -10px;
}

:deep(.ant-collapse-header) {
  font-weight: 500;
}

:deep(.ant-form-item) {
  margin-bottom: 14px;
}
</style> 