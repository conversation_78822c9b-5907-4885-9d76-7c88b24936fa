import request from '../utils/request';
import type { ApiResponse } from '../types/api';
import type { ModelItem , ModelTypeItem, ModelOperation} from '../types/model';

/**
 * 获取模型列表
 */
export function getModelList(): Promise<ApiResponse<ModelItem[]>> {
  return request.get('/v2/serve/model/models');
}

/**
 * 获取所有模型类型配置
 */
export function getModelTypes(): Promise<ApiResponse<ModelTypeItem[]>> {
  return request.get('/v2/serve/model/model-types');
}

/**
 * 创建自定义模型
 * @param data 模型配置数据
 */
export function createModel(data: any): Promise<ApiResponse<any>> {
  return request.post('/v2/serve/model/models', data);
}

/**
 * 启动模型
 * @param data 模型信息
 */
export function startModel(data: ModelOperation): Promise<ApiResponse<any>> {
  return request.post('/v2/serve/model/models/start', data);
}

/**
 * 停止模型
 * @param data 模型信息
 */
export function stopModel(data: ModelOperation): Promise<ApiResponse<any>> {
  return request.post('/v2/serve/model/models/stop', data);
}

/**
 * 停止并删除模型
 * @param data 模型信息
 */
export function stopAndDeleteModel(data: ModelOperation): Promise<ApiResponse<any>> {
  // 添加删除标记
  return request.post('/v2/serve/model/models/stop', {
    ...data,
    delete_after: true
  });
} 