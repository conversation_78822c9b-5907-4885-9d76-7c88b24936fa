type CommonZhType = {
  [key: string]: string;
};

export const CommonZh: CommonZhType = {
  Knowledge_Space: '知识库',
  space: '知识库',
  Vector: '向量',
  Owner: '创建人',
  Count: '文档数',
  File_type_Invalid: '文件类型错误',
  Knowledge_Space_Config: '知识库配置',
  Choose_a_Datasource_type: '知识库类型',
  Segmentation: '分片',
  No_parameter: '不需要配置分片参数',
  Knowledge_Space_Name: '知识库名称',
  Please_input_the_name: '请输入名称',
  Please_input_the_owner: '请输入创建人',
  Please_select_file: '请至少选择一个文件',
  Description: '描述',
  Storage: '存储类型',
  Domain: '领域类型',
  Please_input_the_description: '请输入描述',
  Please_select_the_storage: '请选择存储类型',
  Please_select_the_domain_type: '请选择领域类型',
  Next: '下一步',
  the_name_can_only_contain: '名称只能包含数字、字母、中文字符、-或_',
  Text: '文本',
  'Fill your raw text': '填写您的原始文本',
  URL: '网址',
  Fetch_the_content_of_a_URL: '获取 URL 的内容',
  Document: '文档',
  Upload_a_document: '上传文档，文档类型可以是PDF、CSV、Text、PowerPoint、Word、Markdown、Zip',
  Name: '名称',
  Text_Source: '文本来源（可选）',
  Please_input_the_text_source: '请输入文本来源',
  Sync: '同步',
  Back: '上一步',
  Finish: '完成',
  Web_Page_URL: '网页网址',
  Please_input_the_Web_Page_URL: '请输入网页网址',
  Select_or_Drop_file: '选择或拖拽文件',
  Documents: '文档',
  Chat: '对话',
  Add_Datasource: '添加数据源',
  View_Graph: '查看图谱',
  Arguments: '参数',
  Type: '类型',
  Size: '切片',
  Last_Sync: '上次同步时间',
  Status: '状态',
  Result: '结果',
  Details: '明细',
  Delete: '删除',
  Operation: '操作',
  Submit: '提交',
  close: '关闭',
  Chunks: '切片',
  Content: '内容',
  Meta_Data: '元数据',
  Please_select_a_file: '请上传一个文件',
  Please_input_the_text: '请输入文本',
  Embedding: '嵌入',
  topk: 'TopK',
  the_top_k_vectors: '基于相似度得分的前 k 个向量',
  recall_score: '召回分数',
  Set_a_threshold_score: '设置相似向量检索的阈值分数',
  recall_type: '召回类型',
  model: '模型',
  A_model_used: '用于创建文本或其他数据的矢量表示的模型',
  Automatic: '自动切片',
  Process: '切片处理',
  Automatic_desc: '自动设置分割和预处理规则。',
  chunk_size: '块大小',
  The_size_of_the_data_chunks: '处理中使用的数据块的大小',
  chunk_overlap: '块重叠',
  The_amount_of_overlap: '相邻数据块之间的重叠量',
  scene: '场景',
  A_contextual_parameter: '用于定义使用提示的设置或环境的上下文参数',
  template: '模板',
  structure_or_format: '预定义的提示结构或格式，有助于确保人工智能系统生成与所需风格或语气一致的响应。',
  max_token: '最大令牌',
  max_iteration: '最大迭代',
  concurrency_limit: '并发限制',
  The_maximum_number_of_tokens: '提示中允许的最大标记或单词数',
  Theme: '主题',
  database_type: '数据库类型',
  edit_database: '编辑数据源',
  add_database: '添加数据源',
  update_success: '更新成功',
  update_failed: '更新失败',
  create_success: '创建成功',
  create_failed: '创建失败',
  please_select_database_type: '请选择数据库类型',
  select_database_type: '选择数据库类型',
  description: '描述',
  input_description: '请输入描述',
  Port: '端口',
  Username: '用户名',
  Password: '密码',
  Remark: '备注',
  Edit: '编辑',
  Database: '数据库',
  Data_Source: '数据中心',
  Close_Sidebar: '收起',
  Show_Sidebar: '展开',
  language: '语言',
  choose_model: '请选择一个模型',
  data_center_desc: 'DB-GPT支持数据库交互和基于文档的对话，它还提供了一个用户友好的数据中心管理界面。',
  create_database: '创建数据库',
  create_knowledge: '创建知识库',
  create_flow: '创建工作流',
  path: '路径',
  model_manage: '模型管理',
  create_model: '创建模型',
  model_select_tips: '请选择一个模型',
  submit: '提交',
  stop_model: '停止模型',
  stop_model_success: '模型停止成功',
  start_model: '启动模型',
  start_model_success: '启动模型成功',
  stop_and_delete_model: '停止并删除模型',
  stop_and_delete_model_success: '停止并删除模型成功',
  confirm_start_model: '确定要启动模型? 模型名：',
  confirm_stop_model: '确定要停止模型? 模型名：',
  confirm_stop_and_delete_model: '确定要停止并删除模型? 模型名：',
  worker_type_select_tips: '请选择一个worker类型',
  model_select_worker_type: '选择worker类型',
  download_model_tip: '请先下载模型！',
  start_model_failed: '启动模型失败',
  provider_select_tips: '请选择一个模型提供商',
  model_select_provider: '请选择一个模型提供商',
  model_please_input_name: '请输入模型名称',
  model_select_or_input_model: '选择或输入模型名称',
  model_deploy_name: '模型名称',
  Plugins: '插件列表',
  try_again: '刷新重试',
  no_data: '暂无数据',
  Prompt: '提示词',
  Open_Sidebar: '展开',
  verify: '确认',
  cancel: '取消',
  Edit_Success: '编辑成功',
  Add: '新增',
  Add_Success: '新增成功',
  Error_Message: '出错了',
  Please_Input: '请输入',
  Prompt_Info_Scene: '场景',
  Prompt_Info_Sub_Scene: '次级场景',
  Prompt_Info_Name: '名称',
  Prompt_Info_Content: '内容',
  Public: '公共',
  Private: '私有',
  Lowest: '渣渣',
  Missed: '没理解',
  Lost: '答不了',
  Incorrect: '答错了',
  Verbose: '较啰嗦',
  Best: '真棒',
  Rating: '评分',
  Q_A_Category: '问答类别',
  Q_A_Rating: '问答评分',
  feed_back_desc:
    '0: 无结果\n' +
    '1: 有结果，但是在文不对题，没有理解问题\n' +
    '2: 有结果，理解了问题，但是提示回答不了这个问题\n' +
    '3: 有结果，理解了问题，并做出回答，但是回答的结果错误\n' +
    '4: 有结果，理解了问题，回答结果正确，但是比较啰嗦，缺乏总结\n' +
    '5: 有结果，理解了问题，回答结果正确，推理正确，并给出了总结，言简意赅\n',
  input_count: '共计输入',
  input_unit: '字',
  Click_Select: '点击选择',
  Quick_Start: '快速开始',
  Select_Plugins: '选择插件',
  Search: '搜索',
  Reset: '重置',
  Update_From_Github: '更新Github插件',
  Upload: '上传',
  Market_Plugins: '插件市场',
  My_Plugins: '我的插件',
  Del_Knowledge_Tips: '你确定删除该知识库吗',
  Del_Document_Tips: '你确定删除该文档吗',
  Tips: '提示',
  Limit_Upload_File_Count_Tips: '一次只能上传一个文件',
  To_Plugin_Market: '前往插件市场',
  Summary: '总结',
  stacked_column_chart: '堆叠柱状图',
  column_chart: '柱状图',
  percent_stacked_column_chart: '百分比堆叠柱状图',
  grouped_column_chart: '簇形柱状图',
  time_column: '簇形柱状图',
  pie_chart: '饼图',
  line_chart: '折线图',
  area_chart: '面积图',
  stacked_area_chart: '堆叠面积图',
  scatter_plot: '散点图',
  bubble_chart: '气泡图',
  stacked_bar_chart: '堆叠条形图',
  bar_chart: '条形图',
  percent_stacked_bar_chart: '百分比堆叠条形图',
  grouped_bar_chart: '簇形条形图',
  water_fall_chart: '瀑布图',
  table: '表格',
  multi_line_chart: '多折线图',
  multi_measure_column_chart: '多指标柱形图',
  multi_measure_line_chart: '多指标折线图',
  Advices: '自动推荐',
  Retry: '重试',
  Load_more: '加载更多',
  new_chat: '创建会话',
  choice_agent_tip: '请选择代理',
  no_context_tip: '请输入你的问题',
  Terminal: '终端',
  used_apps: '最近使用',
  app_in_mind: '没有心仪的应用？去',
  explore: '探索广场',
  Discover_more: '发现更多',
  sdk_insert: 'SDK接入',
  my_apps: '我的应用',
  awel_flow: 'AWEL 工作流',
  save: '保存',
  add_node: '添加节点',
  no_node: '没有可编排节点',
  connect_warning: '节点无法连接',
  flow_modal_title: '保存工作流',
  flow_name: '工作流名称',
  flow_description: '工作流描述',
  flow_name_required: '请输入工作流名称',
  flow_description_required: '请输入工作流描述',
  save_flow_success: '保存工作流成功',
  delete_flow_confirm: '确定删除该工作流吗？',
  related_nodes: '关联节点',
  language_select_tips: '请选择语言',
  add_resource: '添加资源',
  team_modal: '工作模式',
  App: '应用程序',
  resource: '资源',
  resource_name: '资源名',
  resource_type: '资源类型',
  resource_value: '参数',
  resource_dynamic: '动态',
  Please_input_the_work_modal: '请选择工作模式',
  available_resources: '可用资源',
  edit_new_applications: '编辑新的应用',
  collect: '收藏',
  collected: '已收藏',
  create: '创建',
  Agents: '智能体',
  edit_application: '编辑应用',
  add_application: '添加应用',
  app_name: '应用名称',
  input_app_name: '请输入应用名称',
  LLM_strategy: '模型策略',
  please_select_LLM_strategy: '请选择模型策略',
  LLM_strategy_value: '模型策略参数',
  please_select_LLM_strategy_value: '请选择模型策略参数',
  operators: '算子',
  Chinese: '中文',
  English: '英文',
  docs: '文档',
  apps: '全部',
  please_enter_the_keywords: '请输入关键词',
  input_tip: '请选择模型，输入描述快速开始',
  create_app: '创建应用',
  copy_url: '单击复制分享链接',
  double_click_open: '双击钉钉打开',
  construct: '应用管理',
  chat_online: '在线对话',
  recommend_apps: '热门推荐',
  all_apps: '全部应用',
  latest_apps: '最新应用',
  my_collected_apps: '我的收藏',
  collect_success: '收藏成功',
  cancel_success: '取消成功',
  published: '已发布',
  unpublished: '未发布',
  start_chat: '开始对话',
  native_app: '原生应用',
  native_type: '应用类型',
  temperature: '温度',
  max_new_tokens: '最大输出token',
  update: '更新',
  refreshSuccess: '刷新成功',
  Download: '下载',
  app_type_select: '请选择应用类型',
  please_select_param: '请选择参数',
  please_select_model: '请选择模型',
  please_input_temperature: '请输入temperature值',
  please_input_max_new_tokens: '请输入max_new_tokens值',
  select_workflow: '选择工作流',
  please_select_workflow: '请选择工作流',
  recommended_questions: '推荐问题',
  question: '问题',
  please_input_recommended_questions: '请输入推荐问题',
  is_effective: '是否生效',
  add_question: '添加问题',
  please_select_prompt: '请选择一个提示词',
  details: '详情',
  choose: '选择',
  please_choose: '请先选择',
  want_delete: '你确定要删除吗?',
  success: '成功',
  input_parameter: '输入参数',
  output_structure: '输出结构',
  User_input: '用户输入',
  LLM_test: 'LLM测试',
  Output_verification: '输出验证',
  select_scene: '请选择场景',
  select_type: '请选择类型',
  Please_complete_the_input_parameters: '请填写完整的输入参数',
  Please_fill_in_the_user_input: '请填写用户输入内容',
  help: '我可以帮您:',
  Refresh_status: '刷新状态',
  Recall_test: '召回测试',
  synchronization: '一键同步',
  Synchronization_initiated: '同步已发起，请稍后',
  Edit_document: '编辑文档',
  Document_name: '文档名',
  Correlation_problem: '关联问题',
  Add_problem: '添加问题',
  New_knowledge_base: '新增知识库',
  yuque: '语雀文档',
  Get_yuque_document: '获取语雀文档的内容',
  document_url: '文档地址',
  input_document_url: '请输入文档地址',
  Get_token: '请先获取团队知识库token，token获取',
  Reference_link: '参考链接',
  document_token: '文档token',
  input_document_token: '请输入文档token',
  input_question: '请输入问题',
  detail: '详情',
  Manual_entry: '手动录入',
  Data_content: '数据内容',
  Main_content: '主要内容',
  Auxiliary_data: '辅助数据',
  enter_question_first: '请先输入问题',
  Update_successfully: '更新成功',
  Create_successfully: '创建成功',
  Update_failure: '更新失败',
  Create_failure: '创建失败',
  View_details: '查看详情',
  All: '全部',
  Please_input_prompt_name: '请输入prompt名称',
  Copy_Btn: '复制',
  Delete_Btn: '删除',
  copy_to_clipboard: '复制到剪贴板',
  copy_to_clipboard_success: '复制到剪贴板成功',
  copy_to_clipboard_failed: '复制到剪贴板失败',
  publish: '发布',
  unpublish: '取消发布',
  publish_desc: '您确认发布该应用吗？',
  unPublish_desc: '您确认取消发布该应用吗？',
  published_success: '发布成功',
  dbgpts_community: 'DBGPTS社区',
  community_dbgpts: '社区DBGPTS',
  my_dbgpts: '我的DBGPTS',
  Refresh_dbgpts: '从社区Git仓库刷新',
  workflow: '工作流',
  resources: '资源',
  app: '应用',
  please_select_resource_type: '请选择资源类型',
} as const;
