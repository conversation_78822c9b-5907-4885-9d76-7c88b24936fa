import type { GetMetadataTopicListVO } from '@/types/app';

export function sortTopicOption(data: GetMetadataTopicListVO[]) {
  const result: GetMetadataTopicListVO[] = [];
  for (let i = 0; i < data.length; i++) {
    if (data[i].topic_name === '默认主题') {
      result.unshift(data[i]); // 将 '默认主题' 元素添加到 result 数组的开头
    } else {
      result.push(data[i]); // 将其他元素添加到 result 数组的末尾
    } 
  }
  // if (result.length > 0) {
  //   result.push({ 
  //     id: -1,
  //     topic_name: '自动选择', 
  //     topic_desc: '自动选择',
  //     connect_config_id: '',
  //   }); // 如果 result 数组为空，添加一个默认元素
  // }
  return result;
}

export function getTopicFromCascader (input: string | string[] | undefined) {
  if (Array.isArray(input)) {
    return input[input.length - 1]; // 返回数组的最后一个元素 
  }
  return input;
}

export const topicCascadeDefaultOptions = [
  {
    label: '自动选择',
    value: '-1',
  },
  {
    label: '手动选择',
    value: '-2',
    children: [],
  }
]