/**
 * 模型项目接口
 */
export interface ModelItem {
  model_name: string;
  worker_type: string;
  host: string;
  port: number;
  manager_host: string;
  manager_port: number;
  healthy: boolean;
  check_healthy: boolean;
  prompt_template: string | null;
  last_heartbeat: string;
  // 保留以下字段以兼容现有代码
  id?: string;
  name?: string;
  provider?: string;
  icon?: string;
  manageHost?: string;
  status?: string;
  lastHeartBeat?: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * 模型表单接口
 */
export interface ModelForm {
  name: string;
  provider: string;
  icon?: string;
  host: string;
  manageHost: string;
  status: string;
} 

// 模型类型参数定义
export interface ModelTypeParam {
  required: boolean;
  is_array: boolean;
  param_name: string;
  param_class: string;
  param_type: string;
  param_order?: number;
  label: string;
  description: string;
  default_value: any;
  valid_values: any;
  ext_metadata: any;
  nested_fields: any;
}

// 模型类型定义
export interface ModelTypeItem {
  worker_type: string;
  provider: string;
  model: string;
  params: ModelTypeParam[];
  path: string | null;
  path_exist: boolean;
  proxy: boolean;
  enabled: boolean;
  description: string;
  host: string;
  port: number;
}

// 模型操作请求接口
export interface ModelOperation {
  host: string;
  port: number;
  model: string;
  worker_type: string;
  delete_after?: boolean;
  params?: Record<string, any>;
}