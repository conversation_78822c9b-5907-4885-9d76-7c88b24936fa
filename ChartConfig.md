# 图表配置示例

本文档提供了各种图表类型的配置示例，包括数据结构和映射关系。

## 目录

- [柱状图 (Bar Chart)](#柱状图)
- [折线图 (Line Chart)](#折线图)
- [饼图 (Pie Chart)](#饼图)
- [面积图 (Area Chart)](#面积图)
- [散点图 (Scatter Chart)](#散点图)
- [雷达图 (Radar Chart)](#雷达图)
- [漏斗图 (Funnel Chart)](#漏斗图)
- [热力图 (Heatmap Chart)](#热力图)
- [仪表盘 (Gauge Chart)](#仪表盘)
- [矩形树图 (Treemap Chart)](#矩形树图)
- [条形图 (Horizontal Bar Chart)](#条形图)
- [词云图 (Word Cloud Chart)](#词云图)
- [指标卡 (Metric Chart)](#指标卡)
- [表格 (Table Chart)](#表格)

## 柱状图

```typescript
{
  dataType: 'static',
  data: {
    columns: ['月份', '销量', '利润'],
    values: [
      ['一月', 120, 20],
      ['二月', 200, 40],
      ['三月', 150, 30],
      ['四月', 80, 15],
      ['五月', 70, 14],
      ['六月', 110, 22]
    ]
  },
  config: {
    type: 'bar',
    title: {
      text: '销售数据',
      show: true
    },
    tooltip: {},
    legend: {}
  },
  dataMapping: {
    xField: '月份',
    yFields: ['销量', '利润'],
    seriesNames: ['销量', '利润']
  }
}
```

映射后的config示例：

```typescript
{
  type: 'bar',
  title: {
    text: '销售数据',
    show: true
  },
  tooltip: {},
  legend: {},
  xAxis: {
    type: 'category',
    data: ['一月', '二月', '三月', '四月', '五月', '六月']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '销量',
      type: 'bar',
      data: [120, 200, 150, 80, 70, 110]
    },
    {
      name: '利润',
      type: 'bar',
      data: [20, 40, 30, 15, 14, 22]
    }
  ]
}
```

## 折线图

```typescript
{
  dataType: 'static',
  data: {
    columns: ['日期', '访问量', '点击量'],
    values: [
      ['周一', 120, 220],
      ['周二', 132, 182],
      ['周三', 101, 191],
      ['周四', 134, 234],
      ['周五', 90, 290],
      ['周六', 230, 330],
      ['周日', 210, 310]
    ]
  },
  config: {
    type: 'line',
    title: {
      text: '一周数据趋势',
      show: true
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {}
  },
  dataMapping: {
    xField: '日期',
    yFields: ['访问量', '点击量'],
    seriesNames: ['访问量', '点击量']
  }
}
```

映射后的config示例：

```typescript
{
  type: 'line',
  title: {
    text: '一周数据趋势',
    show: true
  },
  tooltip: {
    trigger: 'axis'
  },
  legend: {},
  xAxis: {
    type: 'category',
    data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '访问量',
      type: 'line',
      data: [120, 132, 101, 134, 90, 230, 210]
    },
    {
      name: '点击量',
      type: 'line',
      data: [220, 182, 191, 234, 290, 330, 310]
    }
  ]
}
```

## 饼图

```typescript
{
  dataType: 'static',
  data: {
    columns: ['类别', '数值'],
    values: [
      ['直接访问', 335],
      ['邮件营销', 310],
      ['联盟广告', 234],
      ['视频广告', 135],
      ['搜索引擎', 1548]
    ]
  },
  config: {
    type: 'pie',
    title: {
      text: '访问来源',
      show: true
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    }
  },
  dataMapping: {
    pie: {
      nameField: '类别',
      valueField: '数值'
    }
  }
}
```

映射后的config示例：

```typescript
{
  type: 'pie',
  title: {
    text: '访问来源',
    show: true
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  series: [
    {
      type: 'pie',
      data: [
        { name: '直接访问', value: 335 },
        { name: '邮件营销', value: 310 },
        { name: '联盟广告', value: 234 },
        { name: '视频广告', value: 135 },
        { name: '搜索引擎', value: 1548 }
      ]
    }
  ]
}
```

## 面积图

```typescript
{
  dataType: 'static',
  data: {
    columns: ['日期', '访问量', '点击量'],
    values: [
      ['周一', 120, 220],
      ['周二', 132, 182],
      ['周三', 101, 191],
      ['周四', 134, 234],
      ['周五', 90, 290],
      ['周六', 230, 330],
      ['周日', 210, 310]
    ]
  },
  config: {
    type: 'area',
    title: {
      text: '一周数据面积图',
      show: true
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {}
  },
  dataMapping: {
    xField: '日期',
    yFields: ['访问量', '点击量'],
    seriesNames: ['访问量', '点击量']
  }
}
```

映射后的config示例：

```typescript
{
  type: 'area',
  title: {
    text: '一周数据面积图',
    show: true
  },
  tooltip: {
    trigger: 'axis'
  },
  legend: {},
  xAxis: {
    type: 'category',
    data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '访问量',
      type: 'line',
      areaStyle: {},
      data: [120, 132, 101, 134, 90, 230, 210]
    },
    {
      name: '点击量',
      type: 'line',
      areaStyle: {},
      data: [220, 182, 191, 234, 290, 330, 310]
    }
  ]
}
```

## 散点图

```typescript
{
  dataType: 'static',
  data: {
    columns: ['身高(cm)', '体重(kg)', '年龄'],
    values: [
      [161.2, 51.6, 23],
      [167.5, 59.0, 28],
      [159.5, 49.2, 21],
      [157.0, 63.0, 25],
      [170.0, 59.0, 33],
      [176.2, 66.8, 35],
      [180.3, 76.6, 42],
      [164.5, 55.0, 26],
      [173.0, 60.9, 30],
      [183.0, 72.0, 39]
    ]
  },
  config: {
    type: 'scatter',
    title: {
      text: '身高体重分布',
      show: true
    },
    tooltip: {
      trigger: 'item',
      formatter: '身高: {c[0]}cm<br/>体重: {c[1]}kg<br/>年龄: {c[2]}岁'
    }
  },
  dataMapping: {
    scatter: {
      xField: '身高(cm)',
      yField: '体重(kg)',
      sizeField: '年龄' // 额外添加一个字段用于表示点的大小
    }
  }
}
```

映射后的config示例：

```typescript
{
  type: 'scatter',
  title: {
    text: '身高体重分布',
    show: true
  },
  tooltip: {
    trigger: 'item',
    formatter: '身高: {c[0]}cm<br/>体重: {c[1]}kg<br/>年龄: {c[2]}岁'
  },
  xAxis: {
    type: 'value',
    name: '身高(cm)'
  },
  yAxis: {
    type: 'value',
    name: '体重(kg)'
  },
  series: [
    {
      type: 'scatter',
      symbolSize: function (data) {
        return Math.sqrt(data[2]) * 3; // 根据第三个值计算散点大小
      },
      data: [
        [161.2, 51.6, 23],
        [167.5, 59.0, 28],
        [159.5, 49.2, 21],
        [157.0, 63.0, 25],
        [170.0, 59.0, 33],
        [176.2, 66.8, 35],
        [180.3, 76.6, 42],
        [164.5, 55.0, 26],
        [173.0, 60.9, 30],
        [183.0, 72.0, 39]
      ]
    }
  ]
}
```

## 雷达图

```typescript
{
  dataType: 'static',
  data: {
    columns: ['项目', '预算分配', '实际开销'],
    values: [
      ['销售', 4300, 5000],
      ['管理', 10000, 14000],
      ['信息技术', 28000, 28000],
      ['客服', 35000, 31000],
      ['研发', 50000, 42000],
      ['市场', 19000, 21000]
    ]
  },
  config: {
    type: 'radar',
    title: {
      text: '预算vs开销',
      show: true
    },
    tooltip: {}
  },
  dataMapping: {
    radar: {
      indicatorField: '项目',
      seriesFields: ['预算分配', '实际开销'],
      maxValueMapping: {
        '销售': 6500,
        '管理': 16000,
        '信息技术': 30000,
        '客服': 38000,
        '研发': 52000,
        '市场': 25000
      }
    }
  }
}
```

映射后的config示例：

```typescript
{
  type: 'radar',
  title: {
    text: '预算vs开销',
    show: true
  },
  tooltip: {},
  legend: {
    data: ['预算分配', '实际开销']
  },
  radar: {
    indicator: [
      { name: '销售', max: 6500 },
      { name: '管理', max: 16000 },
      { name: '信息技术', max: 30000 },
      { name: '客服', max: 38000 },
      { name: '研发', max: 52000 },
      { name: '市场', max: 25000 }
    ]
  },
  series: [
    {
      type: 'radar',
      data: [
        {
          name: '预算分配',
          value: [4300, 10000, 28000, 35000, 50000, 19000]
        },
        {
          name: '实际开销',
          value: [5000, 14000, 28000, 31000, 42000, 21000]
        }
      ]
    }
  ]
}
```

## 漏斗图

```typescript
{
  dataType: 'static',
  data: {
    columns: ['阶段', '数值'],
    values: [
      ['访问', 100],
      ['咨询', 80],
      ['订单', 60],
      ['付款', 40],
      ['成交', 20]
    ]
  },
  config: {
    type: 'funnel',
    title: {
      text: '销售漏斗',
      show: true
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}'
    }
  },
  dataMapping: {
    funnel: {
      nameField: '阶段',
      valueField: '数值'
    }
  }
}
```

映射后的config示例：

```typescript
{
  type: 'funnel',
  title: {
    text: '销售漏斗',
    show: true
  },
  tooltip: {
    trigger: 'item',
    formatter: '{b}: {c}'
  },
  series: [
    {
      type: 'funnel',
      data: [
        { name: '访问', value: 100 },
        { name: '咨询', value: 80 },
        { name: '订单', value: 60 },
        { name: '付款', value: 40 },
        { name: '成交', value: 20 }
      ]
    }
  ]
}
```

## 热力图

```typescript
{
  dataType: 'static',
  data: {
    columns: ['时段', '工作日', '销售额'],
    values: [
      ['12a', '周一', 5],
      ['1a', '周一', 7],
      ['2a', '周一', 3],
      ['3a', '周一', 1],
      ['4a', '周一', 0],
      ['12a', '周二', 6],
      ['1a', '周二', 3],
      ['2a', '周二', 2],
      ['3a', '周二', 0],
      ['4a', '周二', 1],
      ['12a', '周三', 7],
      ['1a', '周三', 8],
      ['2a', '周三', 6],
      ['3a', '周三', 4],
      ['4a', '周三', 2]
    ]
  },
  config: {
    type: 'heatmap',
    title: {
      text: '销售额热力图',
      show: true
    },
    tooltip: {
      position: 'top'
    },
    grid: {
      height: '50%',
      top: '10%'
    },
    visualMap: {
      min: 0,
      max: 10,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '15%'
    }
  },
  dataMapping: {
    heatmap: {
      xField: '时段',
      yField: '工作日',
      valueField: '销售额'
    }
  }
}
```

映射后的config示例：

```typescript
{
  type: 'heatmap',
  title: {
    text: '销售额热力图',
    show: true
  },
  tooltip: {
    position: 'top'
  },
  grid: {
    height: '50%',
    top: '10%'
  },
  visualMap: {
    min: 0,
    max: 10,
    calculable: true,
    orient: 'horizontal',
    left: 'center',
    bottom: '15%'
  },
  xAxis: {
    type: 'category',
    data: ['12a', '1a', '2a', '3a', '4a']
  },
  yAxis: {
    type: 'category',
    data: ['周一', '周二', '周三']
  },
  series: [
    {
      type: 'heatmap',
      data: [
        [0, 0, 5],
        [1, 0, 7],
        [2, 0, 3],
        [3, 0, 1],
        [4, 0, 0],
        [0, 1, 6],
        [1, 1, 3],
        [2, 1, 2],
        [3, 1, 0],
        [4, 1, 1],
        [0, 2, 7],
        [1, 2, 8],
        [2, 2, 6],
        [3, 2, 4],
        [4, 2, 2]
      ]
    }
  ]
}
```

## 仪表盘

```typescript
{
  dataType: 'static',
  data: {
    columns: ['指标名称', '当前值', '目标值'],
    values: [
      ['完成率', 75, 100]
    ]
  },
  config: {
    type: 'gauge',
    title: {
      text: '业务完成率',
      show: true
    },
    tooltip: {
      formatter: '{a} <br/>{b} : {c}%'
    }
  },
  dataMapping: {
    gauge: {
      valueField: '当前值',
      nameField: '指标名称'
    }
  }
}
```

映射后的config示例：

```typescript
{
  type: 'gauge',
  title: {
    text: '业务完成率',
    show: true
  },
  tooltip: {
    formatter: '{a} <br/>{b} : {c}%'
  },
  series: [
    {
      name: '业务指标',
      type: 'gauge',
      detail: {
        valueAnimation: true,
        formatter: '{value}%',
        color: 'inherit'
      },
      data: [{ value: 75, name: '完成率' }],
      axisLine: {
        lineStyle: {
          width: 30,
          color: [
            [0.3, '#67e0e3'],
            [0.7, '#37a2da'],
            [1, '#fd666d']
          ]
        }
      }
    }
  ]
}
```

## 矩形树图

```typescript
{
  dataType: 'static',
  data: {
    columns: ['部门', '产品', '销售额'],
    values: [
      ['第一部门', null, 40],
      ['第一部门', '产品A', 30],
      ['第一部门', '产品B', 10],
      ['第二部门', null, 20],
      ['第三部门', null, 60],
      ['第三部门', '产品C', 20],
      ['第三部门', '产品D', 10],
      ['第三部门', '产品E', 30]
    ]
  },
  config: {
    type: 'treemap',
    title: {
      text: '部门销售额',
      show: true
    },
    tooltip: {}
  },
  dataMapping: {
    treemap: {
      parentField: '部门',
      childField: '产品',
      valueField: '销售额'
    }
  }
}
```

映射后的config示例：

```typescript
{
  type: 'treemap',
  title: {
    text: '部门销售额',
    show: true
  },
  tooltip: {},
  series: [
    {
      type: 'treemap',
      visibleMin: 300,
      data: [
        {
          name: '第一部门',
          value: 40,
          children: [
            {
              name: '产品A',
              value: 30
            },
            {
              name: '产品B',
              value: 10
            }
          ]
        },
        {
          name: '第二部门',
          value: 20
        },
        {
          name: '第三部门',
          value: 60,
          children: [
            {
              name: '产品C',
              value: 20
            },
            {
              name: '产品D',
              value: 10
            },
            {
              name: '产品E',
              value: 30
            }
          ]
        }
      ]
    }
  ]
}
```

## 条形图

```typescript
{
  dataType: 'static',
  data: {
    columns: ['类别', '数量'],
    values: [
      ['衬衫', 20],
      ['羊毛衫', 35],
      ['雪纺衫', 15],
      ['裤子', 40],
      ['高跟鞋', 25],
      ['袜子', 30]
    ]
  },
  config: {
    type: 'horizontal-bar',
    title: {
      text: '产品销量',
      show: true
    },
    tooltip: {},
    legend: {
      data: ['数量']
    }
  },
  dataMapping: {
    // 可共用柱状图的映射逻辑，只是渲染方向不同
    xField: '数量',  // 注意水平条形图的x与y相反
    yField: '类别',  // 注意水平条形图的x与y相反
    seriesNames: ['数量']
  }
}
```

映射后的config示例：

```typescript
{
  type: 'horizontal-bar',
  title: {
    text: '产品销量',
    show: true
  },
  tooltip: {},
  legend: {
    data: ['数量']
  },
  xAxis: {
    type: 'value'
  },
  yAxis: {
    type: 'category',
    data: ['衬衫', '羊毛衫', '雪纺衫', '裤子', '高跟鞋', '袜子']
  },
  series: [
    {
      name: '数量',
      type: 'bar',
      data: [20, 35, 15, 40, 25, 30]
    }
  ]
}
```

## 词云图

```typescript
{
  dataType: 'static',
  data: {
    columns: ['词语', '频率'],
    values: [
      ['技术', 128],
      ['数据', 110],
      ['分析', 95],
      ['智能', 85],
      ['开发', 78],
      ['云计算', 72],
      ['创新', 68],
      ['平台', 65],
      ['服务', 60],
      ['系统', 58]
    ]
  },
  config: {
    type: 'word-cloud',
    title: {
      text: '热门词汇',
      show: true
    },
    tooltip: {
      show: true
    }
  },
  dataMapping: {
    wordCloud: {
      wordField: '词语',
      valueField: '频率'
    }
  }
}
```

映射后的config示例：

```typescript
{
  type: 'word-cloud',
  title: {
    text: '热门词汇',
    show: true
  },
  tooltip: {
    show: true
  },
  series: [{
    type: 'wordCloud',
    shape: 'circle',
    left: 'center',
    top: 'center',
    width: '80%',
    height: '80%',
    right: null,
    bottom: null,
    sizeRange: [12, 60],
    rotationRange: [-90, 90],
    rotationStep: 45,
    gridSize: 8,
    drawOutOfBound: false,
    textStyle: {
      fontFamily: 'sans-serif',
      fontWeight: 'bold',
      color: function () {
        return 'rgb(' + 
          Math.round(Math.random() * 155 + 100) + ',' + 
          Math.round(Math.random() * 155 + 100) + ',' + 
          Math.round(Math.random() * 155 + 100) + ')';
      }
    },
    data: [
      { name: '技术', value: 128 },
      { name: '数据', value: 110 },
      { name: '分析', value: 95 },
      { name: '智能', value: 85 },
      { name: '开发', value: 78 },
      { name: '云计算', value: 72 },
      { name: '创新', value: 68 },
      { name: '平台', value: 65 },
      { name: '服务', value: 60 },
      { name: '系统', value: 58 }
    ]
  }]
}
```

## 指标卡

```typescript
{
  dataType: 'static',
  data: {
    columns: ['指标名', '当前值', '单位'],
    values: [
      ['总销售额', 1234, '元']
    ]
  },
  config: {
    type: 'metric',
    title: {
      text: '销售指标',
      show: true
    },
    valueSize: 60,
    valueColor: '#333333',
    titleColor: '#666666',
    titleSize: 24,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    valueFormatter: 'thousands'
  },
  dataMapping: {
    metric: {
      titleField: '指标名',
      valueField: '当前值',
      unitField: '单位'
    }
  }
}
```

映射后的config示例：

```typescript
{
  type: 'metric',
  title: {
    text: '总销售额',
    show: true
  },
  value: 1234,
  unit: '元',
  valueSize: 60,
  valueColor: '#333333',
  titleColor: '#666666',
  titleSize: 24,
  backgroundColor: '#FFFFFF',
  borderRadius: 8,
  valueFormatter: 'thousands'
}
```

## 表格

```typescript
{
  dataType: 'static',
  data: {
    columns: ['姓名', '年龄', '地址'],
    values: [
      ['张三', 18, '北京市朝阳区'],
      ['李四', 22, '上海市浦东新区'],
      ['王五', 25, '广州市天河区'],
      ['赵六', 30, '深圳市南山区']
    ]
  },
  config: {
    type: 'table',
    title: {
      text: '人员信息',
      show: true
    },
    pagination: false,
    bordered: true,
    headerBackground: '#fafafa',
    headerTextColor: '#262626',
    rowBackground: '#ffffff',
    rowTextColor: '#262626',
    fontSize: 14,
    headerFontSize: 14
  },
  dataMapping: {
    table: {
      // 表格一般直接使用列名和数据，不需要特殊映射
      columns: ['姓名', '年龄', '地址']
    }
  }
}
```

映射后的config示例：

```typescript
{
  type: 'table',
  title: {
    text: '人员信息',
    show: true
  },
  columns: [
    { title: '姓名', dataIndex: 'col0', key: 'col0' },
    { title: '年龄', dataIndex: 'col1', key: 'col1' },
    { title: '地址', dataIndex: 'col2', key: 'col2' }
  ],
  dataSource: [
    { key: 'row-0', col0: '张三', col1: 18, col2: '北京市朝阳区' },
    { key: 'row-1', col0: '李四', col1: 22, col2: '上海市浦东新区' },
    { key: 'row-2', col0: '王五', col1: 25, col2: '广州市天河区' },
    { key: 'row-3', col0: '赵六', col1: 30, col2: '深圳市南山区' }
  ],
  pagination: false,
  bordered: true,
  headerBackground: '#fafafa',
  headerTextColor: '#262626',
  rowBackground: '#ffffff',
  rowTextColor: '#262626',
  fontSize: 14,
  headerFontSize: 14
}
```
