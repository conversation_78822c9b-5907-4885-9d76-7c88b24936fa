<template>
  <common-chart-settings
    :options="props.options"
    chart-type="gauge"
    @update="updateOptions"
  >
    <!-- 样式配置 -->
    <template #style-settings>
      <!-- 仪表盘设置 -->
      <a-collapse-panel key="gauge" header="仪表盘设置">
        <a-form-item label="最小值">
          <a-input-number 
            v-model:value="chartConfig.series[0].min" 
            :min="0" 
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="最大值">
          <a-input-number 
            v-model:value="chartConfig.series[0].max" 
            :min="1" 
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="当前值">
          <a-input-number 
            v-model:value="chartConfig.series[0].data[0].value" 
            :min="chartConfig.series[0].min" 
            :max="chartConfig.series[0].max" 
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="仪表盘半径">
          <a-input-number 
            v-model:value="gaugeRadius"
            :min="30" 
            :max="100"
            :formatter="(value: number) => `${value}%`"
            :parser="(value: string) => Number(value.replace('%', ''))"
            style="width: 100%"
            @change="updateGaugeRadius"
          />
        </a-form-item>
        
        <a-form-item label="起始角度">
          <a-input-number 
            v-model:value="chartConfig.series[0].startAngle" 
            :min="0" 
            :max="360" 
            style="width: 100%"
          />
        </a-form-item>
        
        <a-form-item label="结束角度">
          <a-input-number 
            v-model:value="chartConfig.series[0].endAngle" 
            :min="0" 
            :max="360" 
            style="width: 100%"
          />
        </a-form-item>
      </a-collapse-panel>
      
      <!-- 进度颜色设置 -->
      <a-collapse-panel key="color" header="进度颜色设置">
        <a-collapse v-model:activeKey="activeColorKeys">
          <a-collapse-panel 
            v-for="(item, index) in chartConfig.series[0].axisLine.lineStyle.color" 
            :key="'color-' + index"
            :header="`区间 ${index + 1}: [${item[0] * 100}%]`"
          >
            <a-form-item label="区间值">
              <a-slider 
                v-model:value="item[0]" 
                :min="0" 
                :max="1" 
                :step="0.01"
              />
            </a-form-item>
            
            <a-form-item label="区间颜色">
              <a-input 
                v-model:value="item[1]" 
                type="color"
                style="width: 100px" 
              />
            </a-form-item>
          </a-collapse-panel>
        </a-collapse>
        
        <a-button type="dashed" block @click="addColorRange" style="margin-top: 16px">
          <plus-outlined /> 添加颜色区间
        </a-button>
      </a-collapse-panel>
      
      <!-- 指针设置 -->
      <a-collapse-panel key="pointer" header="指针设置">
        <a-form-item label="指针长度">
          <a-slider 
            v-model:value="chartConfig.series[0].pointer.length" 
            :min="10" 
            :max="100"
          />
        </a-form-item>
        
        <a-form-item label="指针宽度">
          <a-input-number 
            v-model:value="chartConfig.series[0].pointer.width" 
            :min="1" 
            :max="20" 
            style="width: 100%"
          />
        </a-form-item>
      </a-collapse-panel>
      
      <!-- 刻度设置 -->
      <a-collapse-panel key="axis" header="刻度设置">
        <a-form-item label="显示刻度">
          <a-switch v-model:checked="chartConfig.series[0].axisTick.show" />
        </a-form-item>
        
        <a-form-item label="刻度数量">
          <a-input-number 
            v-model:value="chartConfig.series[0].splitNumber" 
            :min="1" 
            :max="20" 
            style="width: 100%"
          />
        </a-form-item>
      </a-collapse-panel>
      
      <!-- 详情设置 -->
      <a-collapse-panel key="detail" header="详情设置">
        <a-form-item label="显示详情">
          <a-switch v-model:checked="chartConfig.series[0].detail.show" />
        </a-form-item>
        
        <a-form-item label="详情格式">
          <a-input 
            v-model:value="chartConfig.series[0].detail.formatter" 
            placeholder="如: {value}%" 
          />
        </a-form-item>
        
        <a-form-item label="字体大小">
          <a-input-number 
            v-model:value="chartConfig.series[0].detail.fontSize" 
            :min="10" 
            :max="100" 
            style="width: 100%"
          />
        </a-form-item>
      </a-collapse-panel>

      <!-- 颜色与背景 -->
      <a-divider orientation="left">颜色与背景</a-divider>
      <a-form-item label="背景颜色">
        <a-input 
          v-model:value="chartConfig.backgroundColor" 
          placeholder="例如: #ffffff" 
          :addon-before="'颜色'"
          @change="updateConfig"
        />
      </a-form-item>

      <!-- 动画设置 -->
      <a-divider orientation="left">动画设置</a-divider>
      <a-form-item label="开启动画">
        <a-switch v-model:checked="chartConfig.animation" @change="updateConfig" />
      </a-form-item>
      <a-form-item v-if="chartConfig.animation" label="动画时长">
        <a-input-number 
          v-model:value="chartConfig.animationDuration" 
          :min="100" 
          :max="5000"
          :step="100"
          @change="updateConfig" 
        />
      </a-form-item>

      <!-- 提示框设置 -->
      <a-divider orientation="left">提示框设置</a-divider>
      <a-form-item label="显示提示框">
        <a-switch v-model:checked="tooltipShow" @change="updateTooltip" />
      </a-form-item>
      <a-form-item v-if="tooltipShow" label="提示框触发">
        <a-select v-model:value="tooltipTrigger" @change="updateTooltip">
          <a-select-option value="item">数据项</a-select-option>
          <a-select-option value="axis">坐标轴</a-select-option>
          <a-select-option value="none">无</a-select-option>
        </a-select>
      </a-form-item>
    </template>
  </common-chart-settings>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import CommonChartSettings from './CommonChartSettings.vue';
import type { ChartOptions } from '@/types/chart';

const props = defineProps({
  options: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update']);

// 控制折叠面板的展开状态
const activeColorKeys = ref<string[]>(['color-0', 'color-1', 'color-2']);

// 仪表盘半径
const gaugeRadius = ref(75);

// 提示框设置
const tooltipShow = ref(true);
const tooltipTrigger = ref('item');

// 直接访问config部分的计算属性
const chartConfig = computed({
  get: () => {
    return props.options.config || getDefaultOptions().config;
  },
  set: (newConfig) => {
    updateConfig(newConfig);
  }
});

// 获取默认配置
const getDefaultOptions = (): any => {
  return {
    dataType: 'static',
    data: {
      columns: [],
      values: []
    },
    config: {
      type: 'gauge',
      title: {
        text: '仪表盘',
        show: true
      },
      tooltip: {
        formatter: '{a} <br/>{b} : {c}%',
        show: true,
        trigger: 'item'
      },
      backgroundColor: '#ffffff',
      animation: true,
      animationDuration: 1000,
      series: [
        {
          name: '进度',
          type: 'gauge',
          min: 0,
          max: 100,
          radius: '75%',
          startAngle: 225,
          endAngle: -45,
          splitNumber: 10,
          pointer: {
            icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
            length: '60%',
            width: 8,
            offsetCenter: [0, '8%'],
            itemStyle: {
              color: 'inherit'
            }
          },
          axisLine: {
            show: true,
            lineStyle: {
              width: 30,
              color: [
                [0.3, '#67e0e3'],
                [0.7, '#37a2da'],
                [1, '#fd666d']
              ]
            }
          },
          axisTick: {
            show: true,
            splitNumber: 5,
            distance: 2,
            lineStyle: {
              color: '#999',
              width: 2
            },
            length: 6
          },
          splitLine: {
            show: true,
            length: 12,
            lineStyle: {
              color: '#999',
              width: 3
            }
          },
          axisLabel: {
            show: true,
            distance: 15,
            fontSize: 12
          },
          detail: {
            show: true,
            offsetCenter: [0, '50%'],
            color: 'inherit',
            fontSize: 30,
            formatter: '{value}%'
          },
          data: [
            {
              value: 50,
              name: '完成率'
            }
          ]
        }
      ]
    }
  };
};

// 更新配置
const updateOptions = (newOptions: ChartOptions) => {
  emit('update', newOptions);
};

// 更新config部分
const updateConfig = (newConfig: any) => {
  const updatedOptions = {
    ...props.options,
    config: newConfig
  };
  emit('update', updatedOptions);
};

// 更新仪表盘半径
const updateGaugeRadius = () => {
  const config = { ...chartConfig.value };
  config.series[0].radius = `${gaugeRadius.value}%`;
  updateConfig(config);
};

// 添加颜色区间
const addColorRange = () => {
  const config = { ...chartConfig.value };
  const colorRanges = config.series[0].axisLine.lineStyle.color;
  
  if (colorRanges.length > 0) {
    const lastRange = colorRanges[colorRanges.length - 1];
    const newValue = Math.min(lastRange[0] + 0.1, 1);
    
    // 只有当能添加新区间时才添加
    if (newValue < 1) {
      colorRanges.push([newValue, getRandomColor()]);
      
      // 更新折叠面板激活的key
      activeColorKeys.value.push(`color-${colorRanges.length - 1}`);
      
      updateConfig(config);
    }
  }
};

// 生成随机颜色
const getRandomColor = () => {
  const letters = '0123456789ABCDEF';
  let color = '#';
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
};

// 提示框相关
const updateTooltip = () => {
  chartConfig.value.tooltip = {
    ...(chartConfig.value.tooltip || {}),
    show: tooltipShow.value,
    trigger: tooltipTrigger.value
  };
  updateConfig(chartConfig.value);
};

// 初始化组件
onMounted(() => {
  // 初始化仪表盘半径
  if (chartConfig.value.series?.[0]) {
    const radius = chartConfig.value.series[0].radius;
    if (typeof radius === 'string' && radius.endsWith('%')) {
      gaugeRadius.value = parseInt(radius, 10);
    } else {
      gaugeRadius.value = 75;
    }
    
    // 初始化颜色区间的折叠面板key
    if (chartConfig.value.series[0].axisLine?.lineStyle?.color) {
      activeColorKeys.value = chartConfig.value.series[0].axisLine.lineStyle.color.map((_: any, idx: number) => `color-${idx}`);
    }
  }
  // 初始化提示框
  if (chartConfig.value.tooltip) {
    tooltipShow.value = chartConfig.value.tooltip.show !== false;
    tooltipTrigger.value = chartConfig.value.tooltip.trigger || 'item';
  }
});

// 监听配置变化
watch(() => props.options, (newOptions) => {
  if (newOptions?.config?.series?.[0]) {
    // 更新仪表盘半径
    const radius = newOptions.config.series[0].radius;
    if (typeof radius === 'string' && radius.endsWith('%')) {
      gaugeRadius.value = parseInt(radius, 10);
    }
    
    // 更新颜色区间的折叠面板key
    if (newOptions.config.series[0].axisLine?.lineStyle?.color) {
      activeColorKeys.value = newOptions.config.series[0].axisLine.lineStyle.color.map((_: any, idx: number) => `color-${idx}`);
    }
  }
  // 更新提示框
  if (newOptions?.config?.tooltip) {
    tooltipShow.value = newOptions.config.tooltip.show !== false;
    tooltipTrigger.value = newOptions.config.tooltip.trigger || 'item';
  }
}, { deep: true });
</script>

<style scoped>
/* 无需额外样式 */
</style> 