<template>
  <a-page-header
      :title="`${routeParams.database} ( ${routeParams.topicName} ) `"
      :sub-title="`数据源ID: ${routeParams.datasourceId} - 主题ID: ${routeParams.topicId}`"
      @back="() => router.back()"
  />

  <a-card>
    <div class="table-operations">
      <div />
      <a-space>
        <a-button type="primary" @click="drawerOpen" :loading="drawerLoading">添加元数据</a-button>
        <a-button type="primary" @click="handleSync" :loading="syncLoading">
          <span v-if="syncLoading || syncPercent === 100" class="async-meta-percent">{{ syncPercent }}%</span>
          <span>
            {{ syncLoading ? '补充中' : 
              syncPercent === 100 ? '补充完成' :
              '大模型补充注释' }}
          </span>
        </a-button>
        <a-button type="primary" danger :disabled="!hasSelected" @click="handleBatchDelete">
          批量删除
        </a-button>
      </a-space>
    </div>

    <a-table
        :data-source="metadataList"
        :columns="columns"
        :row-selection="rowSelection"
        row-key="id"
        :scroll="{ x: 1500 }"
        :pagination="{
          locale: {
            items_per_page: ' 条/页',
          }
        }"
      >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'tables_remark'">
          <div class="editable-cell">
            <div v-if="editableNameData[record.id]" class="editable-cell-input-wrapper">
              <a-input v-model:value="editableNameData[record.id].tables_remark" @pressEnter="saveTableRemark(record.id)" />
              <check-outlined class="editable-cell-icon-check" @click="saveTableRemark(record.id)" />
            </div>
            <div v-else class="editable-cell-text-wrapper">
              {{ record.tables_remark || ' ' }}
              <edit-outlined class="editable-cell-icon" @click="editTableRemark(record.id)" />
            </div>
          </div>
        </template>
        <template v-if="column.key === 'columns_remark'">
          <div class="editable-cell">
            <div v-if="editableData[record.id]" class="editable-cell-input-wrapper">
              <a-input v-model:value="editableData[record.id].columns_remark" @pressEnter="saveRemark(record.id)" />
              <check-outlined class="editable-cell-icon-check" @click="saveRemark(record.id)" />
            </div>
            <div v-else class="editable-cell-text-wrapper">
              {{ record.columns_remark || ' ' }}
              <edit-outlined class="editable-cell-icon" @click="editRemark(record.id)" />
            </div>
          </div>
        </template>
        <template v-if="column.key === 'foreign_key_table'">
          <div v-if="record.columns_name.includes('_id') || record.columns_name.includes('_no') || record.columns_name.includes('_code')">
            <div class="editable-cell">
              <div v-if="editableForeignKeyData[record.id]" class="editable-cell-input-wrapper foreign-key-container">
                <div class="foreign-key-wrapper">
                  <a-cascader
                    v-model:value="editableForeignKeyData[record.id].foreign_key_cascade" 
                    :options="foreignKeyOptions" 
                    style="margin-left: 10px; text-align: left;"
                    placeholder="请选择外键" 
                  >
                    <template #displayRender="{ labels, selectedOptions }">
                      <span v-for="(label, index) in labels" :key="selectedOptions[index].value">
                        <span v-if="index === labels.length - 1">
                          {{ label  }}
                        </span>
                      </span>
                    </template>
                  </a-cascader>
                </div>
                <check-outlined class="editable-cell-icon-check" @click="saveTableForeignKey(record.id)" />
              </div>
              <div v-else class="editable-cell-text-wrapper">
                <span v-if="record.foreign_key_id" class="foreign-key-text">
                  {{ record.foreign_key_table + '&nbsp;&nbsp;&nbsp;' + record.foreign_key_id }}
                </span>
                <span v-else>未关联外键</span>
                <edit-outlined class="editable-cell-icon" @click="editTableForeignKey(record.id)" />
              </div>
            </div>
          </div>
          <div v-else>-</div>
        </template>
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="handleEditInTable(record)">编辑</a-button>
            <a-button type="link" danger size="small" @click="handleDeleteInTable(record)">删除</a-button>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 编辑模态框 -->
    <a-modal
        :visible="editModalVisible"
        title="编辑元数据"
        @ok="saveEdit"
        @cancel="cancelEdit"
    >
      <a-form :model="editForm" layout="vertical">
        <a-form-item label="数据库名">
          <a-input v-model:value="editForm.db_name" disabled/>
        </a-form-item>
        <a-form-item label="主题">
          <a-input v-model:value="editForm.topic" disabled/>
        </a-form-item>
        <a-form-item label="表名">
          <a-input v-model:value="editForm.tables_name" disabled/>
        </a-form-item>
        <a-form-item label="列名" :rules="[{ required: true, message: '请输入列名' }]">
          <a-input v-model:value="editForm.columns_name" maxlength="50" />
        </a-form-item>
        <a-form-item label="列类型" :rules="[{ required: true, message: '请输入列类型' }]">
          <a-input v-model:value="editForm.columns_type" maxlength="50" />
        </a-form-item>
        <a-form-item label="列注释">
          <a-input v-model:value="editForm.columns_remark" maxlength="100" />
        </a-form-item>
      </a-form>
    </a-modal>
  </a-card>

  <a-drawer title="元数据新增" size="large" :visible="drawerVisible" @close="drawerClose" :key="drawerKey">
    <template #extra>
      <a-button style="margin-right: 8px" @click="drawerClose">取消</a-button>
      <a-button type="primary" @click="submitAddData">确定</a-button>
    </template>
    <a-form :form="addForm" layout="vertical" ref="drawerRef">
      <a-form-item
          name="tableName"
          :rules="[{ validator: checkTableName, trigger: 'blur' }]"
      >
        <template #label>
          <!-- 自定义 label 内容 -->
          <span style="color: red; margin-right: 4px;">*</span>
          <span >元数据表（允许多选）</span>
        </template>
        <a-select
            mode="multiple"
            allow-clear
            style="width: 100%"
            placeholder="请选择源数据表"
            v-model:value="selectedValues"
            @change="handleChange"
            :options="options"
        />
      </a-form-item>
    </a-form>

    <div v-if="selectedValues.length > 0" style="margin-top: 24px">
      <a-menu
          mode="horizontal"
          :selected-keys="[currentTable]"
          @click="menuClick"
      >
        <a-menu-item
            v-for="table in selectedValues"
            :key="table"
        >
          {{ table }}
        </a-menu-item>
      </a-menu>

      <!-- 显示当前表数据 -->
      <div v-if="currentTable" style="margin-top: 16px">
        <a-table
            :data-source="currentTableData"
            :columns="[
          { title: '列名', dataIndex: 'columns_name', width: 120, ellipsis: true },
          { title: '类型', dataIndex: 'columns_type', width: 100, ellipsis: true },
          { title: '备注', dataIndex: 'columns_remark', width: 150, ellipsis: true }
        ]"
            row-key="columns_name"
            :row-selection="{ selectedRowKeys: selectedTableColumnMap[currentTable], onChange: onTableColumnSelectChange }"
        >
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex === 'columns_remark'">
              <div class="editable-cell">
                <div v-if="editDrawerData[`${record.tables_name}_${record.columns_name}`]" class="editable-cell-input-wrapper">
                  <a-input v-model:value="editDrawerData[`${record.tables_name}_${record.columns_name}`].columns_remark" @pressEnter="saveDrawerRemark(record.tables_name, record.columns_name)" />
                  <check-outlined class="editable-cell-icon-check" @click="saveDrawerRemark(record.tables_name, record.columns_name)" />
                </div>
                <div v-else class="editable-cell-text-wrapper">
                  {{ record.columns_remark || '-' }}
                  <edit-outlined class="editable-cell-icon" @click="editDrawerRemark(record.tables_name, record.columns_name)" />
                </div>
              </div>
            </template>
            <span v-if="column.dataIndex === 'columns_type'" class="type-tag">
              {{ text }}
            </span>
              <!-- <span v-else>{{ text || '-' }}</span> -->
          </template>
        </a-table>
      </div>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import type { Ref, UnwrapRef } from 'vue';
import {ref, reactive, computed, onMounted, nextTick} from 'vue';
import {message, Modal} from 'ant-design-vue';
import {useRoute, useRouter} from 'vue-router';
import type { Rule } from 'ant-design-vue/es/form';
import { CheckOutlined, EditOutlined } from '@ant-design/icons-vue';
import {
  getMetadataList,
  addMetadata,
  updateMetadata,
  updateTableMetadata,
  deleteMetadata,
  getDatasourcesList,
  addColumnSexegesis,
  getDefaultMetadataList,
  getForeignKey,
} from '@/api/metadata';
import type { Option } from '@/types/app';
import type { datasourcesItem, tableColumnItem, MetadataRequest, MetadataResponse } from '@/types/metaData.ts';
import {cloneDeep} from "lodash";

interface TableOption {
  label: string;
  value: string;
}

interface TableStructure {
  tables_name: string;
  tables_columns: {
    db_name: string
    db_type: string
    columns_name: string;
    columns_type: string;
    columns_remark: string;
    tables_remark: string;
  }[];
}

type ColumnSelection  = {
  [columnName: string]: boolean;
};

type TableColumnSelection = {
  [tableName: string]: ColumnSelection;
};

type ColumnRemark  = {
  [columnName: string]: string;
};

type TableColumnRemark = {
  [tableName: string]: ColumnRemark;
};

type TableColumnSelected = {
  [tableName: string]: string[];
};

const tableColumnMap = ref<TableColumnSelection>({});
const tableColumnRemarkMap = ref<TableColumnRemark>({});
const selectedTableColumnMap = ref<TableColumnSelected>({});
const foreignKeyOptions = ref<Option[]>([]);

// 路由相关
const route = useRoute();
const router = useRouter();

// 路由参数处理
const routeParams = reactive({
  datasourceId: Number(route.query.datasourceId),
  database: route.query.database,
  db_type: route.query.db_type,
  topicId: Number(route.query.topic),
  topicName: route.query.name,
});

// 元数据列表
const metadataList = ref<MetadataResponse[]>([]);
const loading = ref(false);
const editableData: UnwrapRef<Record<string, MetadataResponse>> = reactive({});
const editableNameData: UnwrapRef<Record<string, MetadataResponse>> = reactive({});
const editableForeignKeyData: UnwrapRef<Record<string, MetadataResponse>> = reactive({});
const editDrawerData: UnwrapRef<Record<string, tableColumnItem>> = reactive({});
const drawerLoading = ref<boolean>(false);
const drawerKey = ref<number>(0);
const syncLoading = ref<boolean>(false);
const syncPercent = ref<number>(0);
const syncTimer = ref();
const syncSteps = ref<number>(3);

// 表格列定义
const columns = ref([
  {title: 'ID', dataIndex: 'id', key: 'id', width: 150, ellipsis: true },
  {title: '数据库名', dataIndex: 'db_name', key: 'db_name', width: 150, ellipsis: true },
  {title: '主题', dataIndex: 'topic', key: 'topic', width: 150, ellipsis: true} ,
  {title: '表名', dataIndex: 'tables_name', key: 'tables_name', width: 150, ellipsis: true },
  {title: '表注释', dataIndex: 'tables_remark', key: 'tables_remark', width: 150, ellipsis: true },
  {title: '列名', dataIndex: 'columns_name', key: 'columns_name', width: 150, ellipsis: true },
  {title: '列类型', dataIndex: 'columns_type', key: 'columns_type', width: 150, ellipsis: true },
  {title: '列注释', dataIndex: 'columns_remark', key: 'columns_remark', width: 150, ellipsis: true },
  {title: '外键', dataIndex: 'foreign_key_table', key: 'foreign_key_table', width: 250, ellipsis: true },
  {title: '操作', key: 'action',  width: 150}
]);

// 判断是否有选中项
const hasSelected = computed(() => selectedRowKeys.value.length > 0);
// 选择相关
const selectedRowKeys = ref<string[]>([]);
const selectedRows = ref<MetadataResponse[]>([]);
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: string[], rows: MetadataResponse[]) => {
    selectedRowKeys.value = keys;
    selectedRows.value = rows;
  }
}));

// 编辑相关
const editModalVisible = ref(false);
const editForm = ref<MetadataResponse>({} as MetadataResponse);
const currentEditId = ref<number | string | null>(null);

//新增相关
const drawerVisible = ref<boolean>(false);
const addForm = ref(null);
// 下拉选项
const options = ref<any[]>([]);
const selectedValues = ref<string[]>([]);
const currentTable = ref<string>('');
const tableDataMap = reactive<Record<string, any>>({});
const drawerRef = ref();

const editRemark = (key: string) => {
  editableData[key] = cloneDeep(metadataList.value.filter(item => key === item.id)[0]);
};

const saveRemark = async (key: string) => {
  const metaData = metadataList.value.filter(item => key === item.id)[0];
  const res = await updateMetadata({
    id: metaData.id || '',
    columns_remark: editableData[key].columns_remark || '',
  });
  Object.assign(metadataList.value.filter(item => key === item.id)[0], editableData[key]);
  delete editableData[key];
  message.success('更新成功');
};

const editTableRemark = (key: string) => {
  editableNameData[key] = { 
    ...cloneDeep(metadataList.value.filter(item => key === item.id)[0]),
    tables_remark: metadataList.value.filter(item => key === item.id)[0].tables_remark || ''
  };
};

const saveTableRemark = async (key: string) => {
  const metaData = metadataList.value.filter(item => key === item.id)[0];
  await updateTableMetadata({
    tables_name: metaData.tables_name,
    tables_remark: editableNameData[key].tables_remark,
    connect_config_id: metaData.connect_config_id,
    topic_id: metaData.topic_id,
  });
  delete editableNameData[key];
  message.success('更新成功');
  loadMetadata();
};

const editTableForeignKey = (key: string) => {
  editableForeignKeyData[key] = cloneDeep(metadataList.value.filter(item => key === item.id)[0]);
};

const saveTableForeignKey = async (key: string) => {
  // const metaData = metadataList.value.filter(item => key === item.id)[0];
  console.log(editableForeignKeyData[key]);
  await updateMetadata({
    foreign_key_table: editableForeignKeyData[key]?.foreign_key_cascade ? editableForeignKeyData[key]?.foreign_key_cascade[0] : '',
    foreign_key_id: (editableForeignKeyData[key]?.foreign_key_cascade && editableForeignKeyData[key]?.foreign_key_cascade.length > 1) ? editableForeignKeyData[key]?.foreign_key_cascade[1] : '',
    id: key,
  });
  delete editableForeignKeyData[key];
  message.success('更新成功');
  loadMetadata();
};

const editDrawerRemark = (tablesName: string, columnsName: string) => {
  editDrawerData[`${tablesName}_${columnsName}`] = cloneDeep(tableDataMap[tablesName].filter((item: any) => columnsName === item.columns_name)[0]);
  console.log(editDrawerData);
};

const saveDrawerRemark = async (tablesName: string, columnsName: string) => {
  Object.assign(tableDataMap[tablesName].filter((item: any) => columnsName === item.columns_name)[0], editDrawerData[`${tablesName}_${columnsName}`]);
  delete editDrawerData[`${tablesName}_${columnsName}`];
};

// 初始化加载数据
onMounted(() => {
  if (!routeParams.datasourceId || !routeParams.topicId) {
    message.error('缺少必要参数');
    router.back();
    return;
  }
  loadMetadata();
  handleGetForeignKey();
});

onMounted(() => {
  syncTimer.value = setInterval(() => {
    if (syncSteps.value === 3) {
      syncSteps.value = 1;
    } else {
      syncSteps.value++;
    }
  }, 300)
});

// 加载元数据
const loadMetadata = async () => {
  try {
    loading.value = true;
    const res = await getMetadataList({
      datasource_id: routeParams.datasourceId,
      topic: routeParams.topicId
    });
    if (res.success) {
      metadataList.value = res.data.map((item: any) => {
        return {
          ...item,
          foreign_key_cascade: item.foreign_key_id ? [item.foreign_key_table, item.foreign_key_id] : [],
        }
      }) as any;
      console.log('metadataList', metadataList.value)
    }
    genTableColumnMap();
  } catch (error) {
    message.error('加载元数据失败');
  } finally {
    loading.value = false;
  }
};

const handleGetForeignKey = async () => {
  const optionList: Option[] = [];
  const res = await getForeignKey(routeParams.datasourceId);
  console.log(res.data)
  res.data?.map((item) => {
    if (item.primary_keys.length > 0) {
      const optionParent = {
        label: item.table_name,
        value: item.table_name,
        children: [] as Option[],
      };
      item.primary_keys.forEach((key: string) => {
        optionParent.children.push({
          label: key,
          value: key,
        });
      });
      optionList.push(optionParent);
    }
  })
  foreignKeyOptions.value = optionList;
}

const genTableColumnMap = () =>  {
  tableColumnMap.value = {};
  selectedTableColumnMap.value = {};
  metadataList.value.forEach(item => {
    if (!tableColumnMap.value.hasOwnProperty(item.tables_name)) {
      tableColumnMap.value[item.tables_name] = {};
    }
    tableColumnMap.value[item.tables_name][item.columns_name] = true;

    if (!selectedTableColumnMap.value.hasOwnProperty(item.tables_name)) {
      selectedTableColumnMap.value[item.tables_name] = [];
    }
    selectedTableColumnMap.value[item.tables_name].push(item.columns_name);
  })
}

// 处理编辑
const handleEditInTable = (record?: MetadataResponse) => {
  if (record) {
    // 从表格行点击编辑
    console.log('1', record);
    editForm.value = {...record};
    console.log('1', editForm.value);
    currentEditId.value = record.id;
  } else {
    message.warning('请选择一条记录进行编辑');
    return;
  }
  editModalVisible.value = true;
};

const handleEdit = () => {
  if (selectedRows.value.length === 1) {
    // 从顶部按钮点击编辑
    editForm.value = {...selectedRows.value[0]};
    console.log('2', editForm.value);
    currentEditId.value = selectedRows.value[0].id;
  } else {
    message.warning('请选择一条记录进行编辑');
    return;
  }
  editModalVisible.value = true;
};


// 保存编辑
const saveEdit = async () => {
  if (!currentEditId.value) {
    message.error('编辑ID不存在');
    return;
  }

  try {
    const formData = {
      ...editForm.value,
      id: currentEditId.value,
    }
    if (formData.hasOwnProperty('topic')) {
      delete (formData as any).topic;
      delete (formData as any).foreign_key_cascade;
    }
    const res = await updateMetadata(formData);

    if (res.success) {
      message.success('更新成功');
      editModalVisible.value = false;
      loadMetadata(); // 重新加载数据
    } else {
      message.error(res.err_msg || '更新失败');
    }
  } catch (error) {
    console.error('更新元数据错误:', error);
    message.error('更新失败');
  }
};

// 取消编辑
const cancelEdit = () => {
  editModalVisible.value = false;
};

const handleBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    message.warning('请选择要删除的记录');
    return;
  }
  const recordNames = selectedRows.value
      .map(record => `${record.db_name}.${record.tables_name}.${record.columns_name}`)
      .join('、');
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除以下 ${selectedRows.value.length} 条记录吗？${recordNames}`,
    onOk: async () => {
      const ids = selectedRows.value.map(record => record.id);
      try {
        const res = await deleteMetadata({ids: ids as string[]});
        if (res.success) {
          message.success('删除成功');
          loadMetadata(); // 重新加载数据
        } else {
          message.error(res.err_msg);
        }
      } catch (error) {
        message.error('删除失败');
      }
    }
  });
};

const handleDeleteInTable = (record?: MetadataResponse) => {
  if (record) {
    const recordName = record.db_name + '.' + record.tables_name + record.columns_name
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除记录${recordName}吗？`,
      onOk: async () => {
        const ids: string[] = [record.id as string]
        try {
          const res = await deleteMetadata({ids: ids});
          if (res.success) {
            message.success('删除成功');
            loadMetadata(); // 重新加载数据
          } else {
            message.error(res.err_msg);
          }
        } catch (error) {
          message.error('删除失败');
        }
      }
    });
  }
};

// const drawerOpen = async () => {
//   try {
//     const res = await getDatasourcesList(routeParams.datasourceId);
//     if (res.success) {
//       options.value = (res.data as any).map((table: datasourcesItem) => ({
//         label: table.tables_name,
//         value: table.tables_name
//       }));
//       drawerVisible.value = true;
//     }
//   } catch (error) {
//     console.error('获取数据表失败:', error);
//     message.error('获取表信息失败');
//   }
// }

const drawerOpen = async () => {
  try {
    drawerLoading.value = true;
    let res: any;
    if (routeParams.topicName !== '默认主题') {
      res = await getDefaultMetadataList(routeParams.datasourceId);
    } else {
      res = await getDatasourcesList(routeParams.datasourceId);
    }
    if (res.success) {
      // 清空旧数据
      selectedValues.value = [];
      currentTable.value = '';
      tableColumnRemarkMap.value = {};
      options.value = [];
      Object.keys(tableDataMap).forEach(key => delete tableDataMap[key]);

      const resData: any =  (res.data as any).sort((a: any, b: any) => {
        return a.tables_name.localeCompare(b.tables_name);
      });

      // 初始化数据
      resData.forEach((table: TableStructure) => {
        if (!tableColumnRemarkMap.value.hasOwnProperty(table.tables_name)) {
          tableColumnRemarkMap.value[table.tables_name] = {};
        }
        
        // 存储表格数据
        tableDataMap[table.tables_name] = table.tables_columns.map(col => {
          tableColumnRemarkMap.value[table.tables_name][col.columns_name] = col.tables_remark;
          return {
            ...col,
            columns_remark: col.columns_remark || '' // 处理 null 值
          }
        });

        // 初始化选项
        options.value.push({
          label: table.tables_name,
          value: table.tables_name
        });
      });

      drawerVisible.value = true;
      drawerLoading.value = false;
      console.log(tableColumnRemarkMap.value);
    }
  } catch (error) {
    console.error('获取数据表失败:', error);
    message.error('获取表信息失败');
    drawerLoading.value = false;
  }
};

// 添加计算属性获取当前表数据
const currentTableData = computed(() => {
  return currentTable.value ? tableDataMap[currentTable.value] || [] : [];
});

const handleSync = async () => {
  try {
    syncPercent.value = 0;
    syncLoading.value = true;
    const loadingTimer = setInterval(() => {
      // 生成 5 到 20 之间的随机数作为进度增加值
      const randomIncrement = Math.floor(Math.random() * 16) + 5; 
      syncPercent.value = Math.min(syncPercent.value + randomIncrement, 99);
      // 当进度达到 100% 时，清除定时器
    }, 300);
    await addColumnSexegesis({
      datasource_id: routeParams.datasourceId,
      topic: routeParams.topicId,
    });
    clearInterval(loadingTimer);
    syncPercent.value = 100;
    syncLoading.value = false;
    loadMetadata();
    setTimeout(() => {
      syncPercent.value = 0;
    }, 1000);
  } catch (error) {
    syncLoading.value = false;
  } 
}

const drawerClose = async () => {
  selectedValues.value = [];
  drawerVisible.value = false;
  setTimeout(() => {
    drawerKey.value ++;
  }, 300);
};

// 处理下拉框
const handleChange = (selectedKeys: string[]) => {
  selectedValues.value = selectedKeys;

  // 自动选择第一个表
  if (selectedKeys.length > 0) {
    currentTable.value = selectedKeys[0];
  } else {
    currentTable.value = '';
  }
  drawerRef.value?.validate();
};

const checkTableName = () => {
  if (selectedValues.value.length > 0) {
    return Promise.resolve();
  } else {
    return Promise.reject('请选择元数据表');
  }
}

// 处理菜单点击
const menuClick = (e: { key: string }) => {
  currentTable.value = e.key;
};

const onTableColumnSelectChange = (selectedRowKeysValue: string[]) => {
  selectedTableColumnMap.value[currentTable.value] = selectedRowKeysValue;
}

const submitAddData = async () => {
  if (selectedValues.value.length === 0) {
    message.warning('请选择要添加的表');
    return;
  }

  try {
    // 构造请求数据
    const data: any[] = [];
    for (const tableName of selectedValues.value) {
      const tableInfo = tableDataMap[tableName];
      // console.log(tableInfo)
      // if (!tableInfo) {
      //   message.error(`表 ${tableName} 的信息不存在，请重新选择`);
      //   continue;
      // }

      // 确保表结构中有列数据
      // if (!tableInfo.tables_columns || tableInfo.tables_columns.length === 0) {
      //   message.warning(`表 ${tableName} 没有列信息，无法添加`);
      //   continue;
      // }

      // 构造每个列的数据项
      if (selectedTableColumnMap.value.hasOwnProperty(tableName)) {
        tableInfo.forEach((column: any) => {
          if (selectedTableColumnMap.value[tableName].includes(column.columns_name)) {
            let tableRemark = '';
            if (tableColumnRemarkMap.value.hasOwnProperty(tableName) && tableColumnRemarkMap.value[tableName].hasOwnProperty(column.columns_name)) {
              tableRemark = tableColumnRemarkMap.value[tableName][column.columns_name];
            }
            data.push({
              connect_config_id: routeParams.datasourceId, // 数据源ID来自路由参数
              db_type: routeParams.db_type, 
              db_name: routeParams.database,
              tables_name: tableName,
              tables_remark: tableRemark, // 表备注，可选,
              columns_name: column.columns_name,
              columns_type: column.columns_type,
              columns_remark: column.columns_remark || '', // 列备注，可选
              // 外键信息根据实际情况处理，此处留空
            });
          }
        });
      }
    }

    if (data.length === 0) {
      message.warning('没有可添加的元数据');
      return;
    }

    // 调用添加接口
    const res = await addMetadata({
      topicId: routeParams.topicId,
      data: data as any
    });

    if (res.success) {
      message.success('添加成功');
      drawerClose();
      loadMetadata(); // 刷新元数据列表
    } else {
      message.error(res.err_msg || '添加失败');
    }
  } catch (error) {
    console.error('添加元数据失败:', error);
    message.error('添加失败，请稍后重试');
  }
};


</script>

<style scoped lang="scss">
.table-operations {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
}

.table-editor {
  margin-top: 16px;
}

.editable-cell {
  position: relative;
  .editable-cell-input-wrapper,
  .editable-cell-text-wrapper {
    padding-right: 24px;
  }

  .editable-cell-text-wrapper {
    width: 100%;
     /* 强制文本在一行显示 */
     white-space: nowrap;
    /* 隐藏溢出的内容 */
    overflow: hidden;
    /* 超出部分显示省略号 */
    text-overflow: ellipsis;
    padding: 5px 24px 5px 5px;
  }

  .editable-cell-icon,
  .editable-cell-icon-check {
    position: absolute;
    right: 0;
    width: 20px;
    cursor: pointer;
  }

  .editable-cell-icon {
    margin-top: 4px;
    display: none;
  }

  .editable-cell-icon-check {
    line-height: 28px;
  }

  .editable-cell-icon:hover,
  .editable-cell-icon-check:hover {
    color: #108ee9;
  }

  .editable-add-btn {
    margin-bottom: 8px;
  }
}
.editable-cell:hover .editable-cell-icon {
  display: inline-block;
}
.async-meta-percent {
  margin-right: 10px;
}
.foreign-key-container {
  display: flex;
  .foreign-key-wrapper {
    width: calc(100% - 10px);
  }
  :deep(.ant-cascader) {
    width: 100% !important;
  } 
}
</style>