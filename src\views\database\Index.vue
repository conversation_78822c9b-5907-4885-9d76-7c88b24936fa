<template>
  <div class="database-manage-container">
    <div class="page-header">
      <h2 class="page-title">数据源管理</h2>
      <a-button type="primary" @click="showAddDatabaseModal">
        <plus-outlined /> 添加数据源
      </a-button>
    </div>

    <!-- 数据库列表组件 -->
    <database-list 
      :refreshTrigger="refreshTrigger" 
      @refresh="handleRefresh"
      @edit="handleEditDatabase"
    />

    <!-- 添加/编辑数据库表单组件 -->
    <database-form 
      v-model:visible="modalVisible" 
      :isEdit="isEdit" 
      :editData="currentDatabase" 
      @success="handleDatabaseSuccess" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import DatabaseList from './DatabaseList.vue';
import DatabaseForm from './DatabaseForm.vue';
import type { DatabaseItem } from '../../types/database';

// 模态框可见性控制
const modalVisible = ref(false);
const isEdit = ref(false);
const currentDatabase = ref<DatabaseItem | null>(null);

// 刷新触发器
const refreshTrigger = ref(0);

// 显示添加数据源对话框
const showAddDatabaseModal = () => {
  isEdit.value = false;
  currentDatabase.value = null;
  modalVisible.value = true;
};

// 处理编辑数据库
const handleEditDatabase = (database: DatabaseItem) => {
  isEdit.value = true;
  currentDatabase.value = database;
  modalVisible.value = true;
};

// 处理数据库添加/编辑成功
const handleDatabaseSuccess = () => {
  // 触发数据库列表刷新
  refreshTrigger.value += 1;
};

// 处理数据库列表刷新
const handleRefresh = () => {
  // 可以在这里添加其他刷新相关逻辑
};
</script>

<style scoped>
.database-manage-container {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}
</style> 