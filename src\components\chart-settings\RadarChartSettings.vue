<template>
  <common-chart-settings
    :options="props.options"
    chart-type="radar"
    @update="updateOptions"
  >
    <!-- 样式配置 -->
    <template #style-settings>
      <!-- 雷达图指示器设置 -->
      <a-divider orientation="left">雷达图指示器</a-divider>
      
      <div v-for="(indicator, index) in chartConfig.radar.indicator" :key="index" class="indicator-item">
        <a-row :gutter="[16, 0]">
          <a-col :span="10">
            <a-form-item :label="`指标名称 ${index + 1}`">
              <a-input v-model:value="indicator.name" placeholder="请输入名称" @change="updateConfig" />
            </a-form-item>
          </a-col>
          <a-col :span="10">
            <a-form-item :label="`最大值 ${index + 1}`">
              <a-input-number 
                v-model:value="indicator.max" 
                style="width: 100%"
                @change="updateConfig"
              />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label=" " class="delete-btn-container">
              <a-button 
                type="primary" 
                danger 
                @click="removeIndicator(index)"
                shape="circle"
              >
                <delete-outlined />
              </a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </div>
      
      <a-button type="dashed" block @click="addIndicator" style="margin-bottom: 16px">
        <plus-outlined /> 添加指标
      </a-button>
      
      <!-- 雷达图形状和大小 -->
      <a-form-item label="形状">
        <a-radio-group v-model:value="chartConfig.radar.shape" @change="updateConfig">
          <a-radio-button value="polygon">多边形</a-radio-button>
          <a-radio-button value="circle">圆形</a-radio-button>
        </a-radio-group>
      </a-form-item>
      
      <a-form-item label="大小">
        <a-input-number 
          v-model:value="radarRadius"
          :min="30" 
          :max="100"
          :formatter="(value: number) => `${value}%`"
          :parser="(value: string) => Number(value.replace('%', ''))"
          style="width: 100%"
          @change="updateRadarRadius"
        />
      </a-form-item>
      
      <!-- 图例设置 -->
      <a-divider orientation="left">图例设置</a-divider>
      
      <a-form-item label="显示图例">
        <a-switch v-model:checked="chartConfig.legend.show" @change="updateConfig" />
      </a-form-item>
    </template>
  </common-chart-settings>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import CommonChartSettings from './CommonChartSettings.vue';
import type { ChartOptions } from '@/types/chart';

const props = defineProps({
  options: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update']);

// 雷达图半径
const radarRadius = ref(75);

// 直接访问config部分的计算属性
const chartConfig = computed({
  get: () => {
    // 确保config对象存在
    if (!props.options.config) {
      return getDefaultConfig();
    }
    return props.options.config;
  },
  set: (newConfig) => {
    const updatedOptions = {
      ...props.options,
      config: newConfig
    };
    emit('update', updatedOptions);
  }
});

// 获取默认配置
const getDefaultConfig = () => {
  return {
    type: 'radar',
    title: {
      text: '',
      show: false
    },
    tooltip: {},
    legend: {
      show: true,
      data: ['预算分配', '实际开销']
    },
    radar: {
      shape: 'polygon',
      radius: '75%',
      indicator: [
        { name: '销售', max: 6500 },
        { name: '管理', max: 16000 },
        { name: '信息技术', max: 30000 },
        { name: '客服', max: 38000 },
        { name: '研发', max: 52000 },
        { name: '市场', max: 25000 }
      ]
    },
    series: [{
      name: '预算 vs 开销',
      type: 'radar',
      data: [
        {
          value: [4300, 10000, 28000, 35000, 50000, 19000],
          name: '预算分配',
          areaStyle: { opacity: 0.5 },
          lineStyle: { width: 2 },
          itemStyle: { color: '#5470c6' }
        },
        {
          value: [5000, 14000, 28000, 31000, 42000, 21000],
          name: '实际开销',
          areaStyle: { opacity: 0.5 },
          lineStyle: { width: 2 },
          itemStyle: { color: '#91cc75' }
        }
      ]
    }]
  };
};

// 更新配置
const updateOptions = (newOptions: ChartOptions) => {
  emit('update', newOptions);
};

// 更新config
const updateConfig = () => {
  // 触发响应式更新
  chartConfig.value = { ...chartConfig.value };
};

// 更新雷达图半径
const updateRadarRadius = () => {
  const config = { ...chartConfig.value };
  config.radar.radius = `${radarRadius.value}%`;
  chartConfig.value = config;
};

// 添加指标
const addIndicator = () => {
  const config = { ...chartConfig.value };
  config.radar.indicator.push({
    name: `新指标${config.radar.indicator.length + 1}`,
    max: 10000
  });
  
  // 更新所有系列的value数组长度
  config.series[0].data.forEach((series: any) => {
    if (!series.value) {
      series.value = Array(config.radar.indicator.length).fill(0);
    } else {
      series.value.push(0);
    }
  });
  
  chartConfig.value = config;
};

// 移除指标
const removeIndicator = (index: number) => {
  const config = { ...chartConfig.value };
  config.radar.indicator.splice(index, 1);
  
  // 更新所有系列的value数组
  config.series[0].data.forEach((series: any) => {
    if (series.value && series.value.length > index) {
      series.value.splice(index, 1);
    }
  });
  
  chartConfig.value = config;
};
</script>

<style scoped>
.indicator-item {
  padding: 10px;
  margin-bottom: 12px;
  border: 1px dashed #ddd;
  border-radius: 4px;
}

.delete-btn-container {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  height: 100%;
}
</style> 