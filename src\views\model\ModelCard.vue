<template>
  <a-card class="model-card">
    <div class="model-card-header">
      <div class="model-icon">
        <img :src="getModelIcon(model.provider || getProviderFromModelName(model.model_name))" :alt="model.name || model.model_name" />
      </div>
      <div class="model-name" :title="model.name || model.model_name">{{ model.name || model.model_name }}</div>
      <a-dropdown placement="bottomRight">
        <a-button type="text" class="more-button">
          <ellipsis-outlined />
        </a-button>
        <template #overlay>
          <a-menu>
            <a-menu-item key="stop" @click="showStopConfirm">
              <pause-circle-outlined /> 停止模型
            </a-menu-item>
            <a-menu-item key="start" @click="showStartConfirm">
              <play-circle-outlined /> 启动模型
            </a-menu-item>
            <a-menu-item key="stopAndDelete" @click="showStopAndDeleteConfirm">
              <delete-outlined /> 停止并删除模型
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>

    <div class="model-info">
      <div class="info-item">
        <span class="label">Host:</span>
        <span class="value">{{ model.host }}</span>
      </div>
      <div class="info-item">
        <span class="label">Port:</span>
        <span class="value">{{ model.port }}</span>
      </div>
      <div class="info-item">
        <span class="label">Last Heart Beat:</span>
        <span class="value">{{ model.last_heartbeat }}</span>
      </div>
    </div>

    <div class="model-tags">
      <a-tag color="green" v-if="model.healthy">Healthy</a-tag>
      <a-tag color="red" v-else>Unhealthy</a-tag>
      <a-tag color="blue">{{ model.worker_type }}</a-tag>
    </div>
    
    <!-- 确认弹层 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :ok-text="modalOkText"
      :cancel-text="modalCancelText"
      @ok="handleModalConfirm"
    >
      <p>{{ modalContent }}</p>
    </a-modal>
  </a-card>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import {
  EllipsisOutlined,
  DeleteOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined
} from '@ant-design/icons-vue';
import type { ModelItem } from '../../types/model';

// 引入所有模型图标
import gptIcon from '../../assets/icons/model/gpt.png';
import claudeIcon from '../../assets/icons/model/claude.png';
import mistralIcon from '../../assets/icons/model/mistral.png';
import geminiIcon from '../../assets/icons/model/gemini.png';
import metaIcon from '../../assets/icons/model/meta.png';
import glmIcon from '../../assets/icons/model/glm.png';
import gemmaIcon from '../../assets/icons/model/gemma.png';
import qwenIcon from '../../assets/icons/model/qwen.png';
import doubaoIcon from '../../assets/icons/model/doubao.png';
import deepseekIcon from '../../assets/icons/model/deepseek.png';
import hunyuanIcon from '../../assets/icons/model/hunyuan.png';
import sparkIcon from '../../assets/icons/model/spark.png';
import tiangongIcon from '../../assets/icons/model/tiangong.png';
import baichuanIcon from '../../assets/icons/model/baichuan.png';
import moonshotIcon from '../../assets/icons/model/moonshot.png';
import jinaIcon from '../../assets/icons/model/jina.png';
import wenxinIcon from '../../assets/icons/model/wenxin.png';
import yiIcon from '../../assets/icons/model/yi.png';

// 引入提供商图标
import openaiIcon from '../../assets/icons/provider/openai.png';
import baaiIcon from '../../assets/icons/provider/baai.png';
import googleIcon from '../../assets/icons/provider/google.png';
import microsoftIcon from '../../assets/icons/provider/microsoft.png';
import vllmIcon from '../../assets/icons/provider/vllm.png';
import zhipuIcon from '../../assets/icons/provider/zhipu.png';
import giteeIcon from '../../assets/icons/provider/gitee.png';
import ollamaIcon from '../../assets/icons/provider/ollama.png';
import huggingfaceIcon from '../../assets/icons/provider/huggingface.png';
import siliconcloudIcon from '../../assets/icons/provider/siliconcloud.png';
import volcengineIcon from '../../assets/icons/provider/volcengine.png';
import modelscopeIcon from '../../assets/icons/provider/modelscope.png';
import qianfanIcon from '../../assets/icons/provider/qianfan.png';
import tongyiIcon from '../../assets/icons/provider/tongyi.png';
import llamaCppIcon from '../../assets/icons/provider/llama.cpp.png';

// 提供商图标映射
const providerIcons: Record<string, any> = {
  openai: openaiIcon,
  anthropic: claudeIcon,
  claude: claudeIcon,
  mistral: mistralIcon,
  gemini: geminiIcon,
  google: googleIcon,
  meta: metaIcon,
  microsoft: microsoftIcon,
  baai: baaiIcon,
  jina: jinaIcon,
  vllm: vllmIcon,
  zhipu: zhipuIcon,
  gitee: giteeIcon,
  wenxin: wenxinIcon,
  yi: yiIcon,
  ollama: ollamaIcon,
  huggingface: huggingfaceIcon,
  moonshot: moonshotIcon,
  siliconcloud: siliconcloudIcon,
  baichuan: baichuanIcon,
  volcengine: volcengineIcon,
  spark: sparkIcon,
  modelscope: modelscopeIcon,
  deepseek: deepseekIcon,
  qianfan: qianfanIcon,
  tongyi: tongyiIcon,
  baiducloud: qianfanIcon, // 百度智能云使用千帆图标
  'llama.cpp': llamaCppIcon,
};

// 模型图标映射
const modelIcons: Record<string, any> = {
  gpt: gptIcon,
  openai: gptIcon,
  claude: claudeIcon,
  anthropic: claudeIcon,
  mistral: mistralIcon,
  gemini: geminiIcon,
  meta: metaIcon,
  llama: metaIcon,
  glm: glmIcon,
  chatglm: glmIcon,
  gemma: gemmaIcon,
  qwen: qwenIcon,
  doubao: doubaoIcon,
  deepseek: deepseekIcon,
  hunyuan: hunyuanIcon,
  spark: sparkIcon,
  tiangong: tiangongIcon,
  baichuan: baichuanIcon,
  moonshot: moonshotIcon,
  jina: jinaIcon,
  wenxin: wenxinIcon,
  yi: yiIcon,
};

// 获取提供商图标
const getModelIcon = (provider: string) => {
  const providerLower = provider.toLowerCase();
  return providerIcons[providerLower] || huggingfaceIcon; // 默认使用 huggingface 图标
};

// 从模型名称中提取提供商信息
const getProviderFromModelName = (modelName: string): string => {
  if (!modelName) return '';
  
  const lowerModelName = modelName.toLowerCase();

  // 按优先级检查模型名称中包含的关键词
  const modelPrefixes = [
    { keyword: 'gpt', provider: 'openai' },
    { keyword: 'openai', provider: 'openai' },
    { keyword: 'claude', provider: 'claude' },
    { keyword: 'anthropic', provider: 'claude' },
    { keyword: 'gemini', provider: 'gemini' },
    { keyword: 'mistral', provider: 'mistral' },
    { keyword: 'llama.cpp', provider: 'llama.cpp' },
    { keyword: 'llama-cpp', provider: 'llama.cpp' },
    { keyword: 'llamacpp', provider: 'llama.cpp' },
    { keyword: 'llama', provider: 'meta' },
    { keyword: 'qwen', provider: 'tongyi' },
    { keyword: 'deepseek', provider: 'deepseek' },
    { keyword: 'yi', provider: 'yi' },
    { keyword: 'baichuan', provider: 'baichuan' },
    { keyword: 'wenxin', provider: 'wenxin' },
    { keyword: 'qianfan', provider: 'qianfan' },
    { keyword: 'chatglm', provider: 'zhipu' },
    { keyword: 'glm', provider: 'zhipu' },
    { keyword: 'bge', provider: 'baai' },
    { keyword: 'hunyuan', provider: 'hunyuan' },
    { keyword: 'spark', provider: 'spark' },
    { keyword: 'ernie', provider: 'wenxin' },
    { keyword: 'moonshot', provider: 'moonshot' },
    { keyword: 'gemma', provider: 'google' },
    { keyword: 'jina', provider: 'jina' },
  ];

  for (const { keyword, provider } of modelPrefixes) {
    if (lowerModelName.includes(keyword)) {
      return provider;
    }
  }
  
  return 'huggingface'; // 默认返回 huggingface
};

const props = defineProps<{
  model: ModelItem;
}>();

const emit = defineEmits<{
  (e: 'stop', model: ModelItem): void;
  (e: 'start', model: ModelItem): void;
  (e: 'stopAndDelete', model: ModelItem): void;
}>();

// 弹层相关状态
const modalVisible = ref(false);
const modalTitle = ref('');
const modalContent = ref('');
const modalOkText = ref('确认');
const modalCancelText = ref('取消');
const currentAction = ref<'stop' | 'start' | 'stopAndDelete'>('stop');

// 显示停止模型确认弹层
const showStopConfirm = () => {
  modalTitle.value = '停止模型';
  modalContent.value = `确定要停止模型 ${props.model.name || props.model.model_name} 吗？`;
  currentAction.value = 'stop';
  modalVisible.value = true;
};

// 显示启动模型确认弹层
const showStartConfirm = () => {
  modalTitle.value = '启动模型';
  modalContent.value = `确定要启动模型 ${props.model.name || props.model.model_name} 吗？`;
  currentAction.value = 'start';
  modalVisible.value = true;
};

// 显示停止并删除模型确认弹层
const showStopAndDeleteConfirm = () => {
  modalTitle.value = '停止并删除模型';
  modalContent.value = `确定要停止并删除模型 ${props.model.name || props.model.model_name} 吗？此操作不可恢复！`;
  currentAction.value = 'stopAndDelete';
  modalVisible.value = true;
};

// 处理弹层确认事件
const handleModalConfirm = () => {
  if (currentAction.value === 'stop') {
    emit('stop', props.model);
  } else if (currentAction.value === 'start') {
    emit('start', props.model);
  } else if (currentAction.value === 'stopAndDelete') {
    emit('stopAndDelete', props.model);
  }
  modalVisible.value = false;
};


</script>

<style scoped>
.model-card {
  height: 100%;
  transition: all 0.3s;
  border-radius: 8px;
}

.model-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.model-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.model-icon {
  width: 64px;
  height: 64px;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.model-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.model-name {
  flex: 1;
  font-weight: 500;
  font-size: 16px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.more-button {
  color: #999;
}

.model-info {
  margin: 16px 0;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
  text-align: left;
}

.label {
  color: #666;
  width: 120px;
  text-align: left;
}

.value {
  color: #333;
  flex: 1;
  text-align: left;
}

.model-tags {
  margin-top: 12px;
  text-align: left;
}
</style>
