<template>
  <common-chart-settings
    :options="props.options"
    chart-type="scatter"
    @update="updateOptions"
  >
    <!-- 样式配置 -->
    <template #style-settings>
      <!-- X轴设置 -->
      <a-divider orientation="left">X轴设置</a-divider>
      
      <a-form-item label="X轴名称">
        <a-input v-model:value="chartConfig.xAxis.name" placeholder="请输入X轴名称" />
      </a-form-item>
      
      <a-form-item label="X轴最小值">
        <a-input-number v-model:value="chartConfig.xAxis.min" style="width: 100%" />
      </a-form-item>
      
      <a-form-item label="X轴最大值">
        <a-input-number v-model:value="chartConfig.xAxis.max" style="width: 100%" />
      </a-form-item>
      
      <!-- Y轴设置 -->
      <a-divider orientation="left">Y轴设置</a-divider>
      
      <a-form-item label="Y轴名称">
        <a-input v-model:value="chartConfig.yAxis.name" placeholder="请输入Y轴名称" />
      </a-form-item>
      
      <a-form-item label="Y轴最小值">
        <a-input-number v-model:value="chartConfig.yAxis.min" style="width: 100%" />
      </a-form-item>
      
      <a-form-item label="Y轴最大值">
        <a-input-number v-model:value="chartConfig.yAxis.max" style="width: 100%" />
      </a-form-item>
    </template>
  </common-chart-settings>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import CommonChartSettings from './CommonChartSettings.vue';
import type { ChartOptions } from '@/types/chart';

const props = defineProps({
  options: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update']);

// 直接访问config部分的计算属性
const chartConfig = computed({
  get: () => {
    // 确保config对象存在
    if (!props.options.config) {
      return getDefaultConfig();
    }
    return props.options.config;
  },
  set: (newConfig) => {
    const updatedOptions = {
      ...props.options,
      config: newConfig
    };
    emit('update', updatedOptions);
  }
});

// 获取默认配置
const getDefaultConfig = () => {
  return {
    type: 'scatter',
    title: {
      text: '',
      show: false
    },
    tooltip: {},
    xAxis: {
      name: '',
      type: 'value',
      min: null,
      max: null
    },
    yAxis: {
      name: '',
      type: 'value',
      min: null,
      max: null
    },
    series: [
      {
        name: '系列1',
        type: 'scatter',
        symbolSize: 20,
        data: [
          [10.0, 8.04],
          [8.0, 6.95]
        ],
        itemStyle: {
          color: '#1890ff'
        }
      }
    ]
  };
};

// 更新配置
const updateOptions = (newOptions: ChartOptions) => {
  emit('update', newOptions);
};

// 更新config
const updateConfig = () => {
  // 触发响应式更新
  chartConfig.value = { ...chartConfig.value };
};
</script> 