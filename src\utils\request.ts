import axios from 'axios';
import type { AxiosRequestConfig, AxiosResponse } from 'axios';
import { LOGIN_ACCESS_TOKEN } from '@/constant';
import { useUserStore } from '@/store/user';
import { message } from 'ant-design-vue';

interface ApiResponse<T> {
  success: boolean;
  err_code: number | null;
  err_msg: string | null;
  data: T | null;
}

/**
 * 将通配符模式转换为正则表达式
 * @param pattern 通配符模式，例如: /user/* 或 /api/dashboard/**
 * @returns 正则表达式对象
 */
const convertPatternToRegex = (pattern: string): RegExp => {
  // 转义特殊字符
  let regexStr = pattern
    .replace(/[.*+?^${}()|[\]\\]/g, '\\$&') // 转义正则特殊字符
    .replace(/\\\*/g, '.*'); // 把转义后的 \* 替换为 .*

  // 处理 /** 结尾的情况 (匹配任意子路径)
  if (regexStr.endsWith('\\.\\*\\.\\*')) {
    regexStr = regexStr.replace(/\\\.\\\*\\\.\\\*$/, '(?:/.*)?');
  }

  // 确保完全匹配
  return new RegExp(`^${regexStr}$`);
};

/**
 * 匹配URL是否符合指定模式
 * @param url 需要检查的URL
 * @param pattern 匹配模式
 * @returns 是否匹配
 */
const matchUrlPattern = (url: string, pattern: string): boolean => {
  // 处理简单的精确匹配
  if (!pattern.includes('*')) {
    return url === pattern || url.startsWith(pattern + '/');
  }

  // 处理 /user/* 这样的单层通配符
  if (pattern.endsWith('/*')) {
    const basePath = pattern.slice(0, -2);
    const pathParts = url.split('/');
    const patternParts = basePath.split('/');
    
    // 检查前缀是否匹配
    for (let i = 0; i < patternParts.length; i++) {
      if (patternParts[i] !== pathParts[i]) {
        return false;
      }
    }
    
    // 确保不会匹配到更深层次的路径
    return pathParts.length === patternParts.length + 1;
  }
  
  // 处理 /user/** 这样的多层通配符
  if (pattern.endsWith('/**')) {
    const basePath = pattern.slice(0, -3);
    return url === basePath || url.startsWith(basePath + '/');
  }
  
  // 处理中间带有通配符的情况，例如 /api/*/data
  const regex = convertPatternToRegex(pattern);
  return regex.test(url);
};

// 从环境变量中获取mock路径配置
const getMockPatterns = (): string[] => {
  const mockPathsStr = import.meta.env.VITE_MOCK_PATHS || '';
  return mockPathsStr.split(',').map((path: string) => path.trim()).filter(Boolean);
};

// 判断URL是否应该走mock
const shouldUseMock = (url: string): boolean => {
  // 判断是否启用mock
  const enableMock = import.meta.env.VITE_ENABLE_MOCK !== 'false';
  if (!enableMock) return false;

  // 获取需要mock的路径模式
  const mockPatterns = getMockPatterns();
  
  // 检查URL是否匹配任一模式
  return mockPatterns.some(pattern => matchUrlPattern(url, pattern));
};

// 创建两个 axios 实例，一个用于mock接口，一个用于其他接口
const mockService = axios.create({
  // mock接口直接使用相对路径，会被mock拦截
  baseURL: '/',
  timeout: Number(import.meta.env.VITE_REQUEST_TIMEOUT) || 10000
});

const apiService = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: Number(import.meta.env.VITE_REQUEST_TIMEOUT) || 10000
});

const rootApiService = axios.create({
  baseURL: import.meta.env.VITE_ROOT_API_BASE_URL,
  timeout: Number(import.meta.env.VITE_REQUEST_TIMEOUT) || 10000
});

// 请求拦截器 - mock接口
mockService.interceptors.request.use(
  (config) => {
    // 可以在这里添加 token 或其他请求头
    const token = localStorage.getItem(LOGIN_ACCESS_TOKEN);
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 请求拦截器 - 其他API接口
apiService.interceptors.request.use(
  (config) => {
    // 可以在这里添加 token 或其他请求头
    const token = localStorage.getItem(LOGIN_ACCESS_TOKEN);
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 请求拦截器 - 其他API接口
rootApiService.interceptors.request.use(
  (config) => {
    // 可以在这里添加 token 或其他请求头
    const token = localStorage.getItem(LOGIN_ACCESS_TOKEN);
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - mock接口
mockService.interceptors.response.use(
  (response: AxiosResponse) => {
    const res = response;
    if (res.status !== 200) {
      console.error('接口请求错误');
      return Promise.reject(new Error('请求错误'));
    } else {
      return res.data;
    }
  },
  (error) => {
    console.error('请求失败: ' + error.message);
    return Promise.reject(error);
  }
);

// 响应拦截器 - 其他API接口
apiService.interceptors.response.use(
  (response: AxiosResponse) => {
    const res = response;
    if (res.status === 401 || res.status === 403) {
      message.error('登录已过期，请重新登录');
      localStorage.removeItem(LOGIN_ACCESS_TOKEN);
      window.location.href = '/login'; // 跳转到登录页面
    } else if (res.status !== 200) {
      console.error('接口请求错误');
      return Promise.reject(new Error('请求错误'));
    }  else if (res.data.hasOwnProperty('success') && !(res.data as any).success) {
      message.error((res.data as any).err_msg);
      return Promise.reject(new Error((res.data as any).err_msg));
    } else {
      return res.data;
    }
  },
  (error) => {
    if (error.response?.status === 401 || error.response?.status === 403) {
      localStorage.removeItem(LOGIN_ACCESS_TOKEN);
      if (error.response?.config?.url?.includes('/v2/serve/logout')) { 
        message.success('已成功退出登录');
        setTimeout(() => {
          window.location.href = '/login'; // 跳转到登录页面
        }, 300);
        return Promise.reject(new Error('已成功退出登录'));
      } else if (useUserStore().isFirstLogin) {
        message.error('权限不足');
        return Promise.reject(new Error('权限不足'));
      } else {
        message.error('登录已过期，请重新登录');
        setTimeout(() => {
          window.location.href = '/login'; // 跳转到登录页面
        }, 300);
        return Promise.reject(new Error('登录已过期，请重新登录'));
      }
    }
    console.error(error);
    message.error(error?.response?.data?.err_msg || error.message);
    return Promise.reject(error?.response?.data?.err_msg || error.message);
  }
);


// 响应拦截器 - 其他API接口
rootApiService.interceptors.response.use(
  (response: AxiosResponse) => {
    const res = response;
    if (res.status === 401 || res.status === 403) {
      message.error('登录已过期，请重新登录');
      localStorage.removeItem(LOGIN_ACCESS_TOKEN);
      window.location.href = '/login'; // 跳转到登录页面
    } else if (res.status !== 200) {
      console.error('接口请求错误');
      return Promise.reject(new Error('请求错误'));
    } else if (res.data.hasOwnProperty('success') && !(res.data as any).success) {
      message.error((res.data as any).err_msg);
      return Promise.reject(new Error((res.data as any).err_msg));
    } else {
      return res.data;
    }
  },
  (error) => {
    console.error('请求失败: ' + error.message);
    message.error(error?.response?.data?.err_msg || error.message);
    return Promise.reject(error?.response?.data?.err_msg || error.message);
  }
);

/**
 * 封装 GET 请求
 * @param url 请求地址
 * @param params 请求参数
 * @param config 额外配置
 * @returns Promise
 */
export function get<T>(url: string, params?: any, config?: AxiosRequestConfig, isRootApi = false): Promise<T> {
  const service = shouldUseMock(url) ? mockService : (isRootApi ? rootApiService : apiService);
  return service.get(url, { params, ...config });
}

/**
 * 封装 POST 请求
 * @param url 请求地址
 * @param data 请求数据
 * @param config 额外配置
 * @returns Promise
 */
export function post<T>(url: string, data?: any, config?: AxiosRequestConfig, isRootApi = false): Promise<T> {
  const service = shouldUseMock(url) ? mockService : (isRootApi ? rootApiService : apiService);
  return service.post(url, data, config);
}

/**
 * 封装 PUT 请求
 * @param url 请求地址
 * @param data 请求数据
 * @param config 额外配置
 * @returns Promise
 */
export function put<T>(url: string, data?: any, config?: AxiosRequestConfig, isRootApi = false): Promise<T> {
  const service = shouldUseMock(url) ? mockService : (isRootApi ? rootApiService : apiService);
  return service.put(url, data, config);
}

/**
 * 封装 DELETE 请求
 * @param url 请求地址
 * @param config 额外配置
 * @returns Promise
 */
export function del<T>(url: string, config?: AxiosRequestConfig, isRootApi = false): Promise<T> {
  const service = shouldUseMock(url) ? mockService : (isRootApi ? rootApiService : apiService);
  return service.delete(url, config);
}

export default {
  get,
  post,
  put,
  delete: del,
}; 