<template>
  <a-card class="database-card">
    <div class="database-card-header">
      <div class="database-icon">
        <img :src="getDbIcon(database.type)" :alt="database.params.database" />
      </div>
      <div class="database-name" :title="database.params.database">{{ database.params.database }}</div>
      <a-dropdown placement="bottomRight">
        <a-button type="text" class="more-button" @click.stop>
          <ellipsis-outlined />
        </a-button>
        <template #overlay>
          <a-menu>
            <a-menu-item key="edit" @click="handleEdit">
              <edit-outlined /> 编辑数据源
            </a-menu-item>
            <a-menu-item key="delete" @click="handleDelete">
              <delete-outlined /> 删除数据源
            </a-menu-item>
            <!-- <a-menu-item key="delete" @click="handleMetaData">
              <code-outlined /> 元数据管理
            </a-menu-item> -->
          </a-menu>
        </template>
      </a-dropdown>
    </div>

    <div class="database-info">
      <div class="info-item">
        <span class="label">Host:</span>
        <span class="value">{{ database.params.host }}</span>
      </div>
      <div class="info-item">
        <span class="label">Port:</span>
        <span class="value">{{ database.params.port }}</span>
      </div>
      <div class="info-item">
        <span class="label">User:</span>
        <span class="value">{{ database.params.user }}</span>
      </div>
      <div class="info-item">
        <span class="label">Database:</span>
        <span class="value">{{ database.params.database }}</span>
      </div>
      <div class="info-item" v-if="database.description">
        <span class="label">描述:</span>
        <span class="value">{{ database.description }}</span>
      </div>
    </div>

    <a-button class="meta-management-btn" @click="handleMetaData">元数据管理</a-button>

    <!-- 确认弹层 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :ok-text="modalOkText"
      :cancel-text="modalCancelText"
      @ok="handleModalConfirm"
    >
      <p>{{ modalContent }}</p>
    </a-modal>
  </a-card>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router'
import {
  EllipsisOutlined,
  DeleteOutlined,
  EditOutlined,
  CodeOutlined
} from '@ant-design/icons-vue';
import type { DatabaseItem } from '../../types/database';

// 引入所有数据库图标
import mysqlIcon from '../../assets/icons/database/mysql.png';
import postgresqlIcon from '../../assets/icons/database/postgresql.png';
import sqlserverIcon from '../../assets/icons/database/sqlserver.png';
import oracleIcon from '../../assets/icons/database/oracle.png';
import sqliteIcon from '../../assets/icons/database/sqlite.png';
import cassandraIcon from '../../assets/icons/database/cassandra.png';
import clickhouseIcon from '../../assets/icons/database/clickhouse.png';
import couchbaseIcon from '../../assets/icons/database/couchbase.png';
import db2Icon from '../../assets/icons/database/db2.png';
import dorisIcon from '../../assets/icons/database/doris.png';
import firebirdIcon from '../../assets/icons/database/firebird.png';
import hanaIcon from '../../assets/icons/database/hana.png';
import hiveIcon from '../../assets/icons/database/hive.png';
import kylinIcon from '../../assets/icons/database/kylin.png';
import mariadbIcon from '../../assets/icons/database/mariadb.png';
import oceanbaseIcon from '../../assets/icons/database/oceanbase.png';
import prestodbIcon from '../../assets/icons/database/prestodb.png';
import snowflakeIcon from '../../assets/icons/database/snowflake.png';
import teradataIcon from '../../assets/icons/database/teradata.png';
import tidbIcon from '../../assets/icons/database/tidb.png';
import verticaIcon from '../../assets/icons/database/vertica.png';
import accessIcon from '../../assets/icons/database/access.png';
import kingbaseIcon from '../../assets/icons/database/kingbase.png';
import damengIcon from '../../assets/icons/database/dameng.png';
import gaussdbIcon from '../../assets/icons/database/gaussdb.png';
import tugraphIcon from '../../assets/icons/database/tugraph.png';
import sparkIcon from '../../assets/icons/database/spark.png';
import duckdbIcon from '../../assets/icons/database/duckdb.png';
import starrocksIcon from '../../assets/icons/database/starrocks.png';

// 图标映射
const dbIcons: Record<string, any> = {
  mysql: mysqlIcon,
  postgresql: postgresqlIcon,
  sqlserver: sqlserverIcon,
  oracle: oracleIcon,
  sqlite: sqliteIcon,
  cassandra: cassandraIcon,
  clickhouse: clickhouseIcon,
  couchbase: couchbaseIcon,
  db2: db2Icon,
  doris: dorisIcon,
  firebird: firebirdIcon,
  hana: hanaIcon,
  hive: hiveIcon,
  kylin: kylinIcon,
  mariadb: mariadbIcon,
  oceanbase: oceanbaseIcon,
  prestodb: prestodbIcon,
  snowflake: snowflakeIcon,
  teradata: teradataIcon,
  tidb: tidbIcon,
  vertica: verticaIcon,
  access: accessIcon,
  kingbase: kingbaseIcon,
  dameng: damengIcon,
  gaussdb: gaussdbIcon,
  tugraph: tugraphIcon,
  spark: sparkIcon,
  mssql: sqlserverIcon,
  duckdb: duckdbIcon,
  starrocks: starrocksIcon
};

const props = defineProps<{
  database: DatabaseItem;
}>();

const router = useRouter()

const emit = defineEmits<{
  (e: 'edit', database: DatabaseItem): void;
  (e: 'delete', database: DatabaseItem): void;
}>();

// 弹层相关状态
const modalVisible = ref(false);
const modalTitle = ref('');
const modalContent = ref('');
const modalOkText = ref('确认');
const modalCancelText = ref('取消');
const currentAction = ref<'edit' | 'delete'>('edit');

// 获取数据库图标
const getDbIcon = (type: string) => {
  const typeLower = type.toLowerCase();
  return dbIcons[typeLower] || mysqlIcon;
};

// 编辑数据库
const handleEdit = () => {
  emit('edit', props.database);
};

// 删除数据库
const handleDelete = () => {
  emit('delete', props.database);
  // modalTitle.value = '删除数据源';
  // modalContent.value = `确定要删除数据源 ${props.database.params.database} 吗？`;
  // currentAction.value = 'delete';
  // modalVisible.value = true;
};


// 元数据管理
const handleMetaData = () => {
  console.log('handleMetaData', props.database)
  router.push({
    path: '/topic',
    query: {
      datasourceId: props.database.id,
      database: props.database.params.database,
      db_type: props.database.type,
      host: props.database.params.host,
    }
  })
}

// 处理弹层确认事件
const handleModalConfirm = () => {
  if (currentAction.value === 'delete') {
    emit('delete', props.database);
  }
  modalVisible.value = false;
};
</script>

<style scoped lang="scss">
.database-card {
  height: 100%;
  transition: all 0.3s;
  border-radius: 8px;
  // cursor: pointer;
  padding-bottom: 24px;
  position: relative;
  .meta-management-btn {
    position: absolute;
    right: 24px;
    bottom: 14px;
  }
}

.database-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.database-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.database-icon {
  width: 64px;
  height: 64px;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.database-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.database-name {
  flex: 1;
  font-weight: 500;
  font-size: 16px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.more-button {
  color: #999;
}

.database-info {
  margin: 16px 0;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
  text-align: left;
}

.label {
  color: #666;
  width: 120px;
  text-align: left;
  // 允许在任意字符处换行
  word-break: break-all;
  // 允许长单词或 URL 地址换行到下一行
  overflow-wrap: break-word;
}

.value {
  color: #333;
  flex: 1;
  text-align: left;
  // 允许在任意字符处换行
  word-break: break-all;
  // 允许长单词或 URL 地址换行到下一行
  overflow-wrap: break-word;
  // 限制显示的行数为 2
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style> 