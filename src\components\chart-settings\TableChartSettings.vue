<template>
  <common-chart-settings
    :options="props.options"
    chart-type="table"
    @update="updateOptions"
  >
    <!-- 样式配置 -->
    <template #style-settings>
      <!-- 表格样式设置 -->
      <a-divider orientation="left">表格样式</a-divider>
      
      <a-form-item label="显示边框">
        <a-switch v-model:checked="chartConfig.bordered" />
      </a-form-item>
      
      <a-form-item label="显示表头">
        <a-switch v-model:checked="chartConfig.showHeader" />
      </a-form-item>
      
      <a-form-item label="显示分页">
        <a-switch v-model:checked="chartConfig.pagination.show" />
      </a-form-item>
      
      <a-form-item label="每页行数" v-if="chartConfig.pagination.show">
        <a-input-number
          v-model:value="chartConfig.pagination.pageSize"
          :min="5"
          :max="100"
          :step="5"
          @change="updateConfig"
        />
      </a-form-item>
      
      <a-form-item label="表格大小">
        <a-select v-model:value="chartConfig.size" @change="updateConfig">
          <a-select-option value="small">小</a-select-option>
          <a-select-option value="middle">中</a-select-option>
          <a-select-option value="large">大</a-select-option>
        </a-select>
      </a-form-item>
      
      <a-form-item label="斑马纹">
        <a-switch v-model:checked="chartConfig.stripe" />
      </a-form-item>
      
      <a-form-item label="设置列宽度">
        <a-switch v-model:checked="showColumnWidthSetting" />
      </a-form-item>
      
      <!-- 列设置 -->
      <template v-if="showColumnWidthSetting">
        <a-divider orientation="left">列设置</a-divider>
        
        <div v-for="(column, index) in chartConfig.columns" :key="index">
          <a-row :gutter="[16, 0]">
            <a-col :span="12">
              <a-form-item :label="`列 ${index + 1} 标题`">
                <a-input 
                  v-model:value="column.title" 
                  placeholder="列标题"
                  @change="updateConfig"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item :label="`列 ${index + 1} 宽度`">
                <a-input-number
                  v-model:value="column.width"
                  :min="50"
                  :max="500"
                  @change="updateConfig"
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </template>
    </template>
  </common-chart-settings>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import CommonChartSettings from './CommonChartSettings.vue';
import type { ChartOptions } from '@/types/chart';

const props = defineProps({
  options: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update']);

// 列设置
const showColumnWidthSetting = ref(false);

// 直接访问config部分的计算属性
const chartConfig = computed({
  get: () => {
    // 确保config对象存在
    if (!props.options.config) {
      return getDefaultConfig();
    }
    return props.options.config;
  },
  set: (newConfig) => {
    const updatedOptions = {
      ...props.options,
      config: newConfig
    };
    emit('update', updatedOptions);
  }
});

// 获取默认配置
const getDefaultConfig = () => {
  return {
    type: 'table',
    title: {
      text: '',
      show: false
    },
    bordered: true,
    showHeader: true,
    size: 'middle',
    stripe: false,
    columns: [
      { title: '列1', dataIndex: 'col1', key: 'col1', width: 100 },
      { title: '列2', dataIndex: 'col2', key: 'col2', width: 100 }
    ],
    dataSource: [
      { key: '1', col1: '示例1', col2: '数据1' },
      { key: '2', col1: '示例2', col2: '数据2' }
    ],
    pagination: {
      show: true,
      pageSize: 10
    }
  };
};

// 更新配置
const updateOptions = (newOptions: ChartOptions) => {
  emit('update', newOptions);
};

// 更新config
const updateConfig = () => {
  // 触发响应式更新
  chartConfig.value = { ...chartConfig.value };
};
</script> 