import { defineStore } from "pinia";
import { onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import type { ConversationDbVO } from "@/types/app";
import { useConversationStore } from "./conversation";
import { getPathByMode, onClickCopy } from "@/utils";

export const useHistoryStore = defineStore("history", () => {
  const router = useRouter()
  const route = useRoute()
  const conversationId = route.params.conversationId as string

  const conversationStore = useConversationStore()
  const showHistoryDrawer = ref<boolean>(false);

  const filterConversation = () => {
    return conversationStore.conversationList;
  }

  onMounted(async () => {
    await conversationStore.getData()
    conversationStore.setActiveConversation(conversationStore.conversationList.find((item) => item.conv_uid === conversationId))
  })

  const onSelectConversation = (item: any) => {
    router.push(`/${getPathByMode(item.chat_mode)}/detail/${item.conv_uid}`)
    conversationStore.setActiveConversation(item)
    setHistoryDrawer(false);
  }

  const setHistoryDrawer = (val: boolean) => { showHistoryDrawer.value = val }

  const onCopyUrl = (conversationItem: ConversationDbVO) => {
    onClickCopy(`${location.href.split('chat')[0]}chatData/detail/${conversationItem.conv_uid}`);
  }

  return {
    showHistoryDrawer,
    setHistoryDrawer,
    filterConversation,
    onSelectConversation,
    onCopyUrl,
  }
})