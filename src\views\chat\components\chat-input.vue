<template>
  <div id="chat-input-container" :style="{ width: 'calc(100% - 200px)' }">
    <div class="chat-input-box">
      <div class="chat-input-field-container">
        <div class="chat-input-header" v-if="route.path !== '/chat'">
          <div class="left">
            <a-select
              :dropdownMatchSelectWidth="false"
              v-model:value="modelValue"
              style="width: 120px"
            >
              <a-select-option v-for="modelItem in modelList" :key="modelItem" :value="modelItem">
                <div class="model-select-option" style="display: flex; align-items: center;">
                  <img :src="getModelIcon(modelItem)" style="width: 20px; height: 20px; margin-right: 4px;" />
                  <div>{{ modelItem }}</div>
                </div>
              </a-select-option>
            </a-select>
            <a-select
              :dropdownMatchSelectWidth="false"
              v-model:value="paramsValue"
              style="width: 120px; margin-left: 10px;"
              v-if="route.path.startsWith('/chatDb') || route.path.startsWith('/chatData')"
              @change="handleParamsChange"
            >
              <a-select-option v-for="paramItem in paramsList" :key="paramItem.param" :value="paramItem.param">
                <div class="model-select-option" style="display: flex; align-items: center;">
                  <img :src="(dbMapper as any)[paramItem.type].icon" style="width: 20px; height: 20px; margin-right: 4px;" />
                  <div>{{ paramItem.param }}</div>
                </div>
              </a-select-option>
            </a-select>
            <a-cascader
              v-if="route.path.startsWith('/chatDb') || route.path.startsWith('/chatData')"
              v-model:value="topicValue" 
              :options="topicListCascader" 
              style="width: 120px; margin-left: 10px; text-align: left;"
              placeholder="请选择主题" 
            >
              <template #displayRender="{ labels, selectedOptions }">
                <span v-for="(label, index) in labels" :key="selectedOptions[index].value">
                  <span v-if="index === labels.length - 1">
                    {{ label  }}
                  </span>
                </span>
              </template>
            </a-cascader>
            <!-- <a-select
              v-if="route.path.startsWith('/chatDb') || route.path.startsWith('/chatData')"
              v-model:value="topicValue"
              style="width: 120px; margin-left: 10px; text-align: left;"
              placeholder="请选择主题"
              :options="topicList"
            ></a-select> -->
            <!-- <el-tooltip
              class="box-item"
              effect="dark"
              content="当前应用暂不支持拓展应用"
              placement="top"
            >
              <HourglassOutlined class="tool-icon" />
            </el-tooltip> -->
            <a-popover
              trigger="click"
              placement="top"
            >
              <template #content>
                <div style="display: flex; align-items: center;">
                  <div style="padding: 0 10px; width: 150px;">
                    <a-slider v-model:value="temperatureValue" :min="0" :max="1.0" :step="0.1" />
                  </div>
                  <a-input-number v-model:value="temperatureValue" :min="0" :max="1.0" :step="0.1" />
                </div>
              </template>
              <div class="tool-tip-custom">
                <div class="tool-tip-trigger" style="display: flex; align-items: center; cursor: pointer;">
                  <ProjectOutlined class="tool-icon" />
                  <div class="tool-label">{{ temperatureValue }}</div>
                </div>
                <div class="tool-tip-content">温度</div>
              </div>
            </a-popover>
            <a-popover
              trigger="click"
              placement="top"
            >
              <template #content>
                <div style="display: flex; align-items: center;">
                  <div style="padding: 0 10px; width: 150px;">
                    <a-slider v-model:value="maxTokenValue" :min="1" :max="20480" :step="1" />
                  </div>
                  <a-input-number v-model:value="maxTokenValue" :min="1" :max="20480" :step="1" />
                </div>
              </template>
              <div class="tool-tip-custom">
                <div class="tool-tip-trigger" style="display: flex; align-items: center; cursor: pointer;">
                  <ProjectOutlined class="tool-icon" />
                <div class="tool-label">{{ maxTokenValue }}</div>
                </div>
                <div class="tool-tip-content">最大输出token</div>
              </div>
            </a-popover>
          </div>
          <div class="right">
            <el-tooltip
              class="box-item"
              effect="dark"
              content="重新回答"
              placement="top"
            >
              <RedoOutlined class="tool-icon" @click="reAnswer" />
            </el-tooltip>
            <el-tooltip
              class="box-item"
              effect="dark"
              content="清除记忆"
              placement="top"
            >
              <ClearOutlined class="tool-icon" @click="clearMemoray" />
            </el-tooltip>
          </div>
        </div>
        <van-field
          v-model="getMessageStore().inputMessage"
          placeholder="你可以问我任何问题..."
          class="input-field"
          type="textarea"
          :rows="2"
          @keydown="onKeyDown"
        >
        </van-field>
        <div class="input-bottom">
          <div class="left-side">
            <!-- <NetworkBtn 
              v-if="appInfo?.enableWebSearch" 
              :checked="getMessageStore().enableWebSearch" 
              @change="getMessageStore().enableWebSearch = !getMessageStore().enableWebSearch"
            /> -->
          </div>
          <div class="right-side">
            <SendBtn 
              :loading="getMessageStore().loading" 
              :disabled="sendDisabled" 
              @submit="sendMessage" 
              @stop="getMessageStore().stopReceive"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { useMessageDynamicStore } from '@/store/message_dynamic';
import { useRoute, useRouter } from 'vue-router';
import type { ConversationDbVO, GetParamsVO, GetMetadataTopicListVO, Option } from '@/types/app';
import * as api from "@/api/chat";
import { NEW_CONVERSATION } from '@/constant';
import { useConversationStore } from '@/store/conversation';
// import NetworkBtn from '@/components/network-btn.vue';
import { ProjectOutlined, RedoOutlined, ClearOutlined } from '@ant-design/icons-vue';
import { sortTopicOption, getTopicFromCascader, topicCascadeDefaultOptions } from '../hooks/helper';
import { getModelIcon, getChatModeByRoute, getChatAppCodeByRoute, dbMapper } from '@/utils';
import { cloneDeep, debounce } from 'lodash-es';
import SendBtn from '@/components/send-btn.vue';
import { useUserStore } from '@/store/user';
import { ElMessage } from "element-plus";

const props = defineProps({
  isNew: {
    type: Boolean,
    default: false
  }
})

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();
const modelList = ref<string[]>([]);
const modelValue = ref<string>();
const temperatureValue = ref<number>(0.6);
const maxTokenValue = ref<number>(4000);
const paramsList = ref<GetParamsVO[]>([]);
const paramsValue = ref<string>();
const topicList = ref<Option[]>([]);
const topicListCascader = ref<Option[]>(cloneDeep(topicCascadeDefaultOptions));
const topicValue = ref<string | string[]>();
const dataSourceId = ref<number>();

const conversationStore = useConversationStore();
const getConversationId = () => route.params.conversationId as string;

const getMessageStore = (id?: string) => useMessageDynamicStore.getStore(id || getConversationId() || '');

const sendDisabled = computed(() => {
  if (route.path.startsWith('/chatDb')) {
    return getMessageStore().inputMessage.trim() === '' || !modelValue.value || !paramsValue.value || !topicValue.value; 
  }
  if (route.path.startsWith('/chatData')) {
    return getMessageStore().inputMessage.trim() === '' || !modelValue.value || !paramsValue.value;
  }
  return getMessageStore().inputMessage.trim() === '';
});

const onKeyDown = (event: KeyboardEvent) => {
  if (event.shiftKey && event.key === 'Enter') {
    event.preventDefault(); // 阻止默认的提交行为
    
    // 获取文本区域元素
    const textarea = event.target as HTMLTextAreaElement;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    
    // 在光标位置插入换行符
    const messageStore = getMessageStore();
    const currentValue = messageStore.inputMessage || '';
    messageStore.inputMessage = currentValue.substring(0, start) + '\n' + currentValue.substring(end);
    
    // 手动设置光标位置并滚动到可见区域
    setTimeout(() => {
      textarea.selectionStart = textarea.selectionEnd = start + 1;
      
      // 确保光标位置可见（自动滚动）
      textarea.blur();
      textarea.focus();
      
      // 如果上面的方法在某些浏览器不生效，可以尝试以下方法
      const lineHeight = parseInt(getComputedStyle(textarea).lineHeight) || 20;
      const currentLineCount = textarea.value.substr(0, start + 1).split('\n').length;
      const approximateScrollPosition = lineHeight * (currentLineCount - 1);
      textarea.scrollTop = approximateScrollPosition;
    }, 0);
  } else if (event.key === 'Enter' && !event.shiftKey) {
    if (sendDisabled.value) {
      return;
    }
    // 普通回车键按下时，阻止默认换行，触发发送
    event.preventDefault();
    sendMessage();
  }
};

const emits = defineEmits([
  'sendMessageSuccess',
  'computedScroll',
  'paramsIdChange',
  'loading',
]);

const reAnswer = () => {
  if (getMessageStore().messageList.length === 0) {
    return;
  }
  const message = getMessageStore().messageList[getMessageStore().messageList.length - 1];
  sendMessage(
    message.user_input, 
    message.app_code, 
    message.chat_mode, 
    message.model_name, 
    message.select_param ?? undefined,
    message.topic ?? undefined,
    message.topicList ?? undefined,
    message.datasource_id ?? undefined,
    message.max_new_tokens, 
    message.temperature,
  );
}

const clearMemoray = async () => {
  try {
    await api.clearMemory(getConversationId());
    getMessageStore().messageList = [];
  } catch (_err) {
    ElMessage.error('清除失败');
  }
}

const sendMessage = async (
  value?: string, 
  appCode?: string,
  chatCode?: string,
  modelName?: string,
  selectParams?: string,
  topic?: string | string[],
  topicListInput?: Option[],
  datasource?: string | number,
  maxTokens?: number,
  temperature?: number,
) => {
  // 如果没有消息列表，则说明是新对话
  const chatMode = getChatModeByRoute();
  const chatAppCode = getChatAppCodeByRoute();
  if (props.isNew) {
    // const appId = getAppInfoByRoute().id;
    emits('loading', true);
    const [conversationRes, appInfoRes] = await Promise.all([
      api.createConversationInfo(chatMode, { chat_mode: chatMode }),
      api.getAppInfo({ chat_scene: chatMode, app_code: chatAppCode })
    ]);

    conversationStore.addNewConversation({ 
      ...conversationRes.data, 
      user_input: getMessageStore().inputMessage.trim(),
      model_name: modelValue.value,
      select_param: paramsValue.value,
      topic: topicValue.value,
      datasource_id: dataSourceId.value, 
    } as ConversationDbVO);

    const data = {
      message: getMessageStore().inputMessage.trim(),
      enableWebSearch: getMessageStore().enableWebSearch,
      appInfo: appInfoRes.data,
      conversationInfo: conversationRes.data,
      modelName: modelValue.value,
      selectParams: paramsValue.value,
      topic: topicValue.value,
      topicList: topicList.value,
      datasource: dataSourceId.value,
    }

    // 新对话发送信息需要切换到对话详情页面显示，需要带入初始数据，存入缓存在切换页面时获取后删除
    localStorage.setItem(NEW_CONVERSATION, JSON.stringify(data));

    router.push(`${route.path === '/chatNormal' ? '/chat' : route.path}/detail/${(conversationRes.data as any).conv_uid}`)
    getMessageStore().inputMessage = ''
    return 
  }

  const topicMixValue = getTopicFromCascader(topic || topicValue.value || undefined);

  getMessageStore().sendMessage({
    conversationId: getConversationId(),
    value: value?.trim(),
    appCode: appCode || chatAppCode,
    chatCode: chatCode || chatMode,
    maxTokens: maxTokens || maxTokenValue.value || 4000,
    modelName: modelName || modelValue.value || modelList.value[0],
    temperature: temperature || temperatureValue.value || 0.6,
    selectParams: selectParams || paramsValue.value || paramsList.value[0]?.param || undefined,
    topic: Number(topicMixValue) === -2 ? '-1' : topicMixValue,
    topicList: topicListInput || topicList.value || [],
    datasource: datasource || dataSourceId.value,
    sendMessageSuccess: () => {
      emits('sendMessageSuccess');
    },
    computedScroll: () => {
      emits('computedScroll');
    }
  });
}

const handleGetModels = async (modelName?: string) => {
  let res = await api.getModels();
  modelList.value = res.data as string[];
  if (modelName) {
    modelValue.value = modelName;
    return;
  }
  if (modelList.value.length > 0) {
    modelValue.value = modelList.value[0];
  }
}

const handleGetParams = async (selectParam?: string) => {
  if (!(route.path.startsWith('/chatDb') || route.path.startsWith('/chatData'))) {
    paramsValue.value = undefined;
    return;
  }
  const chatMode = getChatModeByRoute();
  let res = await api.getParams({ chat_mode: chatMode });
  const paramsListTmp: GetParamsVO[] = [];
  (res.data as GetParamsVO[]).map((item) => {
    if (userStore.connectTopicPermissionMap.hasOwnProperty(item.id)) {
      paramsListTmp.push(item);
    }
  });
  if (paramsListTmp.length === 0) {
    topicListCascader.value = [];
  } else if (topicListCascader.value.length === 0) {
    topicListCascader.value = cloneDeep(topicCascadeDefaultOptions);
  }
  paramsList.value = paramsListTmp;
  console.log("paramsList", paramsList.value);
  if (paramsList.value.length > 0) {
    if (selectParam) {
      paramsList.value.map((item) => {
        if (item.param === selectParam) {
          paramsValue.value = item.param;
          dataSourceId.value = item.id;
          emits('paramsIdChange', item.id);
        }
      })
    } else {
      paramsValue.value = paramsList.value[0].param;
      dataSourceId.value = paramsList.value[0].id;
      emits('paramsIdChange', paramsList.value[0].id);
    }
  }
}

const handleParamsChange = (value: string) => {
  const paramItem = paramsList.value.find(item => item.param === value);
  if (paramItem) {
    emits('paramsIdChange', paramItem.id); // 触发事件传递 paramItem.id
  }
}

const handleGetThemes = async (topic?: string) => {
  if (!(route.path.startsWith('/chatDb') || route.path.startsWith('/chatData'))) {
  // if (!route.path.startsWith('/chatDb')) {
    topicValue.value = undefined;
    return;
  }
  if (!dataSourceId.value) {
    return;
  }
  const res = await api.getMetadataTopicList(dataSourceId.value || '');
  const themeListTmp: GetMetadataTopicListVO[] = [];
  (res.data as GetMetadataTopicListVO[]).map((item) => {
    if (userStore.connectTopicPermissionMap.hasOwnProperty(Number(item.connect_config_id))) {
      if (userStore.connectTopicPermissionMap[Number(item.connect_config_id)].hasOwnProperty(Number(item.id))) {
        themeListTmp.push(item);
      }
    }
  });
  const topicSortList = sortTopicOption(themeListTmp || []);
  topicList.value = [];
  topicListCascader.value[1].children = [];
  topicSortList.map((item: GetMetadataTopicListVO) => {
    topicListCascader.value[1].children?.push({
      value: item.id + '',
      label: item.topic_name,
    })
    topicList.value.push({
      value: item.id + '',
      label: item.topic_name,
    })
  })
  if (topicSortList.length > 0) {
    if (topic) {
      topicValue.value = topic;
      return;
    }
    topicValue.value = ['-2', topicSortList[0].id + ''];
  }
}

watch(
  () => [route.path, conversationStore.activeConversation],
  debounce(async () => {
    await nextTick();
    await handleGetModels(conversationStore.activeConversation?.model_name ?? undefined);
    await handleGetParams(conversationStore.activeConversation?.select_param ?? undefined);
    handleGetThemes(conversationStore.activeConversation?.topic ?? undefined);
  }, 300),
  { immediate: true, deep: true },
)

watch(
  () => paramsValue.value,
  debounce(async () => {
    let paramItem = paramsList.value.find(item => item.param === paramsValue.value);
    if (paramItem) {
      dataSourceId.value = paramItem.id;
    }
    if (dataSourceId.value) {
      handleGetThemes(conversationStore.activeConversation?.topic ?? undefined);
    }
  }, 300),
  { immediate: true, deep: true },
)

// 如果需要暴露方法给父组件，使用 defineExpose
defineExpose({
  sendMessage,
});
</script>
<style scoped lang="scss">
  :deep(.ant-select-item-option-content) {
    min-width: 200px !important;
  }
  :deep(.ant-select) {
    width: auto !important;
    min-width: 120px;
  }
  :deep(.select-drop-down-platform) { 
    width: 360px; 
  }

  .tool-tip-custom {
    position: relative;
    display: inline-block;
    overflow: visible; 
  }

  .tool-tip-content {
    visibility: hidden;
    width: max-content;
    max-width: 200px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 4px;
    padding: 8px 12px;
    position: absolute;
    bottom: 30px; /* 定位到上方 */
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  /* 添加三角箭头 */
  .tool-tip-content::after {
    content: "";
    position: absolute;
    top: 100%; 
    left: 50%;
    margin-left: -5px; 
    border-width: 5px; 
    border-style: solid;
    border-color: #333 transparent transparent transparent; 
  }

  .tool-tip-custom:hover .tool-tip-content {
    visibility: visible;
    opacity: 1;
  }
  /* 修复方案 */
  :deep(textarea) {
    /* 重置可能影响光标定位的属性 */
    text-align: left !important;
    direction: ltr !important;
    transform: none !important;
    
    /* 确保精确点击区域 */
    padding: 8px 12px;
    line-height: normal; /* 关键！避免行高影响 */
    box-sizing: border-box;
  }

  /* 防止父容器干扰 */
  :deep(.van-field__body) {
    position: static !important;
    overflow: visible !important;
  }

  /* 修复Chromium光标漂移 */
  @media (-webkit-device-pixel-ratio: 1.25) {
    :deep(.van-field__control) {
      padding-bottom: 2px; /* DPI补偿 */
    }
  }
</style>