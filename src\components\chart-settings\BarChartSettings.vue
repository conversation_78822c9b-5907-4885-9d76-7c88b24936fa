<template>
  <common-chart-settings
    :options="props.options"
    chart-type="bar"
    @update="updateOptions"
  >
    <!-- 样式配置 -->
    <template #style-settings>
        <!-- 坐标轴设置 -->
        <a-collapse-panel key="axis" header="坐标轴设置">
          <a-form-item label="X轴名称">
            <a-input v-model:value="chartConfig.xAxis.name" placeholder="请输入X轴名称" />
          </a-form-item>
          
          <a-form-item label="Y轴名称">
            <a-input v-model:value="chartConfig.yAxis.name" placeholder="请输入Y轴名称" />
          </a-form-item>
          
          <a-form-item label="X轴位置">
            <a-select v-model:value="chartConfig.xAxis.position">
              <a-select-option value="bottom">底部</a-select-option>
              <a-select-option value="top">顶部</a-select-option>
            </a-select>
          </a-form-item>
          
          <a-form-item label="显示网格线">
            <a-switch v-model:checked="chartConfig.grid.show" />
          </a-form-item>
        </a-collapse-panel>
        
        <!-- 柱状样式设置 -->
        <a-collapse-panel key="bar" header="柱状样式">
          <a-form-item label="柱子宽度">
            <a-input-number 
              v-model:value="barWidth" 
              :min="10" 
              :max="50"
              @change="updateBarWidth" 
            />
          </a-form-item>
          
          <a-form-item label="柱间距离">
            <a-input-number 
              v-model:value="barGap" 
              :min="0" 
              :max="1"
              :step="0.1"
              :formatter="(value: number) => `${value * 100}%`"
              :parser="(value: string) => parseFloat(value.replace('%', '')) / 100"
              @change="updateBarGap" 
            />
          </a-form-item>
        </a-collapse-panel>
        
        <!-- 数据标签设置 -->
        <a-collapse-panel key="label" header="数据标签">
          <a-form-item label="显示数据标签">
            <a-switch v-model:checked="chartConfig.series[0].label.show" />
          </a-form-item>
          
          <template v-if="chartConfig.series[0].label && chartConfig.series[0].label.show">
            <a-form-item label="标签位置">
              <a-select v-model:value="chartConfig.series[0].label.position">
                <a-select-option value="top">顶部</a-select-option>
                <a-select-option value="inside">内部</a-select-option>
                <a-select-option value="bottom">底部</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item label="字体大小">
              <a-input-number v-model:value="chartConfig.series[0].label.fontSize" :min="10" :max="20" />
            </a-form-item>
          </template>
        </a-collapse-panel>
    </template>
  </common-chart-settings>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import CommonChartSettings from './CommonChartSettings.vue';
import type { ChartOptions } from '../../types/chart';

const props = defineProps({
  options: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update']);

// 柱状图样式设置
const barWidth = ref(30);
const barGap = ref(0.3);

// 默认柱状图配置
const defaultBarConfig = {
  type: 'bar',
  title: { text: '', show: false },
  tooltip: {},
  legend: { show: true, orient: 'horizontal', left: 'right' },
  grid: { show: false, left: '3%', right: '4%', bottom: '3%', containLabel: true },
  xAxis: { name: '', position: 'bottom', data: [] },
  yAxis: { name: '', type: 'value' },
  series: [{
    name: '系列1',
    type: 'bar',
    data: [],
    barWidth: 30,
    barGap: 0.3,
    itemStyle: { color: '#1890ff' },
    label: { show: false, position: 'top', fontSize: 12 }
  }]
};

// 监听props变化
watch(() => props.options, (newOptions) => {
  if (newOptions) {
    // 同步样式控制变量
    if (newOptions.config?.series?.[0]) {
      barWidth.value = newOptions.config.series[0].barWidth || 30;
      barGap.value = newOptions.config.series[0].barGap || 0.3;
    }
  }
}, { deep: true, immediate: true });

// 直接访问config部分的计算属性
const chartConfig = computed({
  get: () => {
    return props.options.config || defaultBarConfig;
  },
  set: (newConfig) => {
    const updatedOptions = {
      ...props.options,
      config: newConfig
    };
    emit('update', updatedOptions);
  }
});

// 更新配置
const updateOptions = (newOptions: ChartOptions) => {
  emit('update', newOptions);
};

// 更新柱子宽度
const updateBarWidth = () => {
  const config = { ...chartConfig.value };
  config.series.forEach((series: any) => {
    series.barWidth = barWidth.value;
  });
  chartConfig.value = config;
};

// 更新柱间距离
const updateBarGap = () => {
  const config = { ...chartConfig.value };
  config.series.forEach((series: any) => {
    series.barGap = barGap.value;
  });
  chartConfig.value = config;
};
</script>

<style scoped>
.chart-settings {
  padding: 10px;
}
</style> 