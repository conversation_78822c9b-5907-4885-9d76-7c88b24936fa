import request from '../utils/request';
import type { ApiResponse } from '../types/api';
import type { LoginParams, LoginResult, UserInfo, UpdatePasswordDTO } from '../types/user';

/**
 * 用户登录
 */
export function login(data: any): Promise<LoginResult> {
  return request.post('/v2/serve/login', data);
}

/**
 * 获取用户信息
 */
export function getUserInfo(): Promise<UserInfo> {
  return request.get('/v2/serve/userinfo');
}

/**
 * 退出登录
 */
export function logout(): Promise<ApiResponse<boolean>> {
  return request.post('/v2/serve/logout');
} 

export function updatePassword(data: UpdatePasswordDTO): Promise<ApiResponse<boolean>> {
  return request.put('/v2/serve/datasources/updatePassword', data);
} 