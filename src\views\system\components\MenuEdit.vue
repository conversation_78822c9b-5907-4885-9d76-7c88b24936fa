<template>
  <a-modal 
    v-model:visible="showDialog"
    :title="`${props.menu?.id ? '编辑' : '创建'}菜单`" 
    @ok="handleOk" 
    @cancel="handleCancel" 
    cancelText="取消" 
    okText="确定">
    <div class="user-edit-container">
      <a-form
        ref="roleFormRef"
        :model="menuForm"
        name="basic"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
      >
        <a-form-item
          label="名称"
          name="name"
          :rules="[{ required: true, message: '请输入名称' }]"
        >
          <a-input v-model:value="menuForm.name" />
        </a-form-item>
        <a-form-item
          label="URL"
          name="url"
	        :rules="[{ required: true, message: '请输入描述' }]"
        >
          <a-input v-model:value="menuForm.url" />
        </a-form-item>
        <a-form-item
          label="排序"
          name="sort"
	        :rules="[{ required: true, message: '请输入排序' }]"
        >
          <a-input-number v-model:value="menuForm.sort" :step="1" />
        </a-form-item>
        <a-form-item
          label="父级菜单"
          name="status"
        >
          <a-tree-select
            v-model:value="menuForm.parent_id"
            show-search
            style="width: 100%"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            placeholder="请选择"
            allow-clear
            tree-default-expand-all
            :tree-data="menuOptionList"
            tree-node-filter-prop="label"
          >
            <template #title="{ value: val, label }">
              <div>{{ label }}</div>
            </template>
          </a-tree-select>
        </a-form-item>
	      <a-form-item
          label="状态"
          name="status"
        >
          <a-switch v-model:checked="menuForm.statusBoolean" />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, onMounted } from 'vue';
import type { PropType } from 'vue';
import { message } from 'ant-design-vue';
import type { TreeSelectProps } from 'ant-design-vue';
import type { GetMenuListDTO, SaveMenuDTO } from '@/types/system';
import { cloneDeep } from 'lodash-es';
import { getNowDate } from '@/utils';
import * as api from '@/api/system';

const props = defineProps({
  menu: {
    type: Object as PropType<SaveMenuDTO>,
    default: false
  }
});

const emit = defineEmits([
  'refreshData'
]);

const roleFormRef = ref();

const menuForm = reactive<SaveMenuDTO & { statusBoolean?: boolean }>({
  id: undefined,
  name: undefined,
  url: undefined,
  sort: 0,
  parent_id: 0,
  status: 'enabled',
  statusBoolean: true,
});

const showDialog = ref<boolean>(false);
const menuOptionList = ref<TreeSelectProps['treeData']>([]);

const resetRoleForm = () => {
  menuForm.id = undefined;
  menuForm.name = undefined;
  menuForm.url = undefined;
  menuForm.sort = 0;
  menuForm.parent_id = 0;
  menuForm.status = 'enabled';
  menuForm.statusBoolean = true;
}

const handleCancel = () => {
  showDialog.value = false;
  resetRoleForm();
}

const handleOk = async () => {
  try {
    // 执行表单校验
    await roleFormRef.value.validate();
  } catch (_error) {
    message.error('表单校验失败，请检查输入内容');
    return;
  }
  const formData  = cloneDeep(menuForm);
  formData.status = formData.statusBoolean ? 'enabled' : 'disabled';
  delete formData.statusBoolean;
  if (!props.menu?.id) {
    const date = getNowDate();
    formData.create_time = date;
    formData.update_time = date;
  }
  if (props.menu?.id) {
    await api.updateMenu(formData)
  } else {
    await api.createMenu(formData)
  }
  message.success(`${props.menu?.id ? '更新' : '添加'}成功`);
  emit('refreshData');
  showDialog.value = false;
  resetRoleForm();
}

const handleOpen = async (id?: number) => {
  showDialog.value = true;
  await nextTick(); // 等待 DOM 更新
  if (props.menu) {
    menuForm.id = props.menu.id;
    menuForm.name = props.menu.name;
    menuForm.url = props.menu.url;
    menuForm.sort = props.menu.sort;
    menuForm.parent_id = props.menu.parent_id;
    menuForm.status = props.menu.status; 
    menuForm.statusBoolean = props.menu.status === 'enabled';
  } else if (id) {
    menuForm.parent_id = id;
  }
}

const fetchMenuList = async () => {
  try {
    const formData: GetMenuListDTO = {}
    const res = await api.getMenuList(formData);
    menuOptionList.value = [
      {
        label: '根菜单',
        value: 0,
        children: [],
      }
    ];
    for (let i = 0; i < res.data.length; i++) {
      const item = res.data[i];
      if (!Array.isArray(menuOptionList.value[0].children)) {
        menuOptionList.value[0].children = [];
      }
      menuOptionList.value[0].children.push({
        label: item.name,
        value: item.id,
        // children: item.children.map((child: any) => ({
        //   label: child.name,
        //   value: child.id,
        // })),
      });
    }
  } catch (error: any) {
    console.error(error);
  }
};

defineExpose({
  handleOpen,
});

onMounted(() => {
  fetchMenuList();
});
</script>

<style scoped>

</style> 