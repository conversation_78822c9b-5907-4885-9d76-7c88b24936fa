import legendOptions from '../e-charts/legend';
import tooltipOptions from '../e-charts/tooltip';

const pieDefaultOptions = {
  dataType: 'static',
  data: {
    columns: ['名称', '数值'],
    values: [
      ['搜索引擎', 1048],
      ['直接访问', 735],
      ['邮件营销', 580],
      ['联盟广告', 484],
      ['视频广告', 300]
    ]
  },
  config: {
    type: 'pie',
    tooltip: tooltipOptions,
    legend: legendOptions,
    series: [
      {
        type: 'pie',
        radius: '50%',
        data: []
      }
    ]
  },
  dataMapping: {
    pie: {
      nameField: '名称',
      valueField: '数值'
    }
  }
};
export default pieDefaultOptions; 