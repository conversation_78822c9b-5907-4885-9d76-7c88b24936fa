<template>
  <div class="sql-container">
    <a-button
      class="copy-btn"
      type="text"
      @click="handleCopy"
    >
      <template #icon><CopyOutlined /></template>
    </a-button>
    <!-- 使用 pre 和 code 标签，通过 v-html 渲染高亮后的代码 -->
    <pre class="language-{{ language }} bg-black p-4 code-block">
      <code v-html="highlightedCode"></code>
    </pre>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { CSSProperties } from 'vue';
import { CopyOutlined } from '@ant-design/icons-vue';
import { onClickCopy } from '@/utils';
// 引入 prismjs 及其高亮功能
import Prism from 'prismjs';
// 引入 SQL 语言支持
import 'prismjs/components/prism-sql';
// 引入 Prism 暗色主题
import 'prismjs/themes/prism-dark.css';

// 定义组件属性
interface Props {
  code: string;
  language: string;
  customStyle?: CSSProperties;
  light?: { [key: string]: CSSProperties };
  dark?: { [key: string]: CSSProperties };
}

const props = defineProps<Props>();

const decodeHtml = (html: string) => {
  const txt = document.createElement('textarea');
  txt.innerHTML = html;
  return txt.value;
};

// 计算属性，用于获取高亮后的代码
const highlightedCode = computed(() => {
  let formattedCode = decodeHtml(props.code); // 先解码
  return Prism.highlight(formattedCode, Prism.languages[props.language] || Prism.languages.markup, props.language);
});

// 复制代码方法
const handleCopy = () => {
  onClickCopy(props.code);
};
</script>

<style scoped lang="scss">
.sql-container {
  position: relative;
  .code-block {
    white-space: pre-wrap;
    word-wrap: break-word;
    width: 100%;
    // 新增/覆盖暗色主题样式
    background: #181c24 !important; // 更深的暗色背景
    color: rgb(171, 178, 191) !important;      // 柔和的字体色
    border-radius: 8px;
    border: 1px solid #23272f;
    font-size: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  }
  code {
    display: block;
    text-align: left;
    font-family: 'Fira Mono', 'Consolas', 'Menlo', monospace;
    background: transparent !important;
    color: inherit !important;
  }
  code span {
    text-align: left;
  }
  .copy-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1;
    color: #FFFFFF;
  }
}
// 可选：进一步自定义 Prism token 颜色
:deep(.token.comment) { color: rgb(171, 178, 191) }
:deep(.token.keyword) { color: rgb(198, 120, 221) }
:deep(.token.string)  { color: #ce9178; }
:deep(.token.function){ color: #dcdcaa; }
</style>