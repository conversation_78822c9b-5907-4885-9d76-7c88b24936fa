<template>
  <common-chart-settings
    :options="props.options"
    chart-type="line"
    @update="updateOptions"
  >
    <!-- 样式配置 -->
    <template #style-settings>
      <!-- 坐标轴设置 -->
      <a-collapse-panel key="axis" header="坐标轴设置">
        <a-form-item label="X轴名称">
          <a-input v-model:value="chartConfig.xAxis.name" placeholder="请输入X轴名称" />
        </a-form-item>
        
        <a-form-item label="Y轴名称">
          <a-input v-model:value="chartConfig.yAxis.name" placeholder="请输入Y轴名称" />
        </a-form-item>
        
        <a-form-item label="显示网格线">
          <a-switch v-model:checked="chartConfig.grid.show" />
        </a-form-item>
      </a-collapse-panel>
      
      <!-- 线图样式设置 -->
      <a-collapse-panel key="line" header="线图样式">
        <a-form-item label="线条宽度">
          <a-input-number 
            v-model:value="lineWidth" 
            :min="1" 
            :max="10"
            @change="updateLineWidth" 
          />
        </a-form-item>
        
        <a-form-item label="线条类型">
          <a-select 
            v-model:value="lineType" 
            @change="updateLineType"
          >
            <a-select-option value="solid">实线</a-select-option>
            <a-select-option value="dashed">虚线</a-select-option>
            <a-select-option value="dotted">点线</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="平滑曲线">
          <a-switch 
            v-model:checked="smooth" 
            @change="updateSmooth" 
          />
        </a-form-item>
        
        <a-form-item label="显示数据点">
          <a-switch 
            v-model:checked="showSymbol" 
            @change="updateShowSymbol" 
          />
        </a-form-item>
        
        <a-form-item label="数据点大小" v-if="showSymbol">
          <a-input-number 
            v-model:value="symbolSize" 
            :min="2" 
            :max="20"
            @change="updateSymbolSize" 
          />
        </a-form-item>
      </a-collapse-panel>
    </template>
  </common-chart-settings>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue';
import CommonChartSettings from './CommonChartSettings.vue';
import type { ChartOptions } from '../../types/chart';

const props = defineProps({
  options: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update']);

// 线图样式设置
const lineWidth = ref(2);
const lineType = ref('solid');
const smooth = ref(false);
const showSymbol = ref(true);
const symbolSize = ref(6);

// 默认线图配置
const defaultLineConfig = {
  type: 'line',
  title: { text: '', show: false },
  tooltip: {},
  legend: { show: true, orient: 'horizontal', left: 'right' },
  grid: { show: false, left: '3%', right: '4%', bottom: '3%', containLabel: true },
  xAxis: { name: '', type: 'category', data: [] },
  yAxis: { name: '', type: 'value' },
  series: [{
    name: '系列1',
    type: 'line',
    data: [],
    smooth: false,
    showSymbol: true,
    symbolSize: 6,
    lineStyle: {
      color: '#1890ff',
      width: 2,
      type: 'solid'
    }
  }]
};

// 直接访问config部分的计算属性
const chartConfig = computed({
  get: () => {
    return props.options.config || defaultLineConfig;
  },
  set: (newConfig) => {
    const updatedOptions = {
      ...props.options,
      config: newConfig
    };
    emit('update', updatedOptions);
  }
});

// 更新配置
const updateOptions = (newOptions: ChartOptions) => {
  emit('update', newOptions);
};

// 监听props变化，更新本地控制变量
watch(() => props.options, (newOptions) => {
  if (newOptions?.config?.series?.[0]) {
    if (newOptions.config.series[0].lineStyle) {
      lineWidth.value = newOptions.config.series[0].lineStyle.width || 2;
      lineType.value = newOptions.config.series[0].lineStyle.type || 'solid';
    }
    smooth.value = newOptions.config.series[0].smooth || false;
    showSymbol.value = newOptions.config.series[0].showSymbol !== false;
    symbolSize.value = newOptions.config.series[0].symbolSize || 6;
  }
}, { deep: true });

// 更新线条宽度
const updateLineWidth = () => {
  const config = { ...chartConfig.value };
  config.series.forEach((series: any) => {
    if (!series.lineStyle) {
      series.lineStyle = { width: lineWidth.value, type: 'solid', color: '#1890ff' };
    } else {
      series.lineStyle.width = lineWidth.value;
    }
  });
  chartConfig.value = config;
};

// 更新线条类型
const updateLineType = () => {
  const config = { ...chartConfig.value };
  config.series.forEach((series: any) => {
    if (!series.lineStyle) {
      series.lineStyle = { width: 2, type: lineType.value, color: '#1890ff' };
    } else {
      series.lineStyle.type = lineType.value;
    }
  });
  chartConfig.value = config;
};

// 更新平滑曲线
const updateSmooth = () => {
  const config = { ...chartConfig.value };
  config.series.forEach((series: any) => {
    series.smooth = smooth.value;
  });
  chartConfig.value = config;
};

// 更新显示数据点
const updateShowSymbol = () => {
  const config = { ...chartConfig.value };
  config.series.forEach((series: any) => {
    series.showSymbol = showSymbol.value;
  });
  chartConfig.value = config;
};

// 更新数据点大小
const updateSymbolSize = () => {
  const config = { ...chartConfig.value };
  config.series.forEach((series: any) => {
    series.symbolSize = symbolSize.value;
  });
  chartConfig.value = config;
};

// 初始化组件
onMounted(() => {
  // 初始化样式控制变量
  if (chartConfig.value.series?.[0]) {
    if (chartConfig.value.series[0].lineStyle) {
      lineWidth.value = chartConfig.value.series[0].lineStyle.width || 2;
      lineType.value = chartConfig.value.series[0].lineStyle.type || 'solid';
    }
    smooth.value = chartConfig.value.series[0].smooth || false;
    showSymbol.value = chartConfig.value.series[0].showSymbol !== false;
    symbolSize.value = chartConfig.value.series[0].symbolSize || 6;
  }
});
</script>

<style scoped>
.chart-settings-container {
  padding: 16px;
}
</style> 