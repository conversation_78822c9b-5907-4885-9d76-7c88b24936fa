<template>
  <div class="database-list">
    <a-row :gutter="[16, 16]">
      <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6" v-for="db in databaseList" :key="db.id">
        <database-card 
          :database="db" 
          @edit="editDatabase"
          @delete="confirmDelete"
        />
      </a-col>
    </a-row>

    <!-- 删除确认对话框 -->
    <a-modal
      v-model:visible="deleteModalVisible"
      title="确认删除"
      @ok="handleDeleteOk"
      @cancel="handleDeleteCancel"
      okText="确认"
      cancelText="取消"
    >
      <p>确定要删除数据源 "{{ currentDatabase?.params?.database }}" 吗？</p>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import DatabaseCard from './DatabaseCard.vue';
import type { DatabaseItem } from '../../types/database';
import { getDataSources, deleteDataSource } from '../../api/database';

const props = defineProps<{
  refreshTrigger?: number; // 用于触发刷新的属性
}>();

const emit = defineEmits<{
  (e: 'refresh'): void;
  (e: 'edit', database: DatabaseItem): void;
}>();

// 数据库列表
const databaseList = ref<DatabaseItem[]>([]);
const currentDatabase = ref<DatabaseItem | null>(null);

// 删除确认对话框
const deleteModalVisible = ref(false);

// 获取数据库列表
const fetchDatabaseList = async () => {
  try {
    const response = await getDataSources();
    if (response.success) {
      databaseList.value = response.data;
      console.log('databaseList', databaseList.value);
    } else {
      message.error(response.err_msg || '获取数据源列表失败');
    }
  } catch (error) {
    console.error('获取数据源列表失败:', error);
    message.error('获取数据源列表失败');
  }
};

// 编辑数据源
const editDatabase = (db: DatabaseItem) => {
  emit('edit', db);
};

// 确认删除数据源
const confirmDelete = (db: DatabaseItem) => {
  currentDatabase.value = db;
  deleteModalVisible.value = true;
};

// 处理删除确认
const handleDeleteOk = async () => {
  if (!currentDatabase.value) return;
  
  try {
    // 调用删除API
    const response = await deleteDataSource(currentDatabase.value.id);
    if (response.success) {
      message.success('数据源删除成功');
      deleteModalVisible.value = false;
      currentDatabase.value = null;
      
      // 刷新列表
      await fetchDatabaseList();
      
      // 通知父组件刷新
      emit('refresh');
    } else {
      message.error(response.err_msg || '删除数据源失败');
    }
  } catch (error) {
    console.error('删除数据源失败:', error);
    message.error('删除数据源失败');
  }
};

// 取消删除
const handleDeleteCancel = () => {
  deleteModalVisible.value = false;
  currentDatabase.value = null;
};

onMounted(() => {
  fetchDatabaseList();
});

// 监听 refreshTrigger 属性变化来触发刷新
if (props.refreshTrigger !== undefined) {
  watch(() => props.refreshTrigger, () => {
    fetchDatabaseList();
  });
}
</script>

<style scoped>
.database-list {
  margin-top: 16px;
}
</style> 