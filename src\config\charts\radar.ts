import legendOptions from '../e-charts/legend';
import tooltipOptions from '../e-charts/tooltip';

const radarDefaultOptions = {
  dataType: 'static',
  data: {
    columns: ['指标', '预算分配', '实际开销'],
    values: [
      ['销售', 4300, 5000],
      ['管理', 10000, 14000],
      ['信息技术', 28000, 28000],
      ['客服', 35000, 31000],
      ['研发', 50000, 42000],
      ['市场', 19000, 21000]
    ]
  },
  config: {
    type: 'radar',
    tooltip: tooltipOptions,
    legend: legendOptions,
    radar: {
      indicator: []
    },
    series: [
      {
        type: 'radar',
        data: []
      }
    ]
  },
  dataMapping: {
    radar: {
      indicatorField: '指标',
      seriesFields: ['预算分配', '实际开销'],
      maxValueMapping: {
        '销售': 6500,
        '管理': 16000,
        '信息技术': 30000,
        '客服': 38000,
        '研发': 52000,
        '市场': 25000
      }
    }
  }
};
export default radarDefaultOptions; 