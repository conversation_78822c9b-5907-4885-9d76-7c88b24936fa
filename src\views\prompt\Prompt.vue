<template>
  <div class="prompt-manage-container">
    <div class="page-header">
      <h2 class="page-title">提示词管理</h2>
      <!-- <a-button type="primary" @click="handleCreateModal">
        <plus-outlined /> 创建提示词
      </a-button> -->
    </div>

    <div class="prompt-list">
      <a-table
        :columns="columns"
        :data-source="promptList"
        :loading="loading"
        :pagination="paginationConfig"
        rowKey="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'operation'">
            <!-- <a-space> -->
              <a @click="editPrompt(record)">编辑</a>
              <!-- <a-divider type="vertical" />
              <a-popconfirm
                title="确定要删除此提示词吗?"
                ok-text="确定"
                cancel-text="取消"
                @confirm="deletePrompt(record)"
              >
                <a class="delete-link">删除</a>
              </a-popconfirm>
            </a-space> -->
          </template>
        </template>
      </a-table>
    </div>

    <PromptEditDialog 
      ref="promptEditDialogRef" 
      :prompt="currPrompt" 
      @refreshData="fetchPromptList()" />
  </div>
</template>

<script setup lang="ts">
import { h, ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import type { PromptListItem, AddPromptDTO } from '@/types/prompt';
import PromptEditDialog from './PromptEditDialog.vue';
import { useRouter } from 'vue-router';
import * as api from '@/api/prompt';
import { LangMap } from './config';

const router = useRouter();

// 表格列定义
const columns = [
  {
    title: '名称',
    dataIndex: 'prompt_name',
    key: 'prompt_name',
  },
  {
    title: '场景',
    dataIndex: 'chat_scene',
    key: 'chat_scene',
  },
  {
    title: '语言',
    dataIndex: 'prompt_language',
    key: 'prompt_language',
    customRender: ({ record } : { record: any }) => {
      return LangMap[record.prompt_language as keyof typeof LangMap] || record.prompt_language;
    },
  },
  {
    title: '内容',
    dataIndex: 'content',
    key: 'content',
    customRender: ({ record } : { record: any }) => {
      return h('div', {
        style: {
          display: '-webkit-box',
          WebkitBoxOrient: 'vertical',
          WebkitLineClamp: 2,
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          wordBreak: 'break-all',
        }
      }, record.content);
    },
  },
  {
    title: '操作',
    key: 'operation',
    width: 100
  },
];

// 提示词列表和加载状态
const promptList = ref<PromptListItem[]>([]);
const currPrompt = ref<PromptListItem>();
const promptEditDialogRef = ref();
const loading = ref(false);

// 分页配置
const paginationConfig = reactive({
  pageSize: 6, // 每页显示数量
  current: 1, // 当前页码
  total: 0, // 总条数
  showTotal: (total: number) => `共 ${total} 条`, // 显示总条数
  showSizeChanger: true, // 显示每页数量选择器
  pageSizeOptions: ['6', '12', '18', '24'], // 每页数量选项
  locale: {
    items_per_page: ' 条/页',
  },
  onShowSizeChange: (_current: number, size: number) => {
    paginationConfig.pageSize = size;
    fetchPromptList();
  },
  onChange: (page: number, pageSize: number) => {
    paginationConfig.current = page;
    paginationConfig.pageSize = pageSize;
    fetchPromptList();
  }
});


// 获取提示词列表
const fetchPromptList = async () => {
  loading.value = true;
  try {
    const res = await api.getPromptList({ 
      page: paginationConfig.current, 
      page_size: paginationConfig.pageSize 
    });
    promptList.value = res.data.items; // 假设响应数据结构为 { data: [...] }
    paginationConfig.total = res.data.total_count; // 假设响应包含总条数
  } catch (error) {
    console.error('获取提示词列表失败', error);
    message.error('获取提示词列表失败');
  } finally {
    loading.value = false;
  }
};

const handleCreateModal = () => {
  // router.push('/promptAdd');
  currPrompt.value = undefined;
  promptEditDialogRef.value?.handleOpen();
};

// 编辑提示词
const editPrompt = (prompt: PromptListItem) => {
  // localStorage.setItem('editPrompt', JSON.stringify(prompt));
  // router.push('/promptEdit');
  currPrompt.value = prompt;
  promptEditDialogRef.value?.handleOpen();
};

// 删除提示词
const deletePrompt = async (data: AddPromptDTO) => {
  await api.deletePrompt(data);
  fetchPromptList();
  message.success('删除成功');
};

onMounted(() => {
  fetchPromptList();
});
</script>

<style scoped>
.prompt-manage-container {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.prompt-list {
  margin-top: 16px;
}

.delete-link {
  color: #ff4d4f;
}
</style> 