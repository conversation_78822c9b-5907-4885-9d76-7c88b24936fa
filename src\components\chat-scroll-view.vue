<template>
  <div class="chat-messages-title" :style="userStore.isExternal ? 'height: 100px; top: 0;' : ''">
    <!-- {{ title }} -->
    <div class="chat-title-container">
      <component :is="(getAppInfoByRoute() as any).icon" class="icon" />
      <div>
        <div class="title-container">
          <div class="title">{{ title }}</div>
          <!-- <a-tag color="green">native_app</a-tag>
          <a-tag color="cyan">{{ (getAppInfoByRoute() as any).mode }}</a-tag> -->
        </div>
        <div class="sub-title">{{ (getAppInfoByRoute() as any).subtitle }}</div>
      </div>
    </div>
  </div>
  <el-scrollbar 
    ref="scrollbarRef" 
    class="chat-messages-container" 
    :style="{ height: `calc(100vh - ${topBottomHeight}px - 64px - 24px)`, paddingTop: userStore.isExternal ? '20px' : '0' }"
    @scroll="onScroll"
  >
    <Spin :spinning="pageLoading" :style="pageLoading? 'margin-top: 160px;' : ''">
      <div class="chat-messages-content" ref="messageListRef" :style="pageLoading? 'padding-top: 0; height: 0;' : ''">
        <div class="chat-messages">
          <slot />
        </div>
      </div>
    </Spin>
  </el-scrollbar>
</template>

<script setup lang="ts">
import { onMounted, ref, computed } from 'vue';
import { getAppInfoByRoute } from '@/utils';
import { useUserStore } from '@/store/user';
import { Spin } from 'ant-design-vue';

defineProps({
  title: {
    type: String,
    default: ''
  },
  pageLoading: {
    type: Boolean,
    default: false,
  }
})

const scrollbarRef = ref();
const messageListRef = ref();
const userStore = useUserStore();
const chatInputHeight = ref(0);
const scrollHeight = ref(0);

const setInputContainerHeight = () => {
  setTimeout(() => {
    chatInputHeight.value = document.getElementById('chat-input-container')?.clientHeight || 0;
  })
}

const topBottomHeight = computed(() => {
  if (userStore.isExternal) {
    return chatInputHeight.value - 70;
  }
  return chatInputHeight.value;
})

const onScroll = (e: { scrollLeft: number, scrollTop: number }) => {
  scrollHeight.value = e.scrollTop
}

// 滚动到底部
const scrollToBottom = async () => {
  setTimeout(() => {
    scrollbarRef.value?.setScrollTop(10000000000)
  })
};

// 如果页面在底部，在文字更新过程中保持在底部
const computedScroll = async () => {
  const scrollViewHeight = scrollbarRef.value.wrapRef.clientHeight
  const messageListHeight = messageListRef.value.clientHeight
  
  if (10 > messageListHeight - scrollViewHeight - scrollHeight.value) {
    scrollToBottom()
  }
};

onMounted(() => {
  setInputContainerHeight();
  window.onresize = () => {
    setInputContainerHeight();
  }
})

defineExpose({
  scrollToBottom,
  computedScroll,
  setInputContainerHeight
})
</script>
