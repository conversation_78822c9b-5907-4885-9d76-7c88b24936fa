export interface ApiResponse<T = any> {
    code: number
    message: string
    data: T
}

// 登录请求参数
export interface LoginParams {
    username: string
    password: string
}

// 登录响应
export interface LoginResult {
    access_token: string;
    token_type: string;
}

export interface UserRoleItem {
    role_id: number | string;
    role_name: string;
}

export interface UserMenuItem {
    id: number | string;
    menu_id: number | string;
    menu_name: string;
    parent_id: number | string;
    role_id: number | string;
    role_name: string;
    children: UserMenuItem[];
}

export interface DatasourceRoleItem {
    connect_config_id: string | number;
    topic_id: string | number;
}

// 用户信息
export interface UserInfo {
    user_id: number | string;
    username: string;
    nickname: string;
    phone: string;
    email: string;
    status: string;
    roles: UserRoleItem[];
    menus: UserMenuItem[];
    datasource_role_permissions: DatasourceRoleItem[];
}

export interface UpdatePasswordDTO {
    user_id: number | string;
    old_password: string;
    new_password: string;
}