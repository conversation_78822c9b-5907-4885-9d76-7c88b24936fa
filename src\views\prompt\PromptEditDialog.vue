<template>
  <a-modal 
    v-model:visible="showDialog"
    :title="`${props.prompt?.id ? '编辑' : '创建'}提示词`" 
    @ok="handleOk" 
    @cancel="handleCancel" 
    :width="800"
    cancelText="取消" 
    okText="确定">
    <div class="prompt-edit-container">
      <a-form
        ref="promptFormRef"
        :model="promptForm"
        name="basic"
        :label-col="{ span: 2 }"
        :wrapper-col="{ span: 22 }"
        autocomplete="off"
      >
        <!-- <a-form-item
          label="语言"
          name="language"
          :rules="[{ required: true, message: '请选择语言' }]"
        >
          <a-select
            v-model:value="promptForm.language"
            placeholder="请选择语言"
            :options="languageOptions"
          ></a-select>
        </a-form-item> -->
        <a-form-item
          label="提示词"
          name="content"
          :rules="[{ required: true, message: '请输入提示词内容' }]"
        >
        <a-textarea 
          class="prompt-edit-input"
          :style="{
            height: `calc(100vh - 400px)`,
            minHeight: `calc(100vh - 400px)`,
          }"
          v-model:value="promptForm.content" 
          placeholder="请输入提示词内容" 
          :rows="4" />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue';
import type { PropType } from 'vue';
import { message } from 'ant-design-vue';
import type { PromptListItem, AddPromptDTO } from '@/types/prompt';
import { chatDataForm, languageOptions, template } from './config';
import * as api from '@/api/prompt';

const props = defineProps({
  prompt: {
    type: Object as PropType<PromptListItem>,
    default: false
  }
});

const emit = defineEmits([
  'refreshData'
]);

const showDialog = ref<boolean>(false);
const promptFormRef = ref();

interface PromptForm {
  content: string | undefined;
  language: string; 
}

const promptForm = reactive<PromptForm>({
  content: undefined,
  language: 'en',
});

const resetPromptForm = () => {
  promptForm.content = undefined;
  promptForm.language = 'en';
}

const handleCancel = () => {
  resetPromptForm();
  showDialog.value = false;
}

const handleOk = async () => {
  try {
    // 执行表单校验
    await promptFormRef.value.validate();
  } catch (_error) {
    message.error('表单校验失败，请检查输入内容');
    return;
  }
  const data: AddPromptDTO = {
    chat_scene: chatDataForm.chat_scene,
    content: promptForm.content || '',
    input_variables: chatDataForm.input_variables,
    model: chatDataForm.model,
    prompt_desc: chatDataForm.prompt_desc,
    prompt_language: promptForm.language,
    prompt_name: chatDataForm.prompt_name,
    prompt_type: chatDataForm.prompt_type,
    response_schema: chatDataForm.response_schema,
    sub_chat_scene: chatDataForm.sub_chat_scene,
    user_name: chatDataForm.user_name,
  };
  if (props.prompt) {
    data.prompt_code = props.prompt.prompt_code;
    await api.updatePrompt(data);
  } else {
    await api.createPrompt(data);
  }
  message.success(props.prompt ? '提示词编辑成功' : '提示词新增成功');
  emit('refreshData');
  resetPromptForm();
  showDialog.value = false;
}

const handleOpen = async () => {
  showDialog.value = true;
  await nextTick(); // 等待 DOM 更新
  if (props.prompt) {
    promptForm.content = props.prompt.content;
    promptForm.language = props.prompt.prompt_language;
  } else {
    promptForm.content = template;
  }
}

defineExpose({
  handleOpen,
});
</script>

<style scoped>

</style> 