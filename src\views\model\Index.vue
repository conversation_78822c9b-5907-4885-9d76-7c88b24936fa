<template>
  <div class="model-manage-container">
    <div class="page-header">
      <h2 class="page-title">模型管理</h2>
      <a-button type="primary" @click="showCreateModal">
        <plus-outlined /> 创建模型
      </a-button>
    </div>

    <!-- 模型列表组件 -->
    <model-list :refreshTrigger="refreshTrigger" @refresh="handleRefresh" />

    <!-- 创建模型表单组件 -->
    <model-form v-model:visible="modalVisible" @created="handleModelCreated" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import ModelList from './ModelList.vue';
import ModelForm from './ModelForm.vue';

// 模态框可见性控制
const modalVisible = ref(false);

// 刷新触发器
const refreshTrigger = ref(0);

// 显示创建模型对话框
const showCreateModal = () => {
  modalVisible.value = true;
};

// 处理模型创建成功
const handleModelCreated = () => {
  // 触发模型列表刷新
  refreshTrigger.value += 1;
};

// 处理模型列表刷新
const handleRefresh = () => {
  // 可以在这里添加其他刷新相关逻辑
};
</script>

<style scoped>
.model-manage-container {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}
</style> 