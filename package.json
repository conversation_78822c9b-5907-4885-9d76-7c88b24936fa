{"name": "dashbord", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "build": "vue-tsc -b && vite build --mode production", "build:test": "vue-tsc -b && vite build --mode test", "build:prod": "vue-tsc -b && vite build --mode production", "preview": "vite preview", "preview:test": "vite preview --mode test", "preview:prod": "vite preview --mode production"}, "dependencies": {"@ant-design/colors": "^7.2.1", "@antv/algorithm": "^0.1.26", "@antv/ava": "^3.5.0-alpha.4", "@antv/g2": "^5.1.8", "@antv/g6": "^5.0.17", "@antv/graphin": "^3.0.2", "@antv/s2": "^1.51.2", "@antv/util": "^3.3.10", "@microsoft/fetch-event-source": "^2.0.1", "@surely-vue/table": "^5.0.3", "@traptitech/markdown-it-katex": "^3.6.0", "@vicons/ionicons5": "^0.13.0", "ant-design-vue": "^3.2.20", "axios": "^1.9.0", "echarts": "^5.6.0", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.9.9", "highlight.js": "^11.11.1", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "pinia": "^3.0.2", "prismjs": "^1.30.0", "sass-embedded": "^1.87.0", "sql-formatter": "^15.6.1", "uuid": "^11.1.0", "vant": "^4.8.0", "vue": "^3.5.13", "vue-echarts": "^6.7.3", "vue-prism-component": "^2.0.0", "vue-router": "^4.5.1", "vue3-grid-layout": "^1.0.0", "vue3-json-viewer": "^2.3.0"}, "devDependencies": {"@eslint/js": "^9.28.0", "@types/markdown-it": "^14.1.2", "@types/prismjs": "^1.26.5", "@vitejs/plugin-vue": "^5.2.2", "@vue/tsconfig": "^0.7.0", "eslint": "^9.28.0", "eslint-plugin-vue": "^10.2.0", "globals": "^16.2.0", "mockjs": "^1.1.0", "typescript": "~5.7.2", "typescript-eslint": "^8.34.0", "vite": "^6.3.1", "vite-plugin-mock": "^3.0.2", "vue-tsc": "^2.2.8"}}