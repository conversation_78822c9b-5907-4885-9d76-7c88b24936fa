import request from '../utils/request';
import type { ApiResponse } from '../types/api';
import type {
    asyncMetaDataItem, datasourcesItem,
    datasourcesResponse,
    DeleteMetadataParams, deleteTopicRequest,
    MetadataRequest,
    metadataResponse,
    PostMetadataParams,
    topicItem,
    UpdateMetadataParams,
    AddColumnSexegesisDTO,
    GetMetadataRoleListDTO,
    AddMetadataRoleDTO,
    DeleteMetadataRoleDTO,
    GetForeignKeyVO,
} from '../types/metaData.ts'
import type {DatabaseItem} from "@/types/database.ts";

export function getDatasourcesList(datasourceId: number | string): Promise<ApiResponse<datasourcesItem>> {
    return request.get(`/v2/serve/datasources/${datasourceId}/getDatasourcesList`);
}

// 元数据列表
export const getMetadataList = (data: MetadataRequest): Promise<datasourcesResponse> => {
    return request.post(`/v2/serve/datasources/getMetadataList`, data);
};


// 元数据新增
export const addMetadata = (data: PostMetadataParams): Promise<ApiResponse<any>> => {
    return request.post(`/v2/serve/datasources/addMetadata`, data);
};

// 元数据修改
export const updateMetadata = (data: UpdateMetadataParams): Promise<ApiResponse<any>> => {
    return request.put(`/v2/serve/datasources/updateMetadata`, data);
};

// 元数据表名修改
export const updateTableMetadata = (data: UpdateMetadataParams): Promise<ApiResponse<any>> => {
    return request.put(`/v2/serve/datasources/updateTableMetadata`, data);
};

// 详情
export const getMetadata = (id: number): Promise<metadataResponse> => {
    return request.get(`/v2/serve/datasources/getMetadata/${id}`);
};

// 删除
export const deleteMetadata = (data: DeleteMetadataParams): Promise<metadataResponse> => {
    return request.post(`/v2/serve/datasources/deleteMetadata`, data);
};

// 获取主题列表
export const getTopicList = (datasourceId: number | string): Promise<ApiResponse<topicItem[]>> => {
    return request.get(`/v2/serve/datasources/getTopicList/${datasourceId}`);
};

// 主题新增
export const addTopic = (data: topicItem): Promise<ApiResponse<topicItem>> => {
    return request.post(`/v2/serve/datasources/addTopic`, data);
};

// 主题修改
export const updateTopic = (data: topicItem): Promise<ApiResponse<any>> => {
    return request.put(`/v2/serve/datasources/updateTopic`, data);
};

// 主题删除
export const deleteTopic = (data: deleteTopicRequest): Promise<ApiResponse<any>> => {
    return request.post(`/v2/serve/datasources/deleteTopic`, data);
};

// 主题新增
export const initMetadataToVector = (datasource_id: number | string, topic: number | string): Promise<ApiResponse<any>> => {
    return request.post(`/v2/serve/datasources/${datasource_id}/initMetadataToVector/${topic}`, {}, { timeout: 300000 });
};

// 默认主题
export const getDefaultMetadataList = (datasource_id: number | string): Promise<ApiResponse<any>> => {
    return request.get(`/v2/serve/datasources/getDefaultMetadataList/${datasource_id}`, {}, { timeout: 300000 });
};

// 大模型补充注释
export const addColumnSexegesis = (data: AddColumnSexegesisDTO): Promise<ApiResponse<any>> => {
    return request.post(`/v2/serve/datasources/addColumnSexegesis`, data, { timeout: 300000 });
};

// 获取主题角色列表
export const getMetadataRoleList = (data: GetMetadataRoleListDTO): Promise<ApiResponse<any>> => {
    return request.post(`/v2/serve/datasources/getMetadataRoleList`, data);
};

// 主题添加角色
export const addMetadataRole = (data: AddMetadataRoleDTO): Promise<ApiResponse<any>> => {
    return request.post(`/v2/serve/datasources/addMetadataRole`, data);
};

// 主题添加角色
export const deleteMetadataRole = (data: DeleteMetadataRoleDTO): Promise<ApiResponse<any>> => {
    return request.post(`/v2/serve/datasources/deleteMetadataRole`, data);
};

// 外键选项列表
export const getForeignKey = (datasource_id: number | string): Promise<ApiResponse<GetForeignKeyVO[]>> => {
    return request.get(`/v2/serve/datasources/getForeignKey/${datasource_id}`);
};