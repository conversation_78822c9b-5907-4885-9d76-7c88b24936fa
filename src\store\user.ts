import { ref } from "vue";
import { defineStore } from "pinia";
import { LOGIN_ACCESS_TOKEN } from '@/constant';
import { dynamicRouteMap } from '@/router/config';
import type { RouteRecordRaw } from 'vue-router';
import type { UserInfo, DatasourceRoleItem } from '@/types/user';
import { message } from 'ant-design-vue';
import * as api from '@/api/user';
import router from '@/router'; 

export const useUserStore = defineStore("user", () => {

  const userInfo = ref<UserInfo>();
  const permissionList = ref<string[]>([]);
  const permissionMap = ref<Record<string, boolean>>({})
  const dynamicRoutePathMap = ref<Record<string, boolean>>({});
  const connectTopicPermissionMap = ref<Record<number, Record<number, boolean>>>({});
  const redirectPath = ref<string>('');
  const isExternal = ref<boolean>(false);
  const isFirstLogin = ref<boolean>(false);

  const login = async (username: string, password: string) => {
    const formData = new FormData();
    formData.append('username', username);
    formData.append('password', password);
    let res = await api.login(formData);
    console.log(res)
    isFirstLogin.value = true;
    localStorage.setItem(LOGIN_ACCESS_TOKEN, res?.access_token || '');
    router.push('/');
  }

  const logout = async () => {
    await api.logout();
    localStorage.removeItem(LOGIN_ACCESS_TOKEN);
    location.href = '/login';
    message.success('已成功退出登录');
  }
  
  const getUserInfo = async (): Promise<UserInfo> => {
    if (!userInfo.value) {
      let res = await api.getUserInfo();
      userInfo.value = res;
      genPermissionList();
      genConnectTopicPermission();
    }
    return userInfo.value;
  }

  const genPermissionList = () => {
    if (!userInfo.value) return;
    let { menus } = userInfo.value;
    menus.forEach((item) => {
      if (!permissionMap.value.hasOwnProperty(item.menu_name)) {
        permissionMap.value[item.menu_name] = true;
        permissionList.value.push(item.menu_name);
        if (dynamicRouteMap.hasOwnProperty(item.menu_name)) {
          const routList = dynamicRouteMap[item.menu_name];
          routList.forEach((route: RouteRecordRaw) => {
            router.addRoute('DefaultLayout', route);
            dynamicRoutePathMap.value[`/${route.path}`] = true;
          })
        }
        if (item.children) {
          item.children.forEach((child) => {
            if (!permissionMap.value.hasOwnProperty(child.menu_name)) {
              permissionMap.value[child.menu_name] = true;
              permissionList.value.push(child.menu_name);
            }
          }) 
        }
      } else if (item.children) {
        item.children.forEach((child) => {
          if (!permissionMap.value.hasOwnProperty(child.menu_name)) {
            permissionMap.value[child.menu_name] = true;
            permissionList.value.push(child.menu_name);
          }
        }) 
      }
    })
    if (!permissionMap.value.hasOwnProperty('在线对话')) {
      router.removeRoute('/');
      if (permissionList.value.length > 0) {
        router.addRoute({
          path: '/',
          name: '/',
          redirect: genRedirectRoute() as RouteRecordRaw,
        });
      } else {
        router.addRoute({
          path: '/',
          name: '/',
          redirect: '/',
        });
      }
    }
  }

  const genConnectTopicPermission = () => {
    if (!userInfo.value) return;
    let { datasource_role_permissions } = userInfo.value;
    datasource_role_permissions.map((item: DatasourceRoleItem) => {
      if (!connectTopicPermissionMap.value.hasOwnProperty(Number(item.connect_config_id))) {
        connectTopicPermissionMap.value[Number(item.connect_config_id)] = {};
      }
      connectTopicPermissionMap.value[Number(item.connect_config_id)][Number(item.topic_id)] = true;
    })
  }

  const genRedirectRoute = () => {
    for (let i in dynamicRouteMap) {
      if (permissionMap.value.hasOwnProperty(i)) {
        redirectPath.value = `/${dynamicRouteMap[i][0].path}`;
        return dynamicRouteMap[i][0];
      }
    }
  }

  const setExternal = (external: boolean) => {
    isExternal.value = external;
  }

  const setFirstLogin = (firstLogin: boolean) => {
    isFirstLogin.value = firstLogin;
  }

  return {
    login,
    logout,
    userInfo,
    isExternal,
    setExternal,
    getUserInfo,
    redirectPath,
    isFirstLogin,
    setFirstLogin,
    permissionMap,
    permissionList,
    dynamicRoutePathMap,
    connectTopicPermissionMap,
  }
})