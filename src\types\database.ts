/**
 * 数据库类型枚举
 */
export enum DatabaseType {
  MySQL = 'mysql',
  PostgreSQL = 'postgresql',
  SQLServer = 'sqlserver',
  Oracle = 'oracle',
  MongoDB = 'mongodb',
  Redis = 'redis',
  SQLite = 'sqlite'
}

/**
 * API返回的数据库项目接口
 */
export interface DatabaseApiResponse {
  success: boolean;
  err_code: string | null;
  err_msg: string | null;
  data: DatabaseItem[];
}

/**
 * 数据库项目接口
 */
export interface DatabaseItem {
  id: number;
  type: string;
  params: Record<string, any>;
  description: string;
  gmt_created: string;
  gmt_modified: string;
}

/**
 * 数据库表单接口
 */
export interface DatabaseForm {
  name: string;
  type: DatabaseType | string;
  params: Record<string, any>;
  description?: string;
}

// 数据库提供商类型
export interface DatabaseProviderOption {
  id: string;
  name: string;
  icon?: string;
  description?: string;
}

/**
 * 数据库类型参数
 */
export interface DatabaseTypeParam {
  required: boolean;
  is_array: boolean;
  param_name: string;
  param_class: string;
  param_type: string;
  label: string;
  description: string;
  default_value: any;
  valid_values: any[] | null;
  ext_metadata: Record<string, any> | null;
  nested_fields: any[] | null;
  param_order: number;
}

/**
 * 数据库类型
 */
export interface DatabaseTypeItem {
  name: string;
  label: string;
  description: string;
  parameters: DatabaseTypeParam[];
} 