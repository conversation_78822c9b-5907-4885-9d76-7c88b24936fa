<template>
  <a-modal 
    v-model:visible="showDialog"
    :title="`${props.role?.id ? '编辑' : '创建'}角色`" 
    @ok="handleOk" 
    @cancel="handleCancel" 
    :width="800"
    cancelText="取消" 
    okText="确定"
    :confirm-loading="isSubmit"
    :cancel-button-props="{ disabled: isSubmit }"
  >
    <div class="user-edit-container">
      <a-form
        ref="roleFormRef"
        :model="roleForm"
        name="basic"
        :label-col="{ span: 2 }"
        :wrapper-col="{ span: 22 }"
        autocomplete="off"
        :key="formKey"
      >
        <a-form-item
          label="名称"
          name="name"
          :rules="[{ required: true, message: '请输入名称' }]"
        >
          <a-input v-model:value="roleForm.name" maxlength="50" />
        </a-form-item>
        <a-form-item
          label="描述"
          name="description"
	        :rules="[{ required: true, message: '请输入描述' }]"
        >
          <a-input v-model:value="roleForm.description" type="textarea" maxlength="100" />
        </a-form-item>
        <a-form-item
          :label="roleForm.statusBoolean? '正常' : '停用'"
          name="status"
        >
          <a-switch v-model:checked="roleForm.statusBoolean" />
        </a-form-item>
      </a-form>
      <RoleMenu :id="(props.role?.id as any)" ref="roleMenuRef" />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick } from 'vue';
import type { PropType } from 'vue';
import { message } from 'ant-design-vue';
import type { SaveRoleDTO, RoleMenuItem } from '@/types/system';
import RoleMenu from './RoleMenu.vue';
import { cloneDeep } from 'lodash-es';
import { getNowDate } from '@/utils';
import * as api from '@/api/system';

const props = defineProps({
  role: {
    type: Object as PropType<SaveRoleDTO>,
    default: false
  }
});

const emit = defineEmits([
  'refreshData'
]);

const roleFormRef = ref();
const roleMenuRef = ref();

const roleForm = reactive<SaveRoleDTO & { statusBoolean?: boolean, menus?: (string | number)[] }>({
  id: undefined,
  name: undefined,
  description: undefined,
  status: 'enabled',
  statusBoolean: true,
});

const showDialog = ref<boolean>(false);
const isSubmit = ref<boolean>(false);
const formKey = ref<number>(0);


const resetRoleForm = () => {
  roleForm.id = undefined;
  roleForm.name = undefined;
  roleForm.description = undefined;
  roleForm.status = 'enabled';
  roleForm.statusBoolean = true;
}

const handleCancel = () => {
  roleMenuRef.value?.resetMenuList();
  showDialog.value = false;
  resetRoleForm();
}

const handleOk = async () => {
  if (isSubmit.value) {
    return;
  }
  try {
    // 执行表单校验
    await roleFormRef.value.validate();
  } catch (_error) {
    message.error('表单校验失败，请检查输入内容');
    return;
  }
  try {
    isSubmit.value = true;
    const formData  = cloneDeep(roleForm);
    formData.status = formData.statusBoolean ? 'enabled' : 'disabled';
    delete formData.statusBoolean;
    if (!props.role?.id) {
      const date = getNowDate();
      formData.create_time = date;
      formData.update_time = date;
      formData.menus = [];
      roleMenuRef.value?.menuList.map((item: RoleMenuItem) => {
        (formData.menus ??= []).push(item.id);
        if (item.children?.length) {
          item.children.map((child: RoleMenuItem) => {
            (formData.menus??= []).push(child.id);
          })
        }
      })
    }
    if (props.role?.id) {
      await api.updateRole(formData)
    } else {
      await api.createRole(formData)
    }
    message.success(`${props.role?.id ? '更新' : '添加'}成功`);
    roleMenuRef.value?.resetMenuList();
    emit('refreshData');
    showDialog.value = false;
    resetRoleForm();
  } finally {
    isSubmit.value = false;
  }
}

const handleOpen = async () => {
  showDialog.value = true;
  formKey.value ++; // 触发表单重新渲染
  await nextTick(); // 等待 DOM 更新
  if (props.role) {
    roleForm.id = props.role.id;
    roleForm.name = props.role.name;
    roleForm.description = props.role.description;
    roleForm.status = props.role.status; 
    roleForm.statusBoolean = props.role.status === 'enabled';
    roleMenuRef.value?.fetchMenuList();
  }
}

defineExpose({
  handleOpen,
});
</script>

<style scoped>

</style> 