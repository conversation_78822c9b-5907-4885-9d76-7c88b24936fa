import tooltipOptions from '../e-charts/tooltip';
import legendOptions from '../e-charts/legend';

const horizontalBarDefaultOptions = {
  dataType: 'static',
  data: {
    columns: ['类别', '数量'],
    values: [
      ['衬衫', 20],
      ['羊毛衫', 35],
      ['雪纺衫', 15],
      ['裤子', 40],
      ['高跟鞋', 25],
      ['袜子', 30]
    ]
  },
  config: {
    type: 'horizontal-bar',
    tooltip: tooltipOptions,
    legend: legendOptions,
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: []
    },
    series: [
      {
        type: 'bar',
        data: []
      }
    ]
  },
  dataMapping: {
    xField: '数量',
    yField: '类别',
    seriesNames: ['数量']
  }
};
export default horizontalBarDefaultOptions; 