import { createApp } from 'vue'
import './style.css'
import './assets/grid-layout.css'
import App from './App.vue'
import env from './utils/env'  // 导入环境变量工具

// 输出mock配置状态
const isMockEnabled = import.meta.env.VITE_ENABLE_MOCK !== 'false';
const mockPaths = import.meta.env.VITE_MOCK_PATHS || '';
if (isMockEnabled) {
  console.log(`🔧 Mock已启用，以下路径将使用mock数据: ${mockPaths || '无'}`);
} else {
  console.log('🔧 Mock已禁用，所有请求将使用真实API');
}

// 设置页面标题
document.title = env.appTitle

// 引入Ant Design Vue
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/antd.css'

// 引入Vue Router
import router from './router'

// 引入ECharts核心库
import * as echarts from 'echarts/core'
// 引入ECharts组件
import { 
  <PERSON><PERSON>hart, 
  <PERSON><PERSON>hart, 
  <PERSON>hart, 
  <PERSON>atter<PERSON>hart, 
  Radar<PERSON>hart, 
  FunnelChart, 
  Gauge<PERSON>hart, 
  TreemapChart,
  HeatmapChart 
} from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  LegendComponent,
  VisualMapComponent,
  ToolboxComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

// 引入词云图扩展
import 'echarts-wordcloud'

// 注册必要的ECharts组件
echarts.use([
  BarChart,
  LineChart,
  PieChart,
  ScatterChart,
  RadarChart,
  FunnelChart,
  GaugeChart,
  TreemapChart,
  HeatmapChart,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  LegendComponent,
  VisualMapComponent,
  ToolboxComponent,
  CanvasRenderer
])

// 引入vue-echarts
import VChart from 'vue-echarts'

// 引入GridLayout插件
import GridLayoutPlugin from './plugins/gridLayout'
import store from './store'
import Vant from 'vant'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import './assets/index.scss'
import "vant/lib/index.css"

// 创建应用实例
const app = createApp(App)

// 注册全局组件
app.component('VChart', VChart)

// 使用插件
app.use(GridLayoutPlugin)
app.use(Antd)
app.use(router)
app.use(store)
app.use(ElementPlus)
app.use(Vant)

// 全局挂载ECharts，方便在组件中使用
app.config.globalProperties.$echarts = echarts

app.mount('#app')
