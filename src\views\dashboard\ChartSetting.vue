<template>
  <a-drawer
    :visible="visible"
    placement="right"
    :title="title"
    :width="600"
    @close="onClose"
  >
    <template v-if="isLoading">
      <div class="loading-container">
        <a-spin tip="加载中..."></a-spin>
      </div>
    </template>
    <template v-else-if="error">
      <a-alert
        :message="error"
        type="error"
        show-icon
      />
    </template>
    <template v-else>
      <component
        :key="chatKey"
        :is="settingComponent"
        :chart-type="chartType"
        :chartId="props.chartId"
        :options="options" 
        @update="updateChartOptions"
      />
    </template>
    
    <template #footer>
      <div style="text-align: right">
        <a-button style="margin-right: 8px" @click="onClose" :disabled="isSaving">取消</a-button>
        <a-button type="primary" @click="saveSettings" :disabled="!!error" :loading="isSaving">保存</a-button>
      </div>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, computed, markRaw, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { useDashboardStore } from '../../store/dashboard';

// 导入已创建的图表设置组件
import BarChartSettings from '../../components/chart-settings/BarChartSettings.vue';
import LineChartSettings from '../../components/chart-settings/LineChartSettings.vue';
import PieChartSettings from '../../components/chart-settings/PieChartSettings.vue';
import ScatterChartSettings from '../../components/chart-settings/ScatterChartSettings.vue';
import AreaChartSettings from '../../components/chart-settings/AreaChartSettings.vue';
import RadarChartSettings from '../../components/chart-settings/RadarChartSettings.vue';
import FunnelChartSettings from '../../components/chart-settings/FunnelChartSettings.vue';
import HeatmapChartSettings from '../../components/chart-settings/HeatmapChartSettings.vue';
import GaugeChartSettings from '../../components/chart-settings/GaugeChartSettings.vue';
import TreemapChartSettings from '../../components/chart-settings/TreemapChartSettings.vue';
import HorizontalBarChartSettings from '../../components/chart-settings/HorizontalBarChartSettings.vue';
import WordCloudChartSettings from '../../components/chart-settings/WordCloudChartSettings.vue';
import MetricChartSettings from '../../components/chart-settings/MetricChartSettings.vue';
import TableChartSettings from '../../components/chart-settings/TableChartSettings.vue';
// 导入默认设置组件，用于尚未实现的图表类型
import DefaultChartSettings from '../../components/chart-settings/DefaultChartSettings.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  chartType: {
    type: String,
    required: true
  },
  chartId: {
    type: Number,
    required: true
  },
  chartTitle: {
    type: String,
    default: ''
  },
  dashboardId: {
    type: Number,
    required: true
  }
});

const emit = defineEmits(['close', 'save']);

// 使用Pinia store
const dashboardStore = useDashboardStore(props.dashboardId);
console.log('props.dashboardId', props.dashboardId);

// 加载状态和错误状态
const isLoading = ref(false);
const error = ref('');
const isSaving = ref<boolean>(false);
const options = ref<any>(dashboardStore.getChartOptions(props.chartId));
const chatKey = ref<number>(0);

// 本地缓存的图表配置
const localChartOptions = ref<any>(null);

// 标题
const title = computed(() => {
  return `图表设置 - ${props.chartTitle || props.chartId}`;
});

// 设置组件
const settingComponent = ref<any>(null);

// 初始化
onMounted(() => {
  loadSettingComponent();
  loadChartOptions();
});

// 监听可见性变化
watch(() => props.visible, (newVal) => {
  dashboardStore.updateChartFromRef(props.chartId);
  if (newVal) {
    chatKey.value ++;
    loadSettingComponent();
    loadChartOptions();
  } else {
    dashboardStore.refreshSet ++;
    dashboardStore.sqlInput = '';
  }
});

// 加载图表配置
const loadChartOptions = () => {
  isLoading.value = true;
  error.value = '';
  try {
    options.value = dashboardStore.getChartOptions(props.chartId);
    console.log('loadChartOptions', options.value);
    if (!options.value) {
      error.value = '图表配置不存在';
    }
    dashboardStore.sqlInput = options.value?.sql || '';
  } catch (err) {
    console.error('加载图表配置出错:', err);
    error.value = `加载图表配置出错: ${(err as Error).message || '未知错误'}`;
  } finally {
    isLoading.value = false;
  }
};

// 加载设置组件
const loadSettingComponent = () => {
  try {
    error.value = '';
    isLoading.value = true;
    
    const componentMap = {
      'bar': BarChartSettings,
      'line': LineChartSettings,
      'pie': PieChartSettings,
      'scatter': ScatterChartSettings,
      'area': AreaChartSettings,
      'radar': RadarChartSettings,
      'funnel': FunnelChartSettings,
      'heatmap': HeatmapChartSettings,
      'treemap': TreemapChartSettings,
      'horizontal-bar': HorizontalBarChartSettings,
      'word-cloud': WordCloudChartSettings,
      'gauge': GaugeChartSettings,
      'metric': MetricChartSettings,
      'table': TableChartSettings
    };
    
    // 如果找不到对应的组件，返回默认组件并在控制台输出警告
    const component = componentMap[props.chartType as keyof typeof componentMap];
    if (!component) {
      console.warn(`未找到图表类型 ${props.chartType} 对应的设置组件，使用默认组件`);
      settingComponent.value = markRaw(DefaultChartSettings);
    } else {
      settingComponent.value = markRaw(component);
    }
  } catch (err) {
    console.error('加载图表设置组件出错:', err);
    error.value = `加载图表设置组件出错: ${(err as Error).message || '未知错误'}`;
  } finally {
    isLoading.value = false;
  }
};

// 更新图表配置
const updateChartOptions = (newOptions: any) => {
  try {
    console.log('updateChartOptions', newOptions);
    // 直接更新store中的选项，而不是本地缓存
    dashboardStore.updateChartOptions(props.chartId, newOptions);
  } catch (err) {
    console.error('更新图表配置出错:', err);
    message.error('更新图表配置出错');
  }
};

// 关闭抽屉
const onClose = () => {
  emit('close');
};

// 保存设置
const saveSettings = async () => {
  if (error.value) return;

  try {
    // 直接从store获取最新的options
    isSaving.value = true;
    dashboardStore.updateSql ++;
    const options = dashboardStore.getChartOptions(props.chartId);
    const res = await dashboardStore.saveChart(props.chartId);
    
    if (res.success) {
      // 向父组件通知更新，带上options
      emit('save', {
        chartId: props.chartId,
        options: { 
          ...options,
          sql: dashboardStore.sqlInput,
        },
      });
      onClose();
    }
  } catch (error) {
    console.error('保存图表设置出错', error);
    message.error('保存图表设置出错');
  } finally {
    isSaving.value = false;
  }
};
</script>

<style scoped>
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}
</style> 