// 图表定义
export interface Chart {
    id: number
    title: string
    type: string
    options: ChartOptions
    position_x?: number
    position_y?: number
    width?: number
    height?: number
    sort_order?: number
    dashboard_id?: number
}

// 图表选项
export interface ChartOptions {
    // 图表标题
    title: string,
    // 数据类型，'static'表示静态数据，'sql'表示通过SQL语句获取
    dataType: 'static' | 'sql'
    // 静态数据，当dataType为'static'时才有值
    data?: {
        columns: string[]
        values: any[][]
    }
    // 数据源，当dataType为'sql'时才有值
    db_name?: string
    // SQL语句，当dataType为'sql'时才有值
    sql?: string
    datasourceId?: number | string
    datasourceName?: string
    // 图表展示相关配置
    config: {
        // 图表类型
        type: string
        // 其他配置
        [key: string]: any
    }
    // 数据映射配置，指导如何将数据映射到图表
    dataMapping: {
        // 映射到X轴的字段
        xField?: string
        // 映射到Y轴的字段数组
        yFields?: string[]
        // 系列名称数组
        seriesNames?: string[]
        // 饼图特定映射
        pie?: {
            nameField?: string
            valueField?: string
        }
        // 漏斗图特定映射
        funnel?: {
            nameField?: string
            valueField?: string
        }
        // 散点图特定映射
        scatter?: {
            xField?: string
            yField?: string
            sizeField?: string
        }
        // 热力图特定映射
        heatmap?: {
            xField?: string
            yField?: string
            valueField?: string
        }
        // 雷达图特定映射
        radar?: {
            indicatorField?: string
            seriesFields?: string[]
            maxValueMapping?: Record<string, number>
        }
        // 仪表盘特定映射
        gauge?: {
            nameField?: string
            valueField?: string
        }
        // 矩形树图特定映射
        treemap?: {
            parentField?: string
            childField?: string
            valueField?: string
        }
        // 词云图特定映射
        wordCloud?: {
            wordField?: string
            valueField?: string
        }
        // 指标卡特定映射
        metric?: {
            titleField?: string
            valueField?: string
            unitField?: string
        }
        // 表格特定映射
        table?: {
            columns?: string[]
        }
        // 其他图表类型的特定映射
        [key: string]: any
    }
}

// 图表位置信息
export interface ChartPosition {
    id: number
    dashboard_id: number
    position_x: number
    position_y: number
    width: number
    height: number
}

// 创建图表的请求参数
export interface CreateChartData {
    dashboard_id: number
    title: string
    type: string
    options: string
    position_x: number
    position_y: number
    width: number
    height: number
    sort_order?: number
}

// 更新图表的请求参数
export interface UpdateChartData {
    dashboard_id: number
    title: string
    options: string
}

// 根据图表类型获取的数据集列表项
export interface DataSet {
    id: number | string;
    name: string;
    type: string;
    dataType: 'static' | 'sql';
    db_name: string;
    sql: string;
    dataMapping: Record<string, any>;
}

// 获取图表SQL数据的请求参数
export interface ChartDataRequest {
    db_name: string;
    sql: string;
    chart_type: string;
}

// 获取图表SQL数据的返回结果
export interface SqlDataResult {
    sql_data: {
        result_info: string;
        run_cost: number;
        colunms: string[];
        values: any[][];
    };
    chart_values: {
        name: string;
        type: string;
        value: string;
    }[];
    chart_type: string;
} 