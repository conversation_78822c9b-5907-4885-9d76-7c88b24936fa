<template>
  <div class="dashboard-container">
    <div class="dashboard-header" v-if="!(appStore.isPreview || isFullscreen || isShareMode)">
      <a-button @click="backToDashboardList">
        <arrow-left-outlined />
        返回看板列表
      </a-button>
      <div class="dashboard-title" v-if="dashboard">{{ dashboard.title }}</div>
      <a-button type="primary" class="add-chart-btn" @click="showChartSelector = true">
        <plus-outlined />
        添加图表
      </a-button>
      <a-button type="primary" @click="toggleFullscreen">
        <fullscreen-outlined v-if="!isFullscreen" />
        <fullscreen-exit-outlined v-else />
        {{ isFullscreen ? '退出全屏' : '全屏显示' }}
      </a-button>
      <a-button type="default" @click="showShareModal = true" style="margin-left: 8px">
        <share-alt-outlined />
        分享看板
      </a-button>
    </div>
    
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>
    
    <div v-else ref="dashboardContentRef" class="dashboard-content" :class="{ 'fullscreen': isFullscreen }" @click="handleDashboardClick">
      <GridLayout
        :layout="layout"
        :col-num="12"
        :row-height="30"
        :is-draggable="!(appStore.isPreview || isFullscreen)"
        :is-resizable="!(appStore.isPreview || isFullscreen)"
        :vertical-compact="true"
        :use-css-transforms="true"
        :margin="[10, 10]"
        @layout-updated="onLayoutUpdated"
        :style="{ height: containerHeight + 'px' }"
        class="dashboard-grid"
        :responsive="false"
      >
        <GridItem
          v-for="item in layout"
          :key="item.i"
          :x="item.x"
          :y="item.y"
          :w="item.w"
          :h="item.h"
          :i="item.i"
          class="dashboard-item"
          :class="{ 
            'selected-chart': selectedChartId === item.i && !(appStore.isPreview || isFullscreen), 
            'hovered-chart': hoveredChartId === item.i && !(appStore.isPreview || isFullscreen),
            'dashboard-item-edit': !(appStore.isPreview || isFullscreen),
          }"
          :is-draggable="!(appStore.isPreview || isFullscreen)"
          :is-resizable="(hoveredChartId === item.i || selectedChartId === item.i) && !(appStore.isPreview || isFullscreen)"
          drag-allow-from=".chart-header"
          resize-ignore-from=".chart-container"
          :style="{ border: (hoveredChartId === item.i || selectedChartId === item.i) ? '2px solid #40a9ff' : '2px solid transparent' }"
          @click.native.stop="selectChart(item.i)"
          @mouseover.native.stop="setHoveredChart(item.i)"
          @mouseleave.native.stop="setHoveredChart(null)"
        >
          <div class="chart-header" v-if="!(appStore.isPreview || isFullscreen)">
            <span class="chart-title">{{ chartData[item.i]?.title || `图表 ${item.i}` }}</span>
            <div class="chart-actions">
              <a-button 
                type="text" 
                class="chart-action-btn" 
                :style="{ opacity: (selectedChartId === item.i || hoveredChartId === item.i) ? 1 : 0 }"
                @click.stop="openSettings(item.i)"
              >
                <setting-outlined />
              </a-button>
              <a-button 
                type="text" 
                class="chart-action-btn" 
                :style="{ opacity: (selectedChartId === item.i || hoveredChartId === item.i) ? 1 : 0 }"
                @click.stop="openFullView(item.i)"
              >
                <expand-outlined />
              </a-button>
              <a-button 
                type="text" 
                class="remove-chart-btn" 
                :style="{ opacity: (selectedChartId === item.i || hoveredChartId === item.i) ? 1 : 0 }"
                @click.stop="removeChart(item.i)"
              >
                <close-outlined />
              </a-button>
            </div>
          </div>
          <div class="chart-container">
            <template v-if="chartData[item.i] && chartData[item.i].component">
              <component 
                :is="chartData[item.i].component" 
                :chart-id="item.i"
                :dashboard-id="dashboardId"
                :options="chartData[item.i].options"
                :show-title="false"
                class="chart"
              />
            </template>
            <template v-else>
              <div class="chart-error">
                <a-empty description="图表配置不存在" />
              </div>
            </template>
          </div>
        </GridItem>
      </GridLayout>
    </div>
    
    <ChartSelector 
      v-if="showChartSelector" 
      @close="showChartSelector = false"
      @select="addChart"
      :loading="isAdding"
    >
      <div class="add-progress-container" v-if="isAdding">
        <span>读取数据中…</span>
        <a-progress :percent="addingPercent" status="active" />
      </div>
    </ChartSelector>

    <ChartSetting
      :visible="showSettings"
      :chart-type="currentChartType"
      :chart-id="currentChartId"
      :chart-title="currentChartTitle"
      :dashboard-id="dashboardId"
      @close="showSettings = false"
      @save="updateChartSettings"
    />

    <!-- 图表全屏查看弹层 -->
    <a-modal
      v-model:visible="showFullViewModal"
      :title="fullViewChartTitle"
      :width="1000"
      :footer="null"
      centered
      class="chart-full-view-modal"
    >
      <div class="full-view-chart-container">
        <template v-if="fullViewChartId && chartData[fullViewChartId] && chartData[fullViewChartId].component">
          <component 
            :is="chartData[fullViewChartId].component" 
            :chart-id="fullViewChartId"
            :dashboard-id="dashboardId"
            :options="chartData[fullViewChartId].options"
            class="full-view-chart"
          />
        </template>
        <template v-else>
          <a-empty description="图表配置不存在" />
        </template>
      </div>
    </a-modal>

    <a-modal
      v-model:visible="showDeleteModal"
      title="提示"
      okText="删除"
      cancelText="取消"
      :okButtonProps="{ danger: true, loading: deleteLoading }"
      :cancelButtonProps="{ disabled: deleteLoading }"
      @ok="handleDeleteOk"
      @cancel="handleDeleteCancel"
    >
      <div class="delete-confirm-container">
        <ExclamationCircleOutlined class="delete-icon" color="warning" />
        <span>确认删除图表吗？</span>
      </div>
    </a-modal>

    <a-modal
      v-model:visible="showShareModal"
      title="分享看板"
      :footer="null"
      centered
    >
      <div style="display: flex; align-items: center; gap: 8px;">
        <a-input v-model:value="shareUrl" readonly style="flex: 1;" />
        <a-button type="primary" @click="copyShareUrl">复制链接</a-button>
      </div>
      <div style="margin-top: 12px; color: #888; font-size: 13px;">
        可将此链接嵌入iframe在外部系统中展示当前看板内容。
      </div>
    </a-modal>

  </div>
</template>

<script setup lang="ts">
import { h, ref, reactive, onMounted, watch, nextTick, onUnmounted, markRaw, createVNode  } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import ChartSelector from '../../components/selector/ChartSelector.vue';
import ChartSetting from './ChartSetting.vue';
import BarChart from '../../components/charts/BarChart.vue';
import LineChart from '../../components/charts/LineChart.vue';
import PieChart from '../../components/charts/PieChart.vue';
import ScatterChart from '../../components/charts/ScatterChart.vue';
import AreaChart from '../../components/charts/AreaChart.vue';
import RadarChart from '../../components/charts/RadarChart.vue';
import FunnelChart from '../../components/charts/FunnelChart.vue';
import HeatmapChart from '../../components/charts/HeatmapChart.vue';
import GaugeChart from '../../components/charts/GaugeChart.vue';
import TreemapChart from '../../components/charts/TreemapChart.vue';
import HorizontalBarChart from '../../components/charts/HorizontalBarChart.vue';
import WordCloudChart from '../../components/charts/WordCloudChart.vue';
import MetricChart from '../../components/charts/MetricChart.vue';
import TableChart from '../../components/charts/TableChart.vue';
import { GridLayout, GridItem } from 'vue3-grid-layout';
import { 
  PlusOutlined, 
  ArrowLeftOutlined, 
  CloseOutlined, 
  SaveOutlined, 
  SettingOutlined,
  FullscreenOutlined, 
  FullscreenExitOutlined,
  ExpandOutlined,
  ExclamationCircleOutlined,
  ShareAltOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { getDashboardDetail, saveDashboard } from '../../api/dashboard';
import { updateChartsPosition } from '../../api/chart';
import type { Dashboard } from '../../types/dashboard';
import type { Chart, ChartOptions } from '../../types/chart';
import { useDashboardStore } from '../../store/dashboard';
import { useAppStore } from '@/store/app';

const route = useRoute();
const router = useRouter();
const dashboardId = ref(parseInt(route.params.id as string, 10));
const dashboard = ref<Dashboard | null>(null);
const loading = ref(true);
const dashboardContentRef = ref<HTMLElement | null>(null);
const isFullscreen = ref(false);
const appStore = useAppStore();

// 使用dashboard store
const dashboardStore = useDashboardStore(dashboardId.value);

// 设置抽屉相关状态
const showSettings = ref(false);
const currentChartId = ref<number>(0);
const currentChartType = ref('');
const currentChartOptions = ref<any>({});
const currentChartTitle = ref('');

// 图表全屏查看相关状态
const showFullViewModal = ref(false);
const fullViewChartId = ref<number>(0);
const fullViewChartTitle = ref('');

interface LayoutItem {
  x: number;
  y: number;
  w: number;
  h: number;
  i: number;
}

interface ChartDataItem {
  title: string;
  component: any;
  options: any;
  type?: string;
}

const showChartSelector = ref(false);
const layout = ref<LayoutItem[]>([]);
const chartData = reactive<Record<number, ChartDataItem>>({});
const containerHeight = ref(500); // 初始高度
const isAdding = ref<boolean>(false);
const addingPercent = ref<number>(0);
const currChartId = ref<number>();
const showDeleteModal = ref(false);
const deleteLoading = ref(false);
const selectedChartId = ref<number | null>(null);
const hoveredChartId = ref<number | null>(null);

// 布局改变时的防抖保存
const layoutUpdatedTimeout = ref<number | null>(null);

const showShareModal = ref(false);
const shareUrl = ref('');

const isShareMode = ref(route.name === 'DashboardShare');
if (isShareMode.value) {
  appStore.setIsPreview(true);
}

const backToDashboardList = () => {
  appStore.setActiveDashboardGroup(dashboard.value?.dashboard_group_id || -1);
  router.push('/dashboard-list');
};

const chartTypes = {
  'bar': BarChart,
  'line': LineChart,
  'pie': PieChart,
  'scatter': ScatterChart,
  'area': AreaChart,
  'radar': RadarChart,
  'funnel': FunnelChart,
  'heatmap': HeatmapChart,
  'gauge': GaugeChart,
  'treemap': TreemapChart,
  'horizontal-bar': HorizontalBarChart,
  'word-cloud': WordCloudChart,
  'metric': MetricChart,
  'table': TableChart
};

// 获取看板数据
const fetchDashboardData = async () => {
  loading.value = true;
  try {
    // 获取看板详情
    const res = await getDashboardDetail(dashboardId.value);
    let chatCount = 1;
    if (res.success) {
      dashboard.value = res.data || null;
      
      // 重置布局和图表数据
      layout.value = [];
      Object.keys(chartData).forEach(key => {
        delete chartData[Number(key)];
      });
      
      // 配置存在的图表数据
      if (dashboard.value && dashboard.value.charts && dashboard.value.charts.length > 0) {
        dashboard.value.charts.forEach((chart) => {
          const chartId = chart.id;
          
          // 解析 options 字段，将 JSON 字符串转换为对象
          let chartOptions: ChartOptions;
          try {
            chartOptions = typeof chart.options === 'string' ? JSON.parse(chart.options) : chart.options;
          } catch (error) {
            console.error('解析图表选项出错', error);
            chartOptions = {
              dataType: 'static',
              data: { columns: [], values: [] },
              config: { type: chart.type || 'bar' },
              dataMapping: {},
              title: chart.title || ''
            };
          }
          
          // 添加到布局
          layout.value.push({
            x: chart.position_x || 0,
            y: chart.position_y || 0,
            w: chart.width || 4,
            h: chart.height || 8,
            i: chartId,
          });
          
          // 确保类型存在于chartTypes中，默认使用bar类型
          const chartType = chart.type && chartTypes[chart.type as keyof typeof chartTypes] 
            ? chart.type 
            : 'bar';
          
          // 添加到图表数据
          chartData[chartId] = {
            title: chart.title || `图表 ${chartId}`,
            component: markRaw(chartTypes[chartType as keyof typeof chartTypes]),
            options: chartOptions,
            type: chartType
          };

          chatCount ++;

          // console.log(layout.value);
          // console.log(chartData);
        });
        
        // 计算容器高度
        nextTick(() => {
          containerHeight.value = calculateContainerHeight();
        });
      }
    } else {
      message.error(res.err_msg || '获取看板数据失败');
    }
  } catch (error) {
    console.error('获取看板数据出错', error);
    message.error('获取看板数据出错');
  } finally {
    loading.value = false;
  }
};

// 计算容器高度
const calculateContainerHeight = () => {
  if (layout.value.length === 0) return 500; // 默认高度
  
  // 找到最大的y+h值来确定容器底部位置
  const bottomPosition = Math.max(...layout.value.map(item => (item.y + item.h) * 30 + item.y * 10));
  // 添加额外的底部间距，确保有足够空间
  const calculatedHeight = bottomPosition + 100; // 增加更多底部空间
  return Math.max(calculatedHeight, 500); // 最小高度为500px
};

const onLayoutUpdated = (newLayout: LayoutItem[]) => {
  layout.value = newLayout;
  // 计算并更新容器高度
  containerHeight.value = calculateContainerHeight();
  
  // 防抖处理：取消之前的定时任务
  if (layoutUpdatedTimeout.value !== null) {
    clearTimeout(layoutUpdatedTimeout.value);
  }
  
  // 设置新的定时任务，延迟500ms执行保存
  layoutUpdatedTimeout.value = window.setTimeout(() => {
    saveLayoutChanges(newLayout);
  }, 500);
};

// 保存布局变化
const saveLayoutChanges = async (newLayout: LayoutItem[]) => {
  // 如果没有看板ID或图表为空，则不保存
  if (!dashboardId.value || newLayout.length === 0) return;
  
  try {
    // 构建需要更新的图表数据
    const charts = newLayout.map(item => ({
      id: item.i,
      dashboard_id: dashboardId.value,
      position_x: item.x,
      position_y: item.y,
      width: item.w,
      height: item.h
    }));
    
    // 调用API批量更新图表位置
    const res = await updateChartsPosition(charts);
    
    if (!res.success) {
      console.error('保存布局失败', res.err_msg);
    }
  } catch (error) {
    console.error('保存布局出错', error);
  }
};

// 监听布局变化，更新容器高度
watch(layout, () => {
  nextTick(() => {
    containerHeight.value = calculateContainerHeight();
  });
}, { deep: true, immediate: true });

const addChart = async (chartType: any) => {
  if (isAdding.value) {
    return;
  }
  isAdding.value = true;
  // 查找适合的位置，优先考虑右侧插入
  let newX = 0;
  let newY = 0;
  addingPercent.value = 0;
  
  if (layout.value.length > 0) {
    // 获取当前所有行
    const rows: Record<number, LayoutItem[]> = {};
    layout.value.forEach(item => {
      if (!rows[item.y]) rows[item.y] = [];
      rows[item.y].push(item);
    });
    
    // 从第一行开始查找可插入的位置
    let found = false;
    Object.keys(rows).sort((a, b) => Number(a) - Number(b)).forEach(rowY => {
      if (found) return;
      
      // 获取该行最右边的位置
      const rowItems = rows[Number(rowY)];
      const rightmostX = Math.max(...rowItems.map(item => item.x + item.w));
      
      // 检查右侧是否有足够空间
      if (rightmostX + 4 <= 12) {
        newX = rightmostX;
        newY = Number(rowY);
        found = true;
      }
    });
    
    // 如果没有找到合适的位置，放在最底部
    if (!found) {
      newY = Math.max(...layout.value.map(item => item.y + item.h));
    }
  }

  // 确保chartType.type存在且有效
  if (!chartType.type || !chartTypes[chartType.type as keyof typeof chartTypes]) {
    console.error('无效的图表类型', chartType);
    return;
  }
  
  const chartTitle = `${chartType.label || '新图表'}`;
  
  // 构建符合ChartOptions结构的图表选项
  let defaultConfig = {};
  let defaultDataMapping = {};
  
  if (chartType.defaultOptions) {
    if (chartType.defaultOptions.config) {
      defaultConfig = chartType.defaultOptions.config;
    }
    
    if (chartType.defaultOptions.dataMapping) {
      defaultDataMapping = chartType.defaultOptions.dataMapping;
    }
  }
  
  const chartOptions: ChartOptions = {
    title: chartTitle,
    dataType: 'static',
    data: {
      columns: [],
      values: []
    },
    config: {
      type: chartType.type,
      title: {
        text: chartTitle,
        show: true
      },
      // 确保config中不包含数据相关配置
      ...defaultConfig
    },
    // 添加数据映射配置
    dataMapping: {
      // 默认映射规则
      xField: 'column0',
      yFields: ['column1'],
      seriesNames: ['系列1'],
      ...defaultDataMapping
    }
  };
  
  // 如果已有defaultOptions，合并其他属性
  if (chartType.defaultOptions) {
    // 合并数据类型
    if (chartType.defaultOptions.dataType === 'sql') {
      chartOptions.dataType = 'sql';
      // SQL类型需要这些字段
      chartOptions.db_name = chartType.defaultOptions.db_name || '';
      chartOptions.sql = chartType.defaultOptions.sql || '';
      // 删除静态数据字段
      delete chartOptions.data;
    } else if (chartType.defaultOptions.dataType === 'static') {
      // 静态数据，合并数据
      if (chartType.defaultOptions.data) {
        chartOptions.data = {
          columns: chartType.defaultOptions.data.columns || [],
          values: chartType.defaultOptions.data.values || []
        };
      }
    }
  }
  
  const timer = setInterval(() => {
    if (addingPercent.value < 90) {
      addingPercent.value += 10; // 每次增加10%
    }
  }, 100);
  
  try {
    // 使用store添加图表，不再预先创建图表ID
    const result = await dashboardStore.addChart({
      dashboard_id: dashboardId.value,
      title: chartTitle,
      type: chartType.type,
      options: chartOptions,
      position_x: newX,
      position_y: newY,
      width: 4,
      height: 8,
      sort_order: layout.value.length
    });
    
    if (result.success && result.data) {
      const chart = result.data;
      const chartId = chart.id;
      
      // 添加到本地图表数据
      chartData[chartId] = {
        title: chart.title,
        component: markRaw(chartTypes[chart.type as keyof typeof chartTypes]),
        options: chart.options,
        type: chart.type
      };
      
      // 添加到布局
      layout.value.push({
        x: chart.position_x || 0,
        y: chart.position_y || 0,
        w: chart.width || 4,
        h: chart.height || 8,
        i: chartId
      });
      
      // 重新计算容器高度
      nextTick(() => {
        containerHeight.value = calculateContainerHeight();
      });
    }
  } catch (error) {
    console.error('创建图表出错', error);
    message.error('创建图表出错');
  }
  addingPercent.value = 100;
  clearInterval(timer);
  
  isAdding.value = false;
  showChartSelector.value = false;
};

const handleDeleteOk = async () => {
  deleteLoading.value = true;
  // 删除逻辑...
  try {
    // 使用store删除图表
    const result = await dashboardStore.removeChart(currChartId.value!);
    
    if (result.success) {
      const index = layout.value.findIndex(item => item.i === currChartId.value);
      if (index !== -1) {
        layout.value.splice(index, 1);
        delete chartData[currChartId.value || 0];
        
        // 重新整理布局
        compactLayout();
      }
    }
  } catch (error) {
    console.error('删除图表出错', error);
    message.error('删除图表出错');
  }
  // 删除完成后
  currChartId.value = undefined;
  showDeleteModal.value = false;
  deleteLoading.value = false;
};

const handleDeleteCancel = () => {
  showDeleteModal.value = false;
};

const removeChart = async (id: number) => {
  currChartId.value = id;
  showDeleteModal.value = true;
};

// 重新整理布局，使图表紧凑排列
const compactLayout = () => {
  if (layout.value.length === 0) return;
  
  // 按照y坐标排序，然后按照x坐标排序
  const sortedItems = [...layout.value].sort((a, b) => {
    if (a.y === b.y) return a.x - b.x;
    return a.y - b.y;
  });
  
  // 清空现有布局
  layout.value = [];
  
  // 重新计算每个图表的位置
  sortedItems.forEach((item, index) => {
    const row = Math.floor(index / 3); // 每行最多3个图表
    const col = index % 3;
    
    layout.value.push({
      ...item,
      x: col * 4, // 每个图表宽度为4
      y: row * 8  // 每个图表高度为8
    });
  });
  
  // 更新容器高度
  nextTick(() => {
    containerHeight.value = calculateContainerHeight();
  });
};

// 打开图表设置抽屉
const openSettings = (chartId: number) => {
  const chart = chartData[chartId];
  if (!chart) {
    message.error('图表不存在或配置不完整');
    return;
  }
  
  if (!chart.type) {
    message.error('图表类型未定义');
    return;
  }
  
  currentChartId.value = chartId;
  console.log('currentChartId', currentChartId.value);
  currentChartType.value = chart.type;
  currentChartTitle.value = chart.title || `图表 ${chartId}`;
  
  showSettings.value = true;
};

// 更新图表设置
const updateChartSettings = async (data: { chartId: number; options: any; title?: string }) => {
  const { chartId, options, title } = data;

  if (!options) {
    message.error('图表配置不存在，无法保存');
    return;
  }

  if (chartData[chartId]) {
    // 确保options符合ChartOptions结构
    const updatedOptions: ChartOptions = {
      title: chartData[chartId].title,
      dataType: options.dataType || 'static',
      config: options.config || {
        type: chartData[chartId].type || 'bar'
      },
      dataMapping: options.dataMapping || {}
    };
    
    // 根据数据类型设置相应的属性
    updatedOptions.data = options.data || {
      columns: [],
      values: []
    };
    if (updatedOptions.dataType === 'sql') {
      updatedOptions.db_name = options.db_name || '';
      updatedOptions.sql = options.sql || '';
      updatedOptions.datasourceId = options.datasourceId;
      updatedOptions.datasourceName = options.datasourceName;
    }
    
    // 如果更新了标题，同时更新config中的标题
    if (title) {
      chartData[chartId].title = title;
      
      if (!updatedOptions.config.title) {
        updatedOptions.config.title = { text: title, show: true };
      } else {
        updatedOptions.config.title.text = title;
      }
    }
    
    // 更新本地图表数据
    chartData[chartId].options = updatedOptions;
    
    // 使用store更新图表选项
    dashboardStore.updateChartOptions(chartId, updatedOptions);
  }
};

// 全屏控制
const toggleFullscreen = () => {
  if (!dashboardContentRef.value) return;
  
  if (!isFullscreen.value) {
    // 进入全屏
    if (dashboardContentRef.value.requestFullscreen) {
      dashboardContentRef.value.requestFullscreen();
    } else if ((dashboardContentRef.value as any).mozRequestFullScreen) {
      (dashboardContentRef.value as any).mozRequestFullScreen();
    } else if ((dashboardContentRef.value as any).webkitRequestFullscreen) {
      (dashboardContentRef.value as any).webkitRequestFullscreen();
    } else if ((dashboardContentRef.value as any).msRequestFullscreen) {
      (dashboardContentRef.value as any).msRequestFullscreen();
    }
    isFullscreen.value = true;
  } else {
    // 退出全屏
    if (document.exitFullscreen) {
      document.exitFullscreen();
    } else if ((document as any).mozCancelFullScreen) {
      (document as any).mozCancelFullScreen();
    } else if ((document as any).webkitExitFullscreen) {
      (document as any).webkitExitFullscreen();
    } else if ((document as any).msExitFullscreen) {
      (document as any).msExitFullscreen();
    }
    isFullscreen.value = false;
  }
};

// 监听全屏变化事件
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement;
};

// 选择图表
const selectChart = (id: number): void => {
  if (selectedChartId.value === id) {
    selectedChartId.value = null; // 再次点击已选中的图表会取消选中
  } else {
    selectedChartId.value = id;
  }
};

// 处理看板点击
const handleDashboardClick = (event: MouseEvent): void => {
  // 如果点击的是dashboard-content元素本身而不是其子元素，则取消选中
  if (event.target === event.currentTarget) {
    selectedChartId.value = null;
  }
};

// 设置悬停图表
const setHoveredChart = (id: number | null): void => {
  // 防止频繁更新导致的重排
  if (hoveredChartId.value !== id) {
    hoveredChartId.value = id;
  }
};

// 打开图表全屏查看
const openFullView = (chartId: number) => {
  const chart = chartData[chartId];
  if (!chart) {
    message.error('图表数据不存在');
    return;
  }
  
  fullViewChartId.value = chartId;
  fullViewChartTitle.value = chart.title || `图表 ${chartId}`;
  showFullViewModal.value = true;
};

watch(showShareModal, (val) => {
  if (val) {
    // 生成分享链接
    const origin = window.location.origin;
    shareUrl.value = `${origin}/dashboard-share/${dashboardId.value}`;
  }
});

const copyShareUrl = () => {
  if (!shareUrl.value) return;
  navigator.clipboard.writeText(shareUrl.value).then(() => {
    message.success('已复制到剪贴板');
  });
};

onMounted(async () => {
  // 初始化容器高度
  containerHeight.value = calculateContainerHeight();
  
  // 先初始化store，确保store中有数据
  await dashboardStore.initDashboard();
  
  // 获取看板数据
  fetchDashboardData();
  
  // 添加全屏变化事件监听
  document.addEventListener('fullscreenchange', handleFullscreenChange);
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
  document.addEventListener('mozfullscreenchange', handleFullscreenChange);
  document.addEventListener('MSFullscreenChange', handleFullscreenChange);
});

onUnmounted(() => {
  // 移除全屏变化事件监听
  document.removeEventListener('fullscreenchange', handleFullscreenChange);
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
  document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
  document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
});
</script>

<style scoped lang="scss">
.dashboard-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 0;
  margin: 0;
}

.dashboard-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 12px;
  padding: 0;
}

.dashboard-title {
  font-size: 18px;
  font-weight: bold;
  margin-left: 12px;
}

.add-chart-btn {
  margin-left: auto;
}

.dashboard-content {
  flex: 1;
  overflow: auto;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  padding: 4px;
  position: relative;
}

.dashboard-content.fullscreen {
  z-index: 9999;
  background-color: white;
  padding: 20px;
}

.fullscreen-exit-btn {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.fullscreen-exit-btn .ant-btn {
  font-weight: bold;
  padding: 0 20px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  width: 100%;
}

/* 自定义滚动条样式 */
.dashboard-content::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.dashboard-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 5px;
}

.dashboard-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 5px;
  border: 2px solid #f1f1f1;
}

.dashboard-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.dashboard-grid {
  width: 100%;
  border-radius: 4px;
  min-height: 500px;
  overflow: visible;
  transition: height 0.3s ease;
  position: relative;
}

.dashboard-item {
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  /* 固定尺寸不变化 */
  outline: 0 none;
  padding: 0;
  margin: 0;
  transition: border-color 0.3s ease !important;
  border: 2px solid transparent !important;
  box-sizing: border-box !important;
}

/* 通过!important确保CSS优先级 */
:deep(.dashboard-item-edit:hover) {
  border: 2px solid #40a9ff !important;
  box-shadow: 0 0 8px rgba(64, 169, 255, 0.5) !important;
  z-index: 10 !important;
}

/* 确保鼠标悬停时有高亮边框 */
.dashboard-grid-edit :deep(.vue-grid-item:hover) {
  border: 2px solid #40a9ff !important;
  box-shadow: 0 0 8px rgba(64, 169, 255, 0.5) !important;
  z-index: 10 !important;
}

/* 选中样式优先级高于悬停 */
.dashboard-item-edit.selected-chart {
  border: 2px solid #1890ff !important;
  box-shadow: 0 0 8px rgba(24, 144, 255, 0.5) !important;
  z-index: 10 !important;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  cursor: move;
  user-select: none;
  height: 40px;
  box-sizing: border-box;
}

.chart-title {
  font-weight: 500;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 80%;
}

.chart-actions {
  display: flex;
  align-items: center;
  min-width: 64px;
  justify-content: flex-end;
}

.chart-action-btn,
.remove-chart-btn {
  color: #999;
  margin-left: 4px;
  transition: opacity 0.2s ease, color 0.2s ease;
}

.chart-action-btn:hover {
  color: #1890ff;
}

.remove-chart-btn:hover {
  color: #ff4d4f;
}

.chart-container {
  flex: 1;
  padding: 10px;
  overflow: hidden;
  position: relative;
}

.chart {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;
  bottom: 10px;
}

/* 强制样式覆盖 */
.dashboard-grid .vue-grid-item .vue-resizable-handle {
  opacity: 0 !important;
  z-index: 100;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10"><path fill="%231890ff" d="M0,0 L10,0 L10,10 L0,10 Z"/><path fill="white" d="M8,2 L8,8 L2,8 Z"/></svg>');
  background-position: bottom right;
  background-repeat: no-repeat;
  transition: opacity 0.3s;
}

.dashboard-grid .vue-grid-item:hover .vue-resizable-handle {
  opacity: 1 !important;
}

.dashboard-item:hover > .vue-resizable-handle,
.dashboard-item.hovered-chart > .vue-resizable-handle {
  opacity: 1 !important;
}

.chart-full-view-modal {
  .full-view-chart-container {
    height: 600px;
    overflow: hidden;
    position: relative;
  }
  
  .full-view-chart {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
}

.chart-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.add-progress-container {
  padding: 20px;
}

.delete-confirm-container {
  display: flex;
  align-items: center;
  .delete-icon {
    font-size: 22px;
    margin-right: 10px;
    color: #faad14;
  }
}
</style> 