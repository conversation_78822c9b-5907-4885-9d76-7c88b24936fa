/** 状态 */
export enum AppTypeEnum {
    /** 对话型应用 */
    TALK = 'talk',
    /** 文本生产型应用 */
    TEXT_GEN = 'text_generation',
    DB_ASSISTANT = 'db_assistant',
}

export enum WsDataTypeEnum {
    // 表示后端已收到前端发来的query msg
    ACK = 'acknowledgment',
    // 表示生产回答已完成，返回数据是生产完成后的文本
    TEXT_MSG = 'textMessage',
    // 表示返回数据是正在生成的流内容
    STREAM_DATA = "streamingData",
    // 表示返回数据是报错信息
    ERR = "error",
    // 表示返回数据是报错信息
    SQLDATA = "sqlData",
    //Echarts代码
    ECHARTS_CODE = "echartsCode",
    // 表示返回数据是追问
    TEXT_QA = "questionsAfter",
    // 思考内容
    REASONER = 'reasoner',
    //联网搜索结果
    WEB_SEARCH_RESULT = "webSearchResult",
    //RAG\DB 搜索结果
    RETRIEVER_RESULT = "retrieverResult",
} 