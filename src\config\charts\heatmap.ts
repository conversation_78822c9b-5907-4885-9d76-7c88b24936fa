import legendOptions from '../e-charts/legend';
import tooltipOptions from '../e-charts/tooltip';

const heatmapDefaultOptions = {
  dataType: 'static',
  data: {
    columns: ['时间', '日期', '值'],
    values: [
      ['12a', '周一', 5],
      ['1a', '周一', 7],
      ['2a', '周一', 3],
      ['3a', '周二', 5],
      ['4a', '周二', 2],
      ['5a', '周三', 4],
      ['6a', '周三', 7],
      ['7a', '周四', 1],
      ['8a', '周四', 6],
      ['9a', '周五', 4],
      ['10a', '周五', 8],
      ['11a', '周六', 5],
      ['12p', '周六', 2],
      ['1p', '周日', 3]
    ]
  },
  config: {
    type: 'heatmap',
    tooltip: tooltipOptions,
    legend: legendOptions,
    visualMap: {
      min: 0,
      max: 10,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '15%'
    },
    series: [
      {
        type: 'heatmap',
        data: []
      }
    ]
  },
  dataMapping: {
    heatmap: {
      xField: '时间',
      yField: '日期',
      valueField: '值'
    }
  }
};
export default heatmapDefaultOptions; 