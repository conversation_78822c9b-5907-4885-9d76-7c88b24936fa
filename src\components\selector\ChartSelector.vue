<template>
  <div class="chart-selector-overlay" @click.self="$emit('close')">
    <div class="chart-selector-modal">
      <div class="chart-selector-header">
        <h2>选择图表类型</h2>
        <button class="close-btn" @click="$emit('close')">×</button>
      </div>
      <div class="chart-selector-content">
        <div 
          v-for="chart in availableCharts" 
          :key="chart.type"
          class="chart-option"
          @click="selectChart(chart)"
          v-loading="props.loading && currChatType === chart.type"
        >
          <div class="chart-option-icon">
            <img :src="chart.icon" :alt="chart.label" />
          </div>
          <div class="chart-option-label">{{ chart.label }}</div>
        </div>
      </div>
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import barIcon from '../../assets/icons/chart/bar.png';
import horizontalBarIcon from '../../assets/icons/chart/horizontal-bar.png';
import lineIcon from '../../assets/icons/chart/line.png';
import areaIcon from '../../assets/icons/chart/area.png';
import pieIcon from '../../assets/icons/chart/pie.png';
import scatterIcon from '../../assets/icons/chart/scatter.png';
import radarIcon from '../../assets/icons/chart/radar.png';
import funnelIcon from '../../assets/icons/chart/funnel.png';
import heatmapIcon from '../../assets/icons/chart/heatmap.png';
import gaugeIcon from '../../assets/icons/chart/gauge.png';
import treemapIcon from '../../assets/icons/chart/treemap.png';
import wordCloudIcon from '../../assets/icons/chart/word-cloud.png';
import metricIcon from '../../assets/icons/chart/metric.png';
import tableIcon from '../../assets/icons/chart/table.png';
import barDefaultOptions from '../../config/charts/bar';
import horizontalBarDefaultOptions from '../../config/charts/horizontal-bar';
import lineDefaultOptions from '../../config/charts/line';
import areaDefaultOptions from '../../config/charts/area';
import pieDefaultOptions from '../../config/charts/pie';
import scatterDefaultOptions from '../../config/charts/scatter';
import radarDefaultOptions from '../../config/charts/radar';
import funnelDefaultOptions from '../../config/charts/funnel';
import heatmapDefaultOptions from '../../config/charts/heatmap';
import gaugeDefaultOptions from '../../config/charts/gauge';
import treemapDefaultOptions from '../../config/charts/treemap';
import wordCloudDefaultOptions from '../../config/charts/word-cloud';
import metricDefaultOptions from '../../config/charts/metric';
import tableDefaultOptions from '../../config/charts/table';

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  }
});

const emit = defineEmits(['close', 'select']);

const currChatType = ref<string>();

// 可用图表类型
interface ChartType {
  type: string;
  label: string;
  icon: string;
  description?: string;
  defaultOptions: any;
}

const availableCharts: ChartType[] = [
  {
    type: 'bar',
    label: '柱状图',
    icon: barIcon,
    defaultOptions: barDefaultOptions
  },
  {
    type: 'horizontal-bar',
    label: '条形图',
    icon: horizontalBarIcon,
    defaultOptions: horizontalBarDefaultOptions
  },
  {
    type: 'line',
    label: '折线图',
    icon: lineIcon,
    defaultOptions: lineDefaultOptions
  },
  {
    type: 'area',
    label: '面积图',
    icon: areaIcon,
    defaultOptions: areaDefaultOptions
  },
  {
    type: 'pie',
    label: '饼图',
    icon: pieIcon,
    defaultOptions: pieDefaultOptions
  },
  {
    type: 'scatter',
    label: '散点图',
    icon: scatterIcon,
    defaultOptions: scatterDefaultOptions
  },
  {
    type: 'radar',
    label: '雷达图',
    icon: radarIcon,
    defaultOptions: radarDefaultOptions
  },
  {
    type: 'funnel',
    label: '漏斗图',
    icon: funnelIcon,
    defaultOptions: funnelDefaultOptions
  },
  {
    type: 'heatmap',
    label: '热力图',
    icon: heatmapIcon,
    defaultOptions: heatmapDefaultOptions
  },
  {
    type: 'gauge',
    label: '仪表盘',
    icon: gaugeIcon,
    defaultOptions: gaugeDefaultOptions
  },
  {
    type: 'treemap',
    label: '矩形树图',
    icon: treemapIcon,
    defaultOptions: treemapDefaultOptions
  },
  {
    type: 'word-cloud',
    label: '词云图',
    icon: wordCloudIcon,
    defaultOptions: wordCloudDefaultOptions
  },
  {
    type: 'metric',
    label: '指标图',
    icon: metricIcon,
    defaultOptions: metricDefaultOptions
  },
  {
    type: 'table',
    label: '表格',
    icon: tableIcon,
    defaultOptions: tableDefaultOptions
  }
];

const selectChart = (chart: ChartType) => {
  if (props.loading) {
    return;
  }
  currChatType.value = chart.type;
  emit('select', chart);
};
</script>

<style scoped>
.chart-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.chart-selector-modal {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 600px;
  max-width: 90%;
  max-height: 80%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chart-selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.chart-selector-header h2 {
  margin: 0;
  font-size: 18px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close-btn:hover {
  background-color: #f0f0f0;
  color: #666;
}

.chart-selector-content {
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  grid-gap: 16px;
  overflow-y: auto;
}

.chart-option {
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 12px;
  cursor: pointer;
  text-align: center;
  transition: all 0.2s;
}

.chart-option:hover {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.chart-option-icon {
  margin-bottom: 8px;
  display: flex;
  justify-content: center;
}

.chart-option-icon img {
  width: 48px;
  height: 48px;
  object-fit: contain;
}

.chart-option-label {
  font-size: 14px;
}
</style> 