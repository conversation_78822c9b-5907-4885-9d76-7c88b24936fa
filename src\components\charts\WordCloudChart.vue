<template>
  <BaseChart 
    :chart-id="chartId" 
    :dashboard-id="dashboardId"
    @data-loaded="onDataLoaded"
    @error="onError"
  >
    <template #default>
      <div class="chart-wrapper" v-if="showChat">
        <v-chart :option="processedOptions" autoresize />
      </div>
    </template>
  </BaseChart>
</template>

<script setup lang="ts">
import { ref, markRaw } from 'vue';
import VChart from 'vue-echarts';
import BaseChart from './BaseChart.vue';
import 'echarts-wordcloud';

defineProps({
  chartId: {
    type: Number,
    required: true
  },
  dashboardId: {
    type: Number,
    required: true
  }
});

// 处理后的图表ECharts配置
const processedOptions = ref<any>({});
const showChat = ref<boolean>(false);

// 词云图颜色列表
const WORD_CLOUD_COLORS = [
  '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de',
  '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#6e7079'
];

/**
 * 处理词云图数据
 */
const processWordCloudData = (rawData: any): any => {
  const { config = {}, data, dataMapping } = rawData;
  
  // 如果没有数据，返回基础配置
  if (!data?.columns || !data?.values || data.values.length === 0) {
    return false;
  }

  const { columns, values } = data;
  
  // 使用映射逻辑或默认逻辑
  let wordField = 0; // 默认第一列为词语
  let valueField = 1; // 默认第二列为权重
  
  // 如果提供了映射配置，使用映射配置
  if (dataMapping && dataMapping.wordCloud) {
    // 查找wordField对应的列索引
    if (dataMapping.wordCloud.wordField) {
      const wordFieldIndex = columns.findIndex((col: string) => col === dataMapping.wordCloud.wordField);
      if (wordFieldIndex !== -1) {
        wordField = wordFieldIndex;
      }
    }
    
    // 查找valueField对应的列索引
    if (dataMapping.wordCloud.valueField) {
      const valueFieldIndex = columns.findIndex((col: string) => col === dataMapping.wordCloud.valueField);
      if (valueFieldIndex !== -1) {
        valueField = valueFieldIndex;
      }
    }
  }
  
  // 构建词云数据
  const cloudData = values
    .filter((row: any[]) => row[wordField] !== undefined && row[valueField] !== undefined)
    .map((row: any[]) => ({
      name: String(row[wordField] || ''),
      value: Number(row[valueField]) || 0
    }))
    .filter((item: any) => item.value > 0 && item.name.trim() !== '');
  
  // 如果没有有效数据
  if (cloudData.length === 0) {
    return {
      title: {
        text: '暂无有效数据',
        show: true,
        textStyle: {
          fontSize: 14,
          color: '#999'
        },
        left: 'center',
        top: 'center'
      },
      series: [{
        type: 'wordCloud',
        shape: 'circle',
        data: [{ name: '暂无有效数据', value: 100 }]
      }]
    };
  }
  
  // 词云数据排序，使较大的词排在前面
  cloudData.sort((a: {name: string, value: number}, b: {name: string, value: number}) => b.value - a.value);
  
  // 构建返回配置
  return {
    ...config,
    tooltip: {
      show: true,
      formatter: '{b}: {c}'
    },
    series: [{
      type: 'wordCloud',
      shape: 'circle',
      left: 'center',
      top: 'center',
      width: '90%',
      height: '90%',
      right: 'center',
      bottom: 'center',
      sizeRange: [14, 60],
      rotationRange: [-45, 45],
      rotationStep: 10,
      gridSize: 8,
      drawOutOfBound: false,
      textStyle: {
        fontFamily: 'Microsoft YaHei, sans-serif',
        fontWeight: 'normal',
        color: function(params: any) {
          return WORD_CLOUD_COLORS[params.dataIndex % WORD_CLOUD_COLORS.length];
        }
      },
      emphasis: {
        textStyle: {
          fontWeight: 'bold',
          shadowBlur: 5,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        }
      },
      data: cloudData
    }]
  };
};

// 数据加载完成
const onDataLoaded = (rawData: any) => {
  try {
    // 直接处理原始数据
    const processedConfig = processWordCloudData(rawData);
    if (!processedConfig) {
      showChat.value = false;
      return;
    }
    
    // 使用markRaw避免Vue对复杂对象进行递归响应式处理
    processedOptions.value = markRaw(processedConfig);
    showChat.value = true;
  } catch (error) {
    console.error('处理词云图数据出错', error);
    processedOptions.value = {};
    showChat.value = false;
  }
};

// 数据加载错误
const onError = (error: string) => {
  console.error('图表数据加载错误', error);
  
  processedOptions.value = markRaw({
    title: {
      text: '加载错误',
      subtext: error,
      left: 'center',
      top: 'center',
      textStyle: {
        fontSize: 16,
        color: '#e74c3c'
      },
      subtextStyle: {
        fontSize: 13,
        color: '#7f8c8d'
      }
    },
    series: [{
      type: 'wordCloud',
      shape: 'circle',
      left: 'center',
      top: 'center',
      width: '80%',
      height: '80%',
      data: [{ name: '加载错误', value: 100 }]
    }]
  });
};
</script>

<style scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;
}
</style>