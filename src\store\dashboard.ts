import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { update<PERSON><PERSON>, deleteChart, create<PERSON><PERSON> } from '../api/chart';
import { getDashboardDetail } from '../api/dashboard';
import { message } from 'ant-design-vue';
import { cloneDeep, isEqual } from 'lodash-es';
import type { ChartOptions, Chart, CreateChartData, UpdateChartData } from '../types/chart';

// 添加类型定义
interface ChartMap {
  [chartId: string]: Chart;
}

interface ChartKeyMap {
  [chartId: string]: number;
}
interface DashboardStores {
  [key: string]: any;
}

// 动态store注册表
const dashboardStores: DashboardStores = {};

/**
 * 创建基于特定看板ID的store
 * @param dashboardId 看板ID
 */
export function useDashboardStore(dashboardId: number) {
  // 确保store key是字符串
  const storeKey = `dashboard-${dashboardId}`;
  
  // 如果store已存在，直接返回
  if (dashboardStores[storeKey]) {
    return dashboardStores[storeKey];
  }

  // 创建新的store
  const store = defineStore(storeKey, () => {
    // 状态 - 使用Map结构存储图表
    const charts = ref<ChartMap>({});
    const chartRefs = ref<ChartMap>({});
    const chartsKey = ref<ChartKeyMap>({});
    const loading = ref(false);
    const initialized = ref(false);
    const title = ref('');
    const description = ref('');
    const refreshSet = ref(0);
    const sqlInput = ref<string>();
    const updateSql = ref<number>(0);

    // 计算属性
    const getChartById = computed(() => {
      return (chartId: number) => charts.value[chartId] || null;
    });

     const getChartKeyById = computed(() => {
      return (chartId: number) => chartsKey.value[chartId] || null;
    });
    
    const getChartOptions = computed(() => {
      return (chartId: number) => {
        // console.log(charts.value);
        // console.log(chartId);
        const chart = getChartById.value(chartId);
        return chart ? chart.options : null;
      }
    });

     const getChartKey = computed(() => {
      return (chartId: number) => {
        // console.log(charts.value);
        // console.log(chartId);
        return getChartKeyById.value(chartId);
      }
    });
    
    const getChartsList = computed(() => {
      return Object.values(charts.value);
    });

    // Actions
    
    /**
     * 初始化看板数据
     */
    async function initDashboard() {
      // if (initialized.value) return;
      
      loading.value = true;
      try {
        // 获取看板详情
        const res = await getDashboardDetail(dashboardId);
        if (res.success && res.data) {
          // 设置基本信息
          title.value = res.data.title || '';
          description.value = res.data.description || '';
          
          // 将图表数组转换为Map结构
          const chartsMap: ChartMap = {};
          if (res.data.charts && Array.isArray(res.data.charts)) {
            res.data.charts.forEach(chart => {
              // 解析 options 字段，将 JSON 字符串转换为对象
              let chartOptions: ChartOptions;
              try {
                chartOptions = typeof chart.options === 'string' ? JSON.parse(chart.options) : chart.options;
              } catch (error) {
                console.error('解析图表选项出错', error);
                chartOptions = {
                  title: chart.title || '',
                  dataType: 'static',
                  data: { columns: [], values: [] },
                  config: { type: chart.type || 'bar' },
                  dataMapping: {}
                };
              }
              
              chartsMap[chart.id] = {
                ...chart,
                options: chartOptions
              };
              chartsKey.value[chart.id] = 0;
            });
          }
          charts.value = chartsMap;
          chartRefs.value = cloneDeep(chartsMap); // 深拷贝图表引用
          initialized.value = true;
        } else {
          message.error('获取看板数据失败：' + (res.err_msg || '未知错误'));
        }
      } catch (error) {
        console.error('初始化看板数据失败:', error);
        message.error('初始化看板数据失败');
      } finally {
        loading.value = false;
      }
    }

    /**
     * 更新图表配置
     */
    function updateChartOptions(chartId: number, newOptions: ChartOptions) {
      if (charts.value[chartId]) {
        console.log('updateChartOptions-newOptions', newOptions);
        console.log('chartId-newOptions', chartId);
        // Vue 3 响应式对象更新
        if (!isEqual(charts.value[chartId].options, newOptions)) {
          charts.value[chartId] = {
            ...charts.value[chartId],
            options: cloneDeep(newOptions),
          };
        }
        // // Vue 3 响应式对象更新
        // const chatsTmp: any = cloneDeep(charts.value[chartId]);
        // delete chatsTmp.options;
        // chatsTmp.options = cloneDeep(newOptions);
        // charts.value[chartId] = cloneDeep(chatsTmp);
        console.log('chartsMap-update', charts.value);
      }
    }

    /**
     * 保存图表配置到服务器
     */
    async function saveChart(chartId: number) {
      chartsKey.value[chartId] ++;
      try {
        if (charts.value[chartId].options && charts.value[chartId].options.hasOwnProperty('dataType') && charts.value[chartId].options.dataType === 'static') {
          if (charts.value[chartId].options.hasOwnProperty('datasourceId')) {
            delete charts.value[chartId].options.datasourceId;
          }
          if (charts.value[chartId].options.hasOwnProperty('datasourceName')) {
            delete charts.value[chartId].options.datasourceName;
          }
          if (charts.value[chartId].options.hasOwnProperty('db_name')) {
            delete charts.value[chartId].options.db_name;
          }
          if (charts.value[chartId].options.hasOwnProperty('sql')) {
            delete charts.value[chartId].options.sql;
          }
          sqlInput.value = '';
        }
      } catch (error) {
        console.error('处理图表配置出错', error);
      }
      
      const chart = getChartById.value(chartId);
      if (!chart) return { success: false, err_msg: '图表不存在' };

      try {

        const data: UpdateChartData = {
          dashboard_id: dashboardId,
          title: chart.options.title,
          options: JSON.stringify({
            ...chart.options,
            sql: sqlInput.value,
          })
        }

        if (charts.value[chartId].options) {
          charts.value[chartId].options.sql = sqlInput.value; // 更新本地图表标题
        }
        
        // 调用API保存图表
        const res = await updateChart(chartId, data);

        if (res.success) {
          message.success('保存成功');
          chartRefs.value = cloneDeep(charts.value); // 更新图表引用
          return res;
        } else {
          message.error(res.err_msg || '保存失败');
          return res;
        }
      } catch (error) {
        console.error('保存图表配置出错', error);
        message.error('保存图表配置出错');
        return { success: false, err_msg: '保存图表配置出错' };
      }
    }

    /**
     * 添加新图表
     */
    async function addChart(chartData: Partial<Chart> & { dashboard_id: number }) {
      try {


        const data: CreateChartData = {
          dashboard_id: dashboardId,
          title: chartData.title || '',
          type: chartData.type || '',
          options: JSON.stringify(chartData.options),
          position_x: chartData.position_x || 0,
          position_y: chartData.position_y || 0,
          width: chartData.width || 4,
          height: chartData.height || 8,
          sort_order: chartData.sort_order
        }

        const res = await createChart(data);

        if (res.success && res.data) {
          // 解析返回的 options 字段，如果是字符串则转换为对象
          let chartOptions: ChartOptions;
          if (res.data.options) {
            try {
              chartOptions = typeof res.data.options === 'string' ? JSON.parse(res.data.options) : res.data.options;
            } catch (error) {
              console.error('解析图表选项出错', error);
              chartOptions = chartData.options || {
                title: chartData.title || '',
                dataType: 'static',
                data: { columns: [], values: [] },
                config: { type: chartData.type || 'bar' },
                dataMapping: {}
              };
            }
          } else {
            chartOptions = chartData.options || {
              title: chartData.title || '',
              dataType: 'static',
              data: { columns: [], values: [] },
              config: { type: chartData.type || 'bar' },
              dataMapping: {}
            };
          }
          
          // 添加到本地状态，确保 options 是对象格式
          charts.value[res.data.id] = {
            ...res.data,
            options: chartOptions
          };
          chartRefs.value[res.data.id] = {
            ...res.data,
            options: chartOptions
          };
          chartsKey.value[res.data.id] = 0; // 初始化 key
          
          message.success('添加成功');
          return { success: true, data: {
            ...res.data,
            options: chartOptions
          }};
        } else {
          message.error(res.err_msg || '添加失败');
          return { success: false, err_msg: res.err_msg };
        }
      } catch (error) {
        console.error('添加失败', error);
        message.error('添加失败');
        return { success: false, err_msg: '添加失败' };
      }
    }

    /**
     * 删除图表
     */
    async function removeChart(chartId: number) {
      try {
        const res = await deleteChart(chartId);
        
        if (res.success) {
          // 从本地状态中移除
          const { [chartId]: removed, ...rest } = charts.value;
          charts.value = rest;
          message.success('删除成功');
          return { success: true };
        } else {
          message.error(res.err_msg || '删除失败');
          return { success: false, err_msg: res.err_msg };
        }
      } catch (error) {
        console.error('删除失败', error);
        message.error('删除失败');
        return { success: false, err_msg: '删除失败' };
      }
    }

    const updateChartFromRef = (chatId: string) => {
      for (const i in chartRefs.value) {
        if (Number(i) === Number(chatId)) {
          if (!isEqual(charts.value[i], chartRefs.value[i])) {
            charts.value[i] =   cloneDeep(chartRefs.value[i]);
          }
        }
      }
    }

    return {
      // 状态
      charts,
      chartsKey,
      loading,
      initialized,
      title,
      description,
      refreshSet,
      sqlInput,
      updateSql,
      
      // 计算属性
      getChartById,
      getChartOptions,
      getChartKey,
      getChartsList,
      
      // Actions
      initDashboard,
      updateChartOptions,
      updateChartFromRef,
      saveChart,
      addChart,
      removeChart
    };
  })();

  // 存储到注册表
  dashboardStores[storeKey] = store;
  return store;
}

// 清除指定看板的store
export function clearDashboardStore(dashboardId: string) {
  if (dashboardStores[dashboardId]) {
    delete dashboardStores[dashboardId];
  }
}

// 清除所有看板store
export function clearAllDashboardStores() {
  Object.keys(dashboardStores).forEach(key => {
    delete dashboardStores[key];
  });
} 