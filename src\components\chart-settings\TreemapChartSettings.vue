<template>
  <common-chart-settings
    :options="props.options"
    chart-type="treemap"
    @update="updateOptions"
  >
    <!-- 样式配置 -->
    <template #style-settings>
      <!-- 树图设置 -->
      <a-divider orientation="left">树图设置</a-divider>
      
      <a-form-item label="叶子节点间隙">
        <a-slider 
          v-model:value="chartConfig.series[0].leafDepth"
          :min="0" 
          :max="5" 
          :step="0.1"
          @change="updateConfig" 
        />
      </a-form-item>
      
      <a-form-item label="显示标签">
        <a-switch v-model:checked="chartConfig.series[0].label.show" @change="updateConfig" />
      </a-form-item>
      
      <a-form-item label="上边距">
        <a-input-number 
          v-model:value="chartConfig.grid.top" 
          :min="0" 
          :max="100" 
          style="width: 100%"
          @change="updateConfig" 
        />
      </a-form-item>
      
      <a-form-item label="右边距">
        <a-input-number 
          v-model:value="chartConfig.grid.right" 
          :min="0" 
          :max="100" 
          style="width: 100%"
          @change="updateConfig" 
        />
      </a-form-item>
      
      <a-form-item label="下边距">
        <a-input-number 
          v-model:value="chartConfig.grid.bottom" 
          :min="0" 
          :max="100" 
          style="width: 100%"
          @change="updateConfig" 
        />
      </a-form-item>
      
      <a-form-item label="左边距">
        <a-input-number 
          v-model:value="chartConfig.grid.left" 
          :min="0" 
          :max="100" 
          style="width: 100%"
          @change="updateConfig" 
        />
      </a-form-item>
      
      <!-- 视觉映射 -->
      <a-divider orientation="left">视觉映射设置</a-divider>
      
      <a-form-item label="启用视觉映射">
        <a-switch v-model:checked="visualMapEnabled" @change="toggleVisualMap" />
      </a-form-item>
      
      <template v-if="visualMapEnabled">
        <a-form-item label="最小值">
          <a-input-number 
            v-model:value="chartConfig.visualMap.min" 
            :min="0" 
            style="width: 100%"
            @change="updateConfig" 
          />
        </a-form-item>
        
        <a-form-item label="最大值">
          <a-input-number 
            v-model:value="chartConfig.visualMap.max" 
            :min="0" 
            style="width: 100%"
            @change="updateConfig" 
          />
        </a-form-item>
        
        <a-form-item label="最小值颜色">
          <a-input 
            v-model:value="visualMapMinColor" 
            type="color"
            style="width: 100px" 
            @change="updateVisualMapColors" 
          />
        </a-form-item>
        
        <a-form-item label="最大值颜色">
          <a-input 
            v-model:value="visualMapMaxColor" 
            type="color"
            style="width: 100px" 
            @change="updateVisualMapColors" 
          />
        </a-form-item>
      </template>
      
      <!-- 样式设置 -->
      <a-divider orientation="left">样式设置</a-divider>
      
      <a-form-item label="边框宽度">
        <a-input-number 
          v-model:value="chartConfig.series[0].itemStyle.borderWidth" 
          :min="0" 
          :max="5" 
          style="width: 100%"
          @change="updateConfig" 
        />
      </a-form-item>
      
      <a-form-item label="边框颜色">
        <a-input 
          v-model:value="chartConfig.series[0].itemStyle.borderColor" 
          type="color"
          style="width: 100px" 
          @change="updateConfig" 
        />
      </a-form-item>
      
      <a-form-item label="高亮边框宽度">
        <a-input-number 
          v-model:value="chartConfig.series[0].emphasis.itemStyle.borderWidth" 
          :min="0" 
          :max="5" 
          style="width: 100%"
          @change="updateConfig" 
        />
      </a-form-item>
    </template>
  </common-chart-settings>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import CommonChartSettings from './CommonChartSettings.vue';
import type { ChartOptions } from '@/types/chart';

const props = defineProps({
  options: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update']);

// 视觉映射相关
const visualMapEnabled = ref(false);
const visualMapMinColor = ref('#c6e48b');
const visualMapMaxColor = ref('#196127');

// 直接访问config部分的计算属性
const chartConfig = computed({
  get: () => {
    // 确保config对象存在
    if (!props.options.config) {
      return getDefaultConfig();
    }
    return props.options.config;
  },
  set: (newConfig) => {
    const updatedOptions = {
      ...props.options,
      config: newConfig
    };
    emit('update', updatedOptions);
  }
});

// 获取默认配置
const getDefaultConfig = () => {
  return {
    type: 'treemap',
    title: {
      text: '树图',
      show: true
    },
    tooltip: {
      formatter: '{b}: {c}'
    },
    series: [{
      type: 'treemap',
      data: [
        {
          name: '类别1',
          value: 40,
          children: [
            { name: '子项1', value: 10 },
            { name: '子项2', value: 30 }
          ]
        },
        {
          name: '类别2',
          value: 60,
          children: [
            { name: '子项3', value: 25 },
            { name: '子项4', value: 35 }
          ]
        }
      ],
      leafDepth: 1,
      label: {
        show: true
      },
      upperLabel: {
        show: true
      },
      itemStyle: {
        borderColor: '#fff',
        borderWidth: 1
      },
      emphasis: {
        itemStyle: {
          borderColor: '#333',
          borderWidth: 2
        }
      }
    }],
    grid: {
      top: 10,
      right: 10,
      bottom: 10,
      left: 10
    }
  };
};

// 更新配置
const updateOptions = (newOptions: ChartOptions) => {
  emit('update', newOptions);
};

// 更新config
const updateConfig = () => {
  // 触发响应式更新
  chartConfig.value = { ...chartConfig.value };
};

// 切换视觉映射
const toggleVisualMap = (enabled: boolean) => {
  const config = { ...chartConfig.value };
  
  if (enabled) {
    // 如果启用，添加视觉映射配置
    config.visualMap = {
      type: 'continuous',
      min: 0,
      max: 100,
      inRange: {
        color: [visualMapMinColor.value, visualMapMaxColor.value]
      }
    };
  } else {
    // 如果禁用，移除视觉映射配置
    delete config.visualMap;
  }
  
  chartConfig.value = config;
};

// 更新视觉映射颜色
const updateVisualMapColors = () => {
  if (!chartConfig.value.visualMap) return;
  
  const config = { ...chartConfig.value };
  config.visualMap.inRange = {
    color: [visualMapMinColor.value, visualMapMaxColor.value]
  };
  
  chartConfig.value = config;
};
</script>

<style scoped>
.json-preview {
  max-height: 300px;
  overflow: auto;
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}
</style> 