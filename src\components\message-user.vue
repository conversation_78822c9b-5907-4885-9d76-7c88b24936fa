<template>
  <div class="message user-message">
    <div class="message-content">
      {{ content }}
      <slot />
    </div>
    <img :src="profileSrc" class="chat-avatar user-avatar"/>

    <div class="user-message-actions">
      <el-popover placement="top" effect="dark" content="编辑">
        <template #reference>
          <div class="icon" @click="setContentToInput">
            <el-icon><EditPen /></el-icon>
          </div>
        </template>
      </el-popover>
      <el-popover placement="top" effect="dark" content="复制">
        <template #reference>
          <div class="icon" @click="() => onClickCopy(content)">
            <el-icon><CopyOutline /></el-icon>
          </div>
        </template>
      </el-popover>
    </div>
  </div>
</template>

<script setup lang="ts">
import profileSrc from '@/assets/images/profile.png';
import { EditPen } from '@element-plus/icons-vue';
import { CopyOutline } from '@vicons/ionicons5';
import { onClickCopy } from '@/utils';

const props = defineProps({
  content: {
    type: String,
    default: ''
  },
});

const emits = defineEmits([
  'setInputMessage',
]);

const setContentToInput = () => {
  emits('setInputMessage', props.content);
}

</script>