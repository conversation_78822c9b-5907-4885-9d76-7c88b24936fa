import request from '../utils/request';
import type { PromptItem, Scene, GetPromptTemplateVO, GetPrmoptListVO, AddPromptDTO, AddPromptVO, VerifyPromptDTO } from '../types/prompt';
import type { PaginationDTO } from '../types/app';

// API响应类型接口
interface ApiResponse<T> {
  code: number;
  data: T;
  message?: string;
}

export function getScenes(type: string): Promise<ApiResponse<Scene[]>> {
  return request.get(`/prompt/type/targets?prompt_type=${type}`, {}, {}, true);
}

export function getPromptTemplate(promptType: string, target: string): Promise<ApiResponse<GetPromptTemplateVO>> {
  return request.post(`/prompt/template/load?prompt_type=${promptType}&target=${target}`, {
    prompt_type: promptType,
    target,
  }, {}, true);
}

export function getPromptList(params: PaginationDTO): Promise<ApiResponse<GetPrmoptListVO>> {
  return request.post(`/prompt/query_page?page=${params.page}&page_size=${params.page_size}`, {}, {}, true);
}

export function createPrompt(data: AddPromptDTO): Promise<ApiResponse<AddPromptVO>> {
  return request.post('/prompt/add', data, {}, true);
}

export function updatePrompt(data: AddPromptDTO): Promise<ApiResponse<AddPromptVO>> {
  return request.post('/prompt/update', data, {}, true);
}

export function deletePrompt(data: AddPromptDTO): Promise<ApiResponse<null>> {
  return request.post('/prompt/delete', data, {}, true);
} 

export function verifyPrompt(data: VerifyPromptDTO): Promise<ApiResponse<boolean>> {
  return request.post('/prompt/response/verify', data, {}, true);
}

