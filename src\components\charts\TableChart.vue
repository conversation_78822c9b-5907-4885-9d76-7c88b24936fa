<template>
  <BaseChart 
    :chart-id="chartId" 
    :dashboard-id="dashboardId"
    @data-loaded="onDataLoaded"
    @error="onError"
  >
    <template #default="slotProps">
      <div class="table-wrapper">
        <a-table
          :columns="processedOptions.columns || []"
          :data-source="processedOptions.dataSource || []"
          :pagination="processedOptions.pagination"
          :bordered="processedOptions.bordered"
          :size="processedOptions.size || 'default'"
          :style="{
            '--header-bg-color': processedOptions.headerBackground || '#fafafa',
            '--header-text-color': processedOptions.headerTextColor || '#262626',
            '--row-bg-color': processedOptions.rowBackground || '#ffffff',
            '--row-text-color': processedOptions.rowTextColor || '#262626',
            '--font-size': `${processedOptions.fontSize || 14}px`,
            '--header-font-size': `${processedOptions.headerFontSize || 14}px`
          }"
        />
      </div>
    </template>
  </BaseChart>
</template>

<script setup lang="ts">
import { ref, markRaw } from 'vue';
import { Table } from 'ant-design-vue';
import BaseChart from './BaseChart.vue';

// 导入组件
const ATable = Table;

// 定义props
const props = defineProps({
  chartId: {
    type: Number,
    required: true
  },
  dashboardId: {
    type: Number,
    required: true
  }
});

// 处理后的图表配置
const processedOptions = ref<any>({
  columns: [],
  dataSource: [],
  pagination: false,
  bordered: true,
  headerBackground: '#fafafa',
  headerTextColor: '#262626',
  rowBackground: '#ffffff',
  rowTextColor: '#262626',
  fontSize: 14,
  headerFontSize: 14
});

/**
 * 处理表格数据
 */
const processTableData = (rawData: any): any => {
  const { config = {}, data: chartData } = rawData;
  
  // 如果没有数据，返回基础配置
  if (!chartData?.columns || !chartData?.values || chartData.values.length === 0) {
    return {
      ...config,
      columns: [],
      dataSource: [],
      pagination: config.pagination || false,
      bordered: config.bordered !== undefined ? config.bordered : true,
      headerBackground: config.headerBackground || '#fafafa',
      headerTextColor: config.headerTextColor || '#262626',
      rowBackground: config.rowBackground || '#ffffff',
      rowTextColor: config.rowTextColor || '#262626',
      fontSize: config.fontSize || 14,
      headerFontSize: config.headerFontSize || 14
    };
  }

  const { columns, values } = chartData;

  // 处理表格列配置
  const tableColumns = columns.map((col: string, index: number) => ({
    title: col,
    dataIndex: `col${index}`,
    key: `col${index}`
  }));
  
  // 处理表格数据源
  const dataSource = values.map((row: any[], rowIndex: number) => {
    const rowData: Record<string, any> = { key: `row${rowIndex}` };
    
    // 将每一列的值添加到行数据对象中，并进行类型转换
    columns.forEach((col: string, colIndex: number) => {
      const value = row[colIndex];
      // 尝试转换为数字，如果失败则保持原值
      const numValue = Number(value);
      rowData[`col${colIndex}`] = !isNaN(numValue) ? numValue : String(value);
    });
    
    return rowData;
  });
  
  // 返回处理后的表格配置
  return {
    ...config,
    columns: tableColumns,
    dataSource: dataSource,
    pagination: config.pagination || false,
    bordered: config.bordered !== undefined ? config.bordered : true,
    headerBackground: config.headerBackground || '#fafafa',
    headerTextColor: config.headerTextColor || '#262626',
    rowBackground: config.rowBackground || '#ffffff',
    rowTextColor: config.rowTextColor || '#262626',
    fontSize: config.fontSize || 14,
    headerFontSize: config.headerFontSize || 14
  };
};

// 数据加载完成
const onDataLoaded = (rawData: any) => {
  try {
    // 直接处理原始数据
    const processedConfig = processTableData(rawData);
    
    // 使用markRaw避免Vue对复杂对象进行递归响应式处理
    processedOptions.value = markRaw(processedConfig);
  } catch (error) {
    console.error('处理表格数据出错', error);
    processedOptions.value = {};
  }
};

// 数据加载错误
const onError = (error: string) => {
  console.error('表格数据加载错误', error);
};
</script>

<style scoped>
.table-wrapper {
  width: 100%;
  height: 100%;
  overflow: auto;
}

:deep(.ant-table) {
  font-size: var(--font-size);
}

:deep(.ant-table-thead > tr > th) {
  background-color: var(--header-bg-color);
  color: var(--header-text-color);
  font-size: var(--header-font-size);
}

:deep(.ant-table-tbody > tr > td) {
  background-color: var(--row-bg-color);
  color: var(--row-text-color);
}
</style> 