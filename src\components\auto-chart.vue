<template>
  <div>
    <div class="chat-header">
      <div class="left">
        <div class="advice-label">自动推荐</div>
        <a-select
          v-model:value="renderChartType"
          style="width: 200px"
          :options="advices"
          @change="handleRenderChartTypeChange"
        ></a-select>
      </div>
      <DownloadOutlined class="download-btn" @click="handleDownload" />
    </div>
    <div ref="container" style="width: 100%; height: auto;"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick } from 'vue';
import type { PropType } from 'vue';
import { processNilData, sortData } from '@/components/autoChart/charts/util';
import { customCharts } from '@/components/autoChart/charts';
import { defaultAdvicesFilter } from '@/components/autoChart/advisor/utils';
import { customizeAdvisor, getVisAdvices } from '@/components/autoChart/advisor/pipeline';
import type { ChartType, CustomAdvisorConfig, CustomChart, Specification } from '@/components/autoChart/types';
import { DownloadOutlined } from '@ant-design/icons-vue';
import { compact, concat, uniq } from 'lodash-es';
import { CommonZh } from '@/locales/zh/common';
import { Chart } from '@antv/g2';
import { useMessageDynamicStore } from "@/store/message_dynamic";
import {useRoute} from "vue-router";

const props = defineProps({
  data: {
    type: Object as PropType<any>,
    required: true
  },
  chartType: {
    type: Array as PropType<any>,
    required: true,
  },
  messageId: {
    type: String,
    default: ''
  },
});

const route = useRoute();
const getConversationId = () => route.params.conversationId as string;
const chartConfig = ref<any>();

let chart: any = null; // chart 需要声明为 null，且为全局变量
const container = ref();
const advisor = ref<any>();
const advices = ref<any[]>([]);
const renderChartType = ref<ChartType>();
const options = ref<any>();
const showChart = ref<boolean>(false);

/** 将 AVA 得到的图表推荐结果和模型的合并 */
const getMergedAdvices = (data: any, avaAdvices: any[]) => {
    if (!advisor.value) return [];
    const filteredAdvices = defaultAdvicesFilter({
      advices: avaAdvices,
    });
    const allChartTypes = uniq(
      compact(
        concat(
          props.chartType,
          avaAdvices.map(item => item.type),
        ),
      ),
    );
    const allAdvices = allChartTypes
      .map(chartTypeItem => {
        const avaAdvice = filteredAdvices.find(item => item.type === chartTypeItem);
        // 如果在 AVA 推荐列表中，直接采用推荐列表中的结果
        if (avaAdvice) {
          return avaAdvice;
        }
        // 如果不在，则单独为其生成图表 spec
        const dataAnalyzerOutput = advisor.value.dataAnalyzer.execute({ data });
        if ('data' in dataAnalyzerOutput) {
          const specGeneratorOutput = advisor.value.specGenerator.execute({
            data: dataAnalyzerOutput.data,
            dataProps: dataAnalyzerOutput.dataProps,
            chartTypeRecommendations: [{ chartType: chartTypeItem, score: 1 }],
          });
          if ('advices' in specGeneratorOutput) return specGeneratorOutput.advices?.[0];
        }
      })
      .filter(advice => advice?.spec) as any[];
    return allAdvices;
  };

const renderChart = (option: any) => {
  // 销毁旧的 chart 实例
  if (chart) {
    chart.destroy();
    chart = null;
  }
  // 如果 container.value 不存在，直接返回
  if (!container.value) return;

  console.log('renderChart_option', option);

  chart = new Chart({
    container: container.value,
    autoFit: option.autoFit,
    theme: option.theme,
    height: option.height,
  });

  // 直接用 options.value 作为 G2Spec 配置
  chart.options(option);
  chart.render();

  // const dataChart = [
  //   { genre: 'Sports', sold: 275 },
  //   { genre: 'Strategy', sold: 115 },
  //   { genre: 'Action', sold: 120 },
  //   { genre: 'Shooter', sold: 350 },
  //   { genre: 'Other', sold: 150 },
  // ];

  // console.log(option)
  // console.log(option.data)
  // console.log(option.encode)
  // chart
  //   .interval()
  //   .data(option.data)
  //   .encode('x', 'genre')
  //   .encode('y', 'sold')
  //   .encode('key', 'genre')
  //   .animate('update', { duration: 300 });

  // chart.render();
  // console.log(chart);
};

const handleData = (renderChartTypeInput?: string) => {
  const dataTmp = processNilData(props.data);
  const input_charts: CustomChart[] = customCharts;
  const advisorConfig: CustomAdvisorConfig = {
    charts: input_charts,
    scopeOfCharts: {
      // 排除面积图
      // exclude: ['area_chart', 'stacked_area_chart', 'percent_stacked_area_chart', 'donut_chart'],
      exclude: ['stacked_area_chart', 'percent_stacked_area_chart', 'donut_chart'],   //允许生成area_chart
    },
    ruleConfig: undefined,
  };
  advisor.value = customizeAdvisor(advisorConfig);
  const avaAdvices = getVisAdvices({
    data: dataTmp,
    myChartAdvisor: advisor.value,
  });
  const allAdvices = getMergedAdvices(dataTmp, avaAdvices);
  advices.value = allAdvices;
  advices.value.forEach((item: any) => {
    item.label = CommonZh[item.type]; 
    item.value = item.type; 
  })

  renderChartType.value = renderChartTypeInput ?? allAdvices[0]?.type as ChartType;
  emit('chartTypeChange', renderChartType.value);

  if (advices.value?.length > 0) {
    const chartTypeInput = renderChartType.value ?? advices.value[0].type;
    const spec: Specification = advices.value?.find((item: any) => item.type === chartTypeInput)?.spec ?? undefined;
    if (spec) {
      console.log('spec', spec);
      if (spec.data && ['line_chart', 'step_line_chart'].includes(chartTypeInput)) {
        // 处理 ava 内置折线图的排序问题
        const dataAnalyzerOutput = advisor.value?.dataAnalyzer.execute({ data: dataTmp });
        if (dataAnalyzerOutput && 'dataProps' in dataAnalyzerOutput) {
          spec.data = sortData({
            data: spec.data,
            xField: dataAnalyzerOutput.dataProps?.find((field: any) => field.recommendation === 'date'),
            chartType: chartTypeInput,
          });
        }
      }
      if (chartTypeInput === 'pie_chart' && spec?.encode?.color) {
        // 补充饼图的 tooltip title 展示
        spec.tooltip = { title: { field: spec.encode.color } };
      }
      options.value = {
        ...spec,
        autoFit: true,
        theme: 'light',
        height: 300,
      }

      // 存储图表配置
      const conversationId = getConversationId();
      const messageStore = useMessageDynamicStore.getStore(conversationId);
      const key = `${conversationId}_${props.messageId}`;


      if (['multi_measure_column_chart', 'multi_measure_line_chart', 'multi_line_chart'].includes(chartTypeInput)){
        chartConfig.value = spec.children[0].encode;
        console.log('11111111', chartConfig.value);
      }else {
        chartConfig.value = spec.encode;
        console.log('22222222', chartConfig.value)
      }

      messageStore.setChartConfig(key, chartConfig.value);



      // 用 nextTick 保证 DOM 已经更新
      nextTick(() => {
        renderChart(options.value);
      });
    }
  } else {
    // showChart.value = false;
  }
}

const emit = defineEmits(['chartTypeChange']);

const handleRenderChartTypeChange = (e: string) => {
  handleData(e);
  // 更新store中的值
  emit('chartTypeChange', e);
}

const handleDownload = () => {
  if (container.value) {
    // 通过 DOM 查询获取原生 canvas
    const canvas = container.value.querySelector('canvas');
    if (canvas && typeof canvas.toDataURL === 'function') {
      const dataURL = canvas.toDataURL('image/png');
      const link = document.createElement('a');
      link.href = dataURL;
      link.download = CommonZh[renderChartType.value as any] || 'chart.png';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      alert('未找到原生 Canvas，或当前渲染模式不支持导出图片。');
    }
  }
}

watch(() => props.data, 
  (newVal) => {
    if (newVal) {
      handleData();
    } else {
      // showChart.value = false;
    }
  },{
    deep: true,
    immediate: true,
  }
)

</script>

<style scoped lang="scss">
  .chat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      display: flex;
      align-items: center;
    }
    .advice-label {
      margin-right: 10px;
    }
    :deep(.ant-select-selection-item) {
      text-align: left !important;
    }
    .download-btn {
      font-size: 20px;
      cursor: pointer;
    }
  }
</style>