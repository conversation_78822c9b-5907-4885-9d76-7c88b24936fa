import legendOptions from '../e-charts/legend';
import tooltipOptions from '../e-charts/tooltip';

const scatterDefaultOptions = {
  dataType: 'static',
  data: {
    columns: ['X值', 'Y值'],
    values: [
      [10.0, 8.04],
      [8.0, 6.95],
      [13.0, 7.58],
      [9.0, 8.81],
      [11.0, 8.33],
      [14.0, 9.96],
      [6.0, 7.24],
      [4.0, 4.26],
      [12.0, 10.84],
      [7.0, 4.82],
      [5.0, 5.68]
    ]
  },
  config: {
    type: 'scatter',
    tooltip: tooltipOptions,
    legend: legendOptions,
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        type: 'scatter',
        symbolSize: 20,
        data: []
      }
    ]
  },
  dataMapping: {
    scatter: {
      xField: 'X值',
      yField: 'Y值'
    }
  }
};
export default scatterDefaultOptions; 