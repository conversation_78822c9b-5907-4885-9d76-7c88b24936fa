<template>
  <BaseChart 
    :chart-id="chartId" 
    :dashboard-id="dashboardId"
    @data-loaded="onDataLoaded"
    @error="onError"
  >
    <template #default="slotProps">
      <div class="chart-wrapper">
        <v-chart :option="processedOptions" autoresize />
      </div>
    </template>
  </BaseChart>
</template>

<script setup lang="ts">
import { ref, markRaw } from 'vue';
import VChart from 'vue-echarts';
import BaseChart from './BaseChart.vue';

// 定义props
const props = defineProps({
  chartId: {
    type: Number,
    required: true
  },
  dashboardId: {
    type: Number,
    required: true
  }
});

// 处理后的图表ECharts配置
const processedOptions = ref<any>({});

/**
 * 处理热力图数据
 */
const processHeatmapData = (rawData: any): any => {
  const { config = {}, data: chartData, dataMapping } = rawData;
  
  // 如果没有数据，返回基础配置
  if (!chartData?.columns || !chartData?.values || chartData.values.length === 0) {
    return config;
  }

  const { columns, values } = chartData;
  
  // 使用映射逻辑或默认逻辑
  let xField = 0; // 默认第一列为X轴
  let yField = 1; // 默认第二列为Y轴
  let valueField = 2; // 默认第三列为值
  
  // 如果提供了映射配置，使用映射配置
  if (dataMapping) {
    // 查找xField对应的列索引
    if (dataMapping.xField) {
      const xFieldIndex = columns.findIndex((col: string) => col === dataMapping.xField);
      if (xFieldIndex !== -1) {
        xField = xFieldIndex;
      }
    }
    
    // 查找yField对应的列索引
    if (dataMapping.yField) {
      const yFieldIndex = columns.findIndex((col: string) => col === dataMapping.yField);
      if (yFieldIndex !== -1) {
        yField = yFieldIndex;
      }
    }
    
    // 查找valueField对应的列索引
    if (dataMapping.valueField) {
      const valueFieldIndex = columns.findIndex((col: string) => col === dataMapping.valueField);
      if (valueFieldIndex !== -1) {
        valueField = valueFieldIndex;
      }
    }
  }
  
  // 提取唯一的X轴和Y轴值
  const xAxisData = Array.from(new Set(values.map((row: any[]) => String(row[xField]))));
  const yAxisData = Array.from(new Set(values.map((row: any[]) => String(row[yField]))));
  
  // 构建热力图数据
  const data = values.map((row: any[]) => [
    String(row[xField]), // x值
    String(row[yField]), // y值
    Number(row[valueField]) || 0 // 热力值
  ]);
  
  // 计算值的范围
  const valueRange = data.reduce((range: [number, number], item: any[]) => {
    const value = item[2];
    return [Math.min(range[0], value), Math.max(range[1], value)];
  }, [Infinity, -Infinity]);
  
  // 构建返回配置
  return {
    ...config,
    title: {
      show: false
    },
    tooltip: {
      position: 'top',
      formatter: (params: any) => {
        const value = params.data[2];
        return `${params.data[0]}, ${params.data[1]}<br/>值: ${value}`;
      }
    },
    grid: {
      top: '10%',
      left: '3%',
      right: '10%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      splitArea: {
        show: true
      }
    },
    yAxis: {
      type: 'category',
      data: yAxisData,
      splitArea: {
        show: true
      }
    },
    visualMap: {
      min: valueRange[0],
      max: valueRange[1],
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '0%'
    },
    series: [{
      type: 'heatmap',
      data: data,
      label: {
        show: false
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  };
};

// 数据加载完成
const onDataLoaded = (rawData: any) => {
  try {
    // 直接处理原始数据
    const processedConfig = processHeatmapData(rawData);
    
    // 使用markRaw避免Vue对复杂对象进行递归响应式处理
    processedOptions.value = markRaw(processedConfig);
  } catch (error) {
    console.error('处理热力图数据出错', error);
    processedOptions.value = {};
  }
};

// 数据加载错误
const onError = (error: string) => {
  console.error('图表数据加载错误', error);
};
</script>

<style scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;
}
</style> 