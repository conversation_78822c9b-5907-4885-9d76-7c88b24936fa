import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { viteMockServe } from 'vite-plugin-mock'
import { fileURLToPath, URL } from 'node:url'

// https://vite.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd())
  
  return {
    plugins: [
      vue(),
      viteMockServe({
        mockPath: 'mock',
        enable: true,
        watchFiles: true
      })
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    // 开发服务器配置
    server: {
      host: true, //允许外部访问
      port: 3000,
      open: true,
      proxy: {
        // 代理配置对非mock的接口生效
        '/api': {
          // target: env.VITE_API_BASE_URL || 'http://localhost:8080',
          target: 'https://manasdatagpttest.manascloud.com/api',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        },
        '/root-api': {
          target: 'https://manasdatagpttest.manascloud.com/api',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/root-api/, '')
        }
      }
    },
    // 构建选项
    build: {
      // 是否生成 source map
      sourcemap: mode !== 'production',
      // 输出目录
      outDir: 'dist',
      // 静态资源目录
      assetsDir: 'assets'
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler',
          additionalData: '@use "@/assets/variable.scss";',
        },
      },
    }
  }
})
