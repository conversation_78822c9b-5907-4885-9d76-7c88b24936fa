<template>
  <common-chart-settings
    :options="props.options"
    chart-type="pie"
    @update="updateOptions"
  >
    <!-- 样式配置 -->
    <template #style-settings>
      <!-- 饼图特有样式 -->
      <a-collapse-panel key="pie" header="饼图样式">
        <a-form-item label="饼图半径">
          <a-input-number 
            v-model:value="pieRadius" 
            :min="30" 
            :max="100"
            :formatter="(value: number) => `${value}%`"
            :parser="(value: string) => parseFloat(value.replace('%', ''))"
            @change="updatePieRadius"
          />
        </a-form-item>
        
        <a-form-item label="是否为环形图">
          <a-switch v-model:checked="isDonut" @change="updatePieRadius" />
        </a-form-item>
        
        <a-form-item label="内环半径" v-if="isDonut">
          <a-input-number 
            v-model:value="innerRadius" 
            :min="0" 
            :max="pieRadius - 10"
            :formatter="(value: number) => `${value}%`"
            :parser="(value: string) => parseFloat(value.replace('%', ''))"
            @change="updatePieRadius"
          />
        </a-form-item>
        
        <a-form-item label="饼图中心位置">
          <a-input-number
            v-model:value="centerX"
            :min="0"
            :max="100"
            :formatter="(value: number) => `${value}%`"
            :parser="(value: string) => parseFloat(value.replace('%', ''))"
            @change="updatePieCenter"
            style="width: 45%"
          /> 
          <span style="display: inline-block; width: 10%; text-align: center;">-</span>
          <a-input-number
            v-model:value="centerY"
            :min="0"
            :max="100"
            :formatter="(value: number) => `${value}%`"
            :parser="(value: string) => parseFloat(value.replace('%', ''))"
            @change="updatePieCenter"
            style="width: 45%"
          />
        </a-form-item>
        
        <a-form-item label="标签位置">
          <a-select v-model:value="labelPosition" @change="updateLabelPosition">
            <a-select-option value="outside">外部</a-select-option>
            <a-select-option value="inside">内部</a-select-option>
            <a-select-option value="center">中心</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="显示百分比">
          <a-switch v-model:checked="showPercentage" @change="updateLabelFormat" />
        </a-form-item>
      </a-collapse-panel>
      
      <!-- 数据项手动编辑 -->
      <a-collapse-panel key="data" header="数据项编辑">
        <div v-for="(item, index) in chartConfig.series[0].data" :key="index" class="data-item">
          <a-row :gutter="[16, 0]">
            <a-col :span="10">
              <a-form-item :label="`名称 ${index + 1}`">
                <a-input v-model:value="item.name" placeholder="请输入名称" />
              </a-form-item>
            </a-col>
            <a-col :span="10">
              <a-form-item :label="`数值 ${index + 1}`">
                <a-input-number 
                  v-model:value="item.value" 
                  style="width: 100%"
                />
              </a-form-item>
            </a-col>
            <a-col :span="4">
              <a-form-item label=" " class="delete-btn-container">
                <a-button 
                  type="primary" 
                  danger 
                  @click="removeDataItem(index)"
                  shape="circle"
                >
                  <delete-outlined />
                </a-button>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-form-item :label="`自定义颜色 ${index + 1}`">
            <a-input 
              :value="getItemColor(index)" 
              placeholder="如: #1890ff" 
              :addon-before="'颜色'"
              @change="(e: Event) => updateItemColor(index, (e.target as HTMLInputElement).value)"
            />
          </a-form-item>
        </div>
        
        <a-button type="dashed" block @click="addDataItem">
          <plus-outlined /> 添加数据项
        </a-button>
      </a-collapse-panel>
    </template>
  </common-chart-settings>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import CommonChartSettings from './CommonChartSettings.vue';
import type { ChartOptions } from '../../types/chart';

const props = defineProps({
  options: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update']);

// 饼图样式控制
const pieRadius = ref(75);
const isDonut = ref(false);
const innerRadius = ref(40);
const centerX = ref(50);
const centerY = ref(50);
const labelPosition = ref('outside');
const showPercentage = ref(true);

// 默认饼图配置
const defaultPieConfig = {
  type: 'pie',
  title: {
    text: '',
    show: false
  },
  tooltip: {
    trigger: 'item'
  },
  legend: {
    show: true,
    orient: 'horizontal',
    left: 'right'
  },
  series: [
    {
      name: '饼图',
      type: 'pie',
      radius: '75%',
      center: ['50%', '50%'],
      data: [
        { value: 335, name: '类别A' },
        { value: 310, name: '类别B' },
        { value: 234, name: '类别C' },
        { value: 135, name: '类别D' },
        { value: 154, name: '类别E' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: {
        show: true,
        position: 'outside',
        formatter: '{b}: {d}%'
      }
    }
  ]
};

// 直接访问config部分的计算属性
const chartConfig = computed({
  get: () => {
    return props.options.config || defaultPieConfig;
  },
  set: (newConfig) => {
    const updatedOptions = {
      ...props.options,
      config: newConfig
    };
    emit('update', updatedOptions);
  }
});

// 更新配置
const updateOptions = (newOptions: ChartOptions) => {
  emit('update', newOptions);
};

// 初始化组件
onMounted(() => {
  // 初始化饼图样式控制变量
  if (chartConfig.value.series?.[0]) {
    // 设置半径
    const radius = chartConfig.value.series[0].radius;
    if (Array.isArray(radius)) {
      // 环形图
      isDonut.value = true;
      innerRadius.value = parseInt(radius[0].toString().replace('%', ''), 10);
      pieRadius.value = parseInt(radius[1].toString().replace('%', ''), 10);
    } else if (typeof radius === 'string') {
      // 普通饼图
      pieRadius.value = parseInt(radius.replace('%', ''), 10);
    }
    
    // 设置中心位置
    const center = chartConfig.value.series[0].center;
    if (Array.isArray(center) && center.length === 2) {
      centerX.value = parseInt(center[0].toString().replace('%', ''), 10);
      centerY.value = parseInt(center[1].toString().replace('%', ''), 10);
    }
    
    // 设置标签位置
    if (chartConfig.value.series[0].label) {
      labelPosition.value = chartConfig.value.series[0].label.position || 'outside';
      
      // 检查是否显示百分比
      if (chartConfig.value.series[0].label.formatter) {
        showPercentage.value = chartConfig.value.series[0].label.formatter.toString().includes('{d}%');
      }
    }
  }
});

// 监听props变化，更新本地控制变量
watch(() => props.options, (newOptions) => {
  if (newOptions?.config?.series?.[0]) {
    // 更新饼图样式控制变量
    const radius = newOptions.config.series[0].radius;
    if (Array.isArray(radius)) {
      // 环形图
      isDonut.value = true;
      innerRadius.value = parseInt(radius[0].toString().replace('%', ''), 10);
      pieRadius.value = parseInt(radius[1].toString().replace('%', ''), 10);
    } else if (typeof radius === 'string') {
      // 普通饼图
      isDonut.value = false;
      pieRadius.value = parseInt(radius.replace('%', ''), 10);
    }
    
    // 更新中心位置
    const center = newOptions.config.series[0].center;
    if (Array.isArray(center) && center.length === 2) {
      centerX.value = parseInt(center[0].toString().replace('%', ''), 10);
      centerY.value = parseInt(center[1].toString().replace('%', ''), 10);
    }
    
    // 更新标签位置
    if (newOptions.config.series[0].label) {
      labelPosition.value = newOptions.config.series[0].label.position || 'outside';
      
      // 检查是否显示百分比
      if (newOptions.config.series[0].label.formatter) {
        showPercentage.value = newOptions.config.series[0].label.formatter.toString().includes('{d}%');
      }
    }
  }
}, { deep: true });

// 更新饼图半径
const updatePieRadius = () => {
  const config = { ...chartConfig.value };
  
  if (isDonut.value) {
    config.series[0].radius = [
      `${innerRadius.value}%`,
      `${pieRadius.value}%`
    ];
  } else {
    config.series[0].radius = `${pieRadius.value}%`;
  }
  
  chartConfig.value = config;
};

// 更新饼图中心位置
const updatePieCenter = () => {
  const config = { ...chartConfig.value };
  config.series[0].center = [
    `${centerX.value}%`,
    `${centerY.value}%`
  ];
  
  chartConfig.value = config;
};

// 更新标签位置
const updateLabelPosition = () => {
  const config = { ...chartConfig.value };
  
  if (!config.series[0].label) {
    config.series[0].label = { show: true, position: labelPosition.value };
  } else {
    config.series[0].label.position = labelPosition.value;
  }
  
  chartConfig.value = config;
  
  // 更新标签格式
  updateLabelFormat();
};

// 更新标签格式
const updateLabelFormat = () => {
  const config = { ...chartConfig.value };
  
  if (!config.series[0].label) {
    config.series[0].label = { 
      show: true, 
      position: labelPosition.value,
      formatter: showPercentage.value ? '{b}: {d}%' : '{b}'
    };
  } else {
    config.series[0].label.formatter = showPercentage.value ? '{b}: {d}%' : '{b}';
  }
  
  chartConfig.value = config;
};

// 获取数据项颜色
const getItemColor = (index: number) => {
  const item = chartConfig.value.series[0].data[index];
  if (item && item.itemStyle && item.itemStyle.color) {
    return item.itemStyle.color;
  }
  
  if (chartConfig.value.color && chartConfig.value.color[index]) {
    return chartConfig.value.color[index];
  }
  
  return '';
};

// 更新数据项颜色
const updateItemColor = (index: number, color: string) => {
  const config = { ...chartConfig.value };
  const item = config.series[0].data[index];
  
  if (!item.itemStyle) {
    item.itemStyle = { color };
  } else {
    item.itemStyle.color = color;
  }
  
  chartConfig.value = config;
};

// 添加数据项
const addDataItem = () => {
  const config = { ...chartConfig.value };
  const data = config.series[0].data || [];
  
  data.push({
    name: `类别${data.length + 1}`,
    value: 100
  });
  
  // 更新图例
  if (config.legend && config.legend.data) {
    config.legend.data = data.map((item: { name: string; value: number }) => item.name);
  }
  
  chartConfig.value = config;
};

// 移除数据项
const removeDataItem = (index: number) => {
  const config = { ...chartConfig.value };
  config.series[0].data.splice(index, 1);
  
  // 更新图例
  if (config.legend && config.legend.data) {
    config.legend.data = config.series[0].data.map((item: { name: string; value: number }) => item.name);
  }
  
  chartConfig.value = config;
};
</script>

<style scoped>
.data-item {
  padding: 10px;
  margin-bottom: 12px;
  border: 1px dashed #ddd;
  border-radius: 4px;
}

.delete-btn-container {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  height: 100%;
}
</style> 