<template>
  <a-modal
    v-model:visible="localVisible"
    :title="isEdit ? '编辑数据源' : '添加数据源'"
    @ok="handleModalOk"
    @cancel="handleModalCancel"
    width="800px"
    :confirmLoading="modalLoading"
  >
    <a-spin :spinning="dbTypesLoading" tip="加载中...">
      <a-form v-if="!dbTypesLoading" :model="formState" :rules="rules" ref="formRef" layout="vertical">
        <a-form-item label="数据源类型" name="type">
          <database-selector
            v-model="formState.type"
            :databases="dbTypeList"
            @change="handleTypeSelect"
          />
        </a-form-item>
        
        <!-- 动态参数表单 -->
        <template v-for="param in selectedParams" :key="param.param_name">
          <a-form-item 
            :label="param.label" 
            :name="['params', param.param_name]"
            :rules="[{ required: param.required, message: `请输入${param.label}`, trigger: 'blur' }]"
          >
            <template #label>
              <span>
                {{ param.label }}
                <a-tooltip v-if="param.description" placement="top">
                  <template #title>{{ param.description || '' }}</template>
                  <question-circle-outlined style="margin-left: 4px" />
                </a-tooltip>
              </span>
            </template>

            <!-- 根据参数类型渲染不同的输入控件 -->
            <template v-if="param.valid_values && param.valid_values.length">
              <a-select 
                v-model:value="formState.params[param.param_name]" 
                :placeholder="`选择${param.label}`"
              >
                <a-select-option 
                  v-for="option in param.valid_values" 
                  :key="option" 
                  :value="option"
                >
                  {{ option }}
                </a-select-option>
              </a-select>
            </template>
            <template v-else-if="param.param_type === 'boolean'">
              <a-checkbox v-model:checked="formState.params[param.param_name]">
              </a-checkbox>
            </template>
            <template v-else-if="param.param_type === 'integer' || param.param_type === 'number'">
              <a-input-number 
                v-model:value="formState.params[param.param_name]" 
                :placeholder="`请输入${param.label}`"
                style="width: 100%"
              />
            </template>
            <template v-else-if="param.param_type === 'string' && param.ext_metadata && param.ext_metadata.tags === 'privacy'">
              <a-input-password 
                v-model:value="formState.params[param.param_name]" 
                :placeholder="`请输入${param.label}`" 
              />
            </template>
            <template v-else>
              <a-input 
                v-model:value="formState.params[param.param_name]" 
                :placeholder="`请输入${param.label}`" 
              />
            </template>
          </a-form-item>
        </template>
        
        <a-form-item label="描述" name="description">
          <a-textarea v-model:value="formState.description" :rows="3" placeholder="请输入描述信息" />
        </a-form-item>

        <a-form-item :wrapper-col="{ span: 24 }" style="margin-top: 16px;">
          <div class="connection-test-result" v-if="connectionTested">
            <div class="connection-success" v-if="connectionSuccess">
              <check-circle-outlined /> 连接测试成功
            </div>
            <div class="connection-failed" v-else>
              <close-circle-outlined /> 连接测试失败: {{ connectionErrorMsg }}
            </div>
          </div>
          <div class="form-actions">
            <a-button type="primary" :loading="testingConnection" @click="testConnection">
              测试连接
            </a-button>
          </div>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import { QuestionCircleOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons-vue';
import { getDatabaseTypes, createDataSource, updateDataSource, testDataSourceConnection, refreshDataSource } from '../../api/database';
import type { DatabaseItem, DatabaseTypeItem, DatabaseTypeParam } from '../../types/database';
import DatabaseSelector from '../../components/selector/DatabaseSelector.vue';

const props = defineProps<{
  visible: boolean;
  isEdit?: boolean;
  editData?: DatabaseItem | null;
}>();

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'success'): void;
}>();

// 创建本地可见性状态
const localVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 表单相关
const formRef = ref();
const modalLoading = ref(false);
const dbTypesLoading = ref(false);
const testingConnection = ref(false);
const connectionTested = ref(false);
const connectionSuccess = ref(false);
const connectionErrorMsg = ref('');

// 数据库类型数据
const dbTypes = ref<DatabaseTypeItem[]>([]);
const dbTypeList = ref<DatabaseTypeItem[]>([]);
const selectedParams = ref<DatabaseTypeParam[]>([]);

// 表单状态
const formState = reactive({
  type: '',
  params: {} as Record<string, any>,
  description: ''
});

// 表单验证规则
const rules = {
  type: [{ required: true, message: '请选择数据源类型', trigger: 'blur' }]
};

// 处理数据库类型选择
const handleTypeSelect = (dbType: DatabaseTypeItem) => {
  // 重置参数
  formState.params = {};
  // 更新表单值
  formState.type = dbType.name;
  
  // 重置连接测试状态
  connectionTested.value = false;
  connectionSuccess.value = false;
  connectionErrorMsg.value = '';
  
  // 设置选中的数据库类型的参数
  selectedParams.value = dbType.parameters;
  
  // 设置默认值
  selectedParams.value.forEach(param => {
    if (param.default_value !== null && param.default_value !== undefined) {
      formState.params[param.param_name] = param.default_value;
    } else {
      formState.params[param.param_name] = '';
    }
  });

  formRef.value.validate();
};

// 取消模态框
const handleModalCancel = () => {
  localVisible.value = false;
  resetForm();
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  
  formState.type = '';
  formState.params = {};
  formState.description = '';
  selectedParams.value = [];
  connectionTested.value = false;
  connectionSuccess.value = false;
  connectionErrorMsg.value = '';
};

// 初始化编辑表单数据
const initEditFormData = () => {
  if (props.editData && dbTypes.value.length > 0) {
    const dbType = dbTypes.value.find(type => type.name === props.editData?.type);
    if (dbType) {
      // 设置数据库类型参数
      selectedParams.value = dbType.parameters;
      
      // 设置表单数据
      formState.type = props.editData.type;
      formState.description = props.editData.description || '';
      
      // 先重置参数为默认值
      formState.params = {};
      selectedParams.value.forEach(param => {
        if (param.default_value !== null && param.default_value !== undefined) {
          formState.params[param.param_name] = param.default_value;
        } else {
          formState.params[param.param_name] = '';
        }
      });
      
      // 再填充编辑数据的参数
      if (props.editData.params) {
        Object.keys(props.editData.params).forEach(key => {
          // 只有在参数存在于选中的参数列表中才设置
          if (selectedParams.value.find(param => param.param_name === key)) {
            formState.params[key] = (props.editData?.params as any)[key];
          }
        });
      }
    }
  }
};

// 获取数据库类型数据
const fetchDatabaseTypes = async () => {
  try {
    dbTypesLoading.value = true;
    
    const response = await getDatabaseTypes();
    
    if (response.success && response.data) {
      dbTypes.value = response.data.types;
      dbTypeList.value = response.data.types;
      
      // 如果是编辑模式，加载表单数据
      if (props.isEdit && props.editData) {
        initEditFormData();
      }
    } else {
      message.error('获取数据库类型数据失败');
      handleModalCancel(); // 获取数据失败时关闭弹窗
    }
  } catch (error) {
    console.error('获取数据库类型数据出错:', error);
    message.error('获取数据库类型数据出错');
    handleModalCancel(); // 发生错误时关闭弹窗
  } finally {
    dbTypesLoading.value = false;
  }
};

// 测试数据库连接
const testConnection = async () => {
  try {
    // 验证表单
    const validateResult = await formRef.value.validate().catch((err: any) => {
      console.error('表单验证失败:', err);
      return false;
    });
    
    if (!validateResult) {
      message.warning('请检查表单输入');
      return;
    }
    
    if (!formState.type) {
      message.warning('请先选择数据源类型');
      return;
    }
    
    // 开始测试连接
    testingConnection.value = true;
    connectionTested.value = false;
    message.loading('正在测试连接...', 0);
    
    const requestData = {
      type: formState.type,
      params: formState.params,
      description: formState.description
    };
    
    const response = await testDataSourceConnection(requestData);
    
    // 关闭加载提示
    message.destroy();
    
    if (response.success) {
      connectionTested.value = true;
      connectionSuccess.value = true;
      connectionErrorMsg.value = '';
      message.success('连接测试成功');
    } else {
      connectionTested.value = true;
      connectionSuccess.value = false;
      connectionErrorMsg.value = response.err_msg || '未知错误';
      message.destroy();
      message.error(`连接测试失败: ${connectionErrorMsg.value}`);
    }
  } catch (error: any) {
    console.error('测试连接出错:', error);
    message.destroy();
    connectionTested.value = true;
    connectionSuccess.value = false;
    connectionErrorMsg.value = error || '测试连接错误：未知错误';
    message.error(`连接失败: ${connectionErrorMsg.value}`);
  } finally {
    testingConnection.value = false;
  }
};

// 处理模态框确认
const handleModalOk = async () => {
  try {
    // 表单验证
    try {
      await formRef.value.validate();
    } catch (error) {
      message.warning('请检查表单输入');
      return;
    }
    
    // 无论之前是否测试过连接，都强制执行一次连接测试
    modalLoading.value = true;
    message.loading('正在测试连接...', 0);
    
    // 构建请求数据
    const requestData = {
      type: formState.type,
      params: formState.params,
      description: formState.description
    };
    
    // 执行连接测试
    const testResponse = await testDataSourceConnection(requestData);
    
    // 连接测试失败，不继续保存
    if (!testResponse.success) {
      message.destroy();
      connectionTested.value = true;
      connectionSuccess.value = false;
      connectionErrorMsg.value = testResponse.err_msg || '未知错误';
      message.error(`连接测试失败: ${connectionErrorMsg.value}`);
      modalLoading.value = false;
      return;
    }
    
    // 连接测试成功，继续保存
    message.destroy();
    message.loading('连接测试通过，正在保存数据...', 0);
    
    try {
      let dataSourceId;

      // 根据是否编辑调用不同API
      if (props.isEdit && props.editData) {
        // 调用更新接口
        await updateDataSource(props.editData.id, requestData);
        message.destroy();
        message.success('数据源更新成功');
        dataSourceId = props.editData.id;
      } else {
        // 调用创建接口
        const res = await createDataSource(requestData);
        message.destroy();
        message.success('数据源添加成功');
        dataSourceId = res?.data?.id;
      }

      refreshDataSource(dataSourceId);

      localVisible.value = false;
      resetForm();
      emit('success');
    } catch (error: any) {
      console.error('保存数据源出错:', error);
      message.destroy();
      message.error(`保存数据源失败: ${error || '未知错误'}`);
    }
  } catch (error) {
    console.log('操作添加', error);
    message.destroy();
    message.error(`操作失败: ${error}` || '操作失败: 未知错误');
  } finally {
    modalLoading.value = false;
    // message.destroy();
  }
};

// 监听编辑数据变化
watch(() => props.editData, (newValue) => {
  if (newValue) {
    // 如果已加载数据库类型，则初始化表单数据
    if (dbTypes.value.length > 0) {
      initEditFormData();
    }
    
    // 对于编辑模式，需要重新测试连接
    connectionTested.value = false;
    connectionSuccess.value = false;
  }
}, { immediate: true });

// 监听数据库类型列表变化
watch(() => dbTypes.value, (newDbTypes) => {
  if (newDbTypes.length > 0 && props.isEdit && props.editData) {
    initEditFormData();
  }
});

// 监听 visible 变化
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    resetForm();
    fetchDatabaseTypes();
  }
});
</script>

<style scoped>
.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.connection-test-result {
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.connection-success {
  color: #52c41a;
  display: flex;
  align-items: center;
  gap: 8px;
}

.connection-failed {
  color: #f5222d;
  display: flex;
  align-items: center;
  gap: 8px;
}
</style> 