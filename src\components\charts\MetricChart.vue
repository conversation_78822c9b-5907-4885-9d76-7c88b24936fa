<template>
  <BaseChart 
    :chart-id="chartId" 
    :dashboard-id="dashboardId"
    @data-loaded="onDataLoaded"
    @error="onError"
  >
    <template #default="slotProps">
      <div 
        class="metric-chart-wrapper"
        :style="{
          backgroundColor: processedOptions.backgroundColor || '#FFFFFF',
          borderRadius: `${processedOptions.borderRadius || 8}px`
        }"
        v-if="showChat"
      >
        <div 
          v-if="processedOptions.title && processedOptions.title.text" 
          class="metric-title"
          :style="{
            fontSize: `${processedOptions.titleSize || 24}px`,
            color: processedOptions.titleColor || '#666666'
          }"
        >
          {{ processedOptions.title.text }}
        </div>
        
        <div 
          class="metric-value"
          :style="{
            fontSize: `${processedOptions.valueSize || 60}px`,
            color: processedOptions.valueColor || '#333333',
            marginTop: (processedOptions.title && processedOptions.title.text) ? '10px' : '0'
          }"
        >
          {{ formattedValue }}
        </div>
      </div>
    </template>
  </BaseChart>
</template>

<script setup lang="ts">
import { ref, computed, markRaw } from 'vue';
import BaseChart from './BaseChart.vue';

// 定义props
const props = defineProps({
  chartId: {
    type: Number,
    required: true
  },
  dashboardId: {
    type: Number,
    required: true
  }
});

// 处理后的图表配置
const processedOptions = ref<any>({
  title: { text: '' },
  value: 0,
  valueColor: '#333333',
  valueSize: 60,
  titleColor: '#666666',
  titleSize: 24,
  backgroundColor: '#FFFFFF',
  borderRadius: 8,
  valueFormatter: 'none'
});
const showChat = ref<boolean>(false);

// 格式化数字
const formatNumber = (value: number, formatter: string): string => {
  if (formatter === 'thousands') {
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  } else if (formatter === 'percentage') {
    return `${value}%`;
  } else if (formatter === 'currency') {
    return `¥${value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`;
  } else if (formatter === 'decimal') {
    return value.toFixed(2);
  } else {
    return value.toString();
  }
};

// 格式化后的值
const formattedValue = computed(() => {
  return formatNumber(
    processedOptions.value.value || 0, 
    processedOptions.value.valueFormatter || 'none'
  );
});

/**
 * 处理指标卡数据
 */
const processMetricData = (rawData: any): any => {
  const { config = {}, data: chartData, dataMapping } = rawData;
  
  // 如果没有数据，返回基础配置
  if (!chartData?.columns || !chartData?.values || chartData.values.length === 0) {
    return false;
  }

  const { columns, values } = chartData;
  
  // 使用映射逻辑或默认逻辑
  let titleField = 0; // 默认第一列为标题
  let valueField = 1; // 默认第二列为值
  
  // 如果提供了映射配置，使用映射配置
  if (dataMapping) {
    // 查找titleField对应的列索引
    if (dataMapping.titleField) {
      const titleFieldIndex = columns.findIndex((col: string) => col === dataMapping.titleField);
      if (titleFieldIndex !== -1) {
        titleField = titleFieldIndex;
      }
    }
    
    // 查找valueField对应的列索引
    if (dataMapping.valueField) {
      const valueFieldIndex = columns.findIndex((col: string) => col === dataMapping.valueField);
      if (valueFieldIndex !== -1) {
        valueField = valueFieldIndex;
      }
    }
  }
  
  // 获取第一行数据作为指标卡值
  const firstRow = values[0];
  const title = String(firstRow[titleField] || '');
  const value = Number(firstRow[valueField]) || 0;
  
  // 返回指标卡配置
  return {
    ...config,
    title: {
      text: title,
      show: false
    },
    value: value,
    valueColor: config.valueColor || '#333333',
    valueSize: config.valueSize || 60,
    titleColor: config.titleColor || '#666666',
    titleSize: config.titleSize || 24,
    backgroundColor: config.backgroundColor || '#FFFFFF',
    borderRadius: config.borderRadius || 8,
    valueFormatter: config.valueFormatter || 'none'
  };
};

// 数据加载完成
const onDataLoaded = (rawData: any) => {
  try {
    // 直接处理原始数据
    const processedConfig = processMetricData(rawData);
    if (!processedConfig) {
      showChat.value = false;
      return;
    }
    
    // 使用markRaw避免Vue对复杂对象进行递归响应式处理
    processedOptions.value = markRaw(processedConfig);
    showChat.value = true;
  } catch (error) {
    console.error('处理指标卡数据出错', error);
    processedOptions.value = {};
    showChat.value = false;
  }
};

// 数据加载错误
const onError = (error: string) => {
  console.error('指标卡数据加载错误', error);
};
</script>

<style scoped>
.metric-chart-wrapper {
  width: calc(100% - 20px);
  height: calc(100% - 20px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  box-sizing: border-box;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 150px; /* 确保最小高度 */
  position: relative; /* 允许子元素使用绝对定位 */
}

.metric-title {
  margin-bottom: 15px;
  text-align: center;
  font-weight: 500;
  line-height: 1.2;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.metric-value {
  font-weight: bold;
  line-height: 1;
  width: 100%;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style> 