<template>
  <div class="user-manage-container">
    <a-form
      layout="inline"
      :model="searchForm"
      @finish="handleFinish"
    >
      <a-form-item label="用户名">
        <a-input v-model:value="searchForm.username" placeholder="请输入用户名">
          <template #prefix><UserOutlined style="color: rgba(0, 0, 0, 0.25)" /></template>
        </a-input>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" html-type="submit" >搜索</a-button>
      </a-form-item>
      <a-form-item @click="resetSearchForm">
        <a-button>重置</a-button>
      </a-form-item>
    </a-form>
    <div class="tool-line">
      <div />
      <div class ="right">
        <a-button type="primary" @click="handleOpenEdit">
          <plus-outlined /> 创建用户
        </a-button>
        <a-popconfirm
          title="确定要删除用户吗?"
          ok-text="确定"
          cancel-text="取消"
          :disabled="selectedRowKeys.length === 0"
          @confirm="deleteUser()"
        >
          <a-button type="primary" danger class="btn-item" :disabled="selectedRowKeys.length === 0" >批量删除</a-button>
        </a-popconfirm>
      </div>
    </div>

    <div class="user-list">
      <a-table
        :columns="columns"
        :data-source="userList"
        :loading="loading"
        rowKey="id"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        :pagination="getPagination"
        :scroll="{ x: 1000 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag color="green" v-if="record.status === 'enabled'">正常</a-tag>
            <a-tag color="red" v-else>停用</a-tag>
          </template>
          <template v-else-if="column.key === 'operation'">
            <a-space>
              <a @click="editUser(record)">编辑</a>
              <a-divider type="vertical" />
              <a-popconfirm
                title="确定要删除此用户吗?"
                ok-text="确定"
                cancel-text="取消"
                @confirm="deleteUser(record)"
              >
                <a class="delete-link">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <UserEdit ref="userEditRef" :user="currUser" @refreshData="fetchUserList()" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import type { GetUserListDTO, SaveUserDTO } from '@/types/system';
import UserEdit from './UserEdit.vue';
import type { UnwrapRef } from 'vue';
import type { FormProps } from 'ant-design-vue';
import * as api from '@/api/system';

// 表格列定义
const columns = [
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username',
    width: 150, // 设置最大宽度
    ellipsis: true // 文本溢出显示省略号
  },
  {
    title: '昵称',
    dataIndex: 'nickname',
    key: 'nickname',
    width: 150, // 设置最大宽度
    ellipsis: true // 文本溢出显示省略号
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email',
    width: 200, // 设置最大宽度
    ellipsis: true // 文本溢出显示省略号
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    key: 'phone',
    width: 150, // 设置最大宽度
    ellipsis: true // 文本溢出显示省略号
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100, // 设置最大宽度
    ellipsis: true // 文本溢出显示省略号
  },
  {
    title: '操作',
    key: 'operation',
    width: 150
  },
];

interface SearchForm {
  username: string;
}

const searchForm: UnwrapRef<SearchForm> = reactive({
  username: '',
});
const selectedRowKeys = ref<string[]>([]); // 选中的行键数组
const currUser = ref<SaveUserDTO>(); // 当前编辑的用户信息
const userEditRef = ref();
const editUserDialogKey = ref<number>(0);

const handleOpenEdit = () => {
  currUser.value = undefined;
  userEditRef.value?.handleOpen();
};

const onSelectChange = (selectedRowKeysValue: string[]) => {
  selectedRowKeys.value = selectedRowKeysValue;
};

const handleFinish: FormProps['onFinish'] = values => {
  fetchUserList();
};

const resetSearchForm = () => {
  searchForm.username = '';
  fetchUserList();
};

const userList = ref<SaveUserDTO[]>([]);
const loading = ref(false);

const fetchUserList = async () => {
  loading.value = true;
  try {
    const formData: GetUserListDTO = {}
    if (searchForm.username) {
      formData.username = searchForm.username;
    }
    const res = await api.getUserList(formData);
    userList.value = res.data;
  } catch (error: any) {
    message.error(error?.message || '获取用户列表失败');
  } finally {
    loading.value = false;
  }
};

// 动态计算分页配置
const getPagination = computed(() => {
  const total = userList.value.length;
  return {
    pageSize: total <= 10 ? 10 : 6,
    showSizeChanger: false, // 不显示分页大小切换器
  };
});

const editUser = (data: SaveUserDTO) => {
  currUser.value = data;
  userEditRef.value?.handleOpen();
};

const deleteUser = async (data?: SaveUserDTO) => {
  let ids: (string | number)[] = [];
  if (data) {
    ids.push(data.id as string);
  } else {
    ids = selectedRowKeys.value;
  }
  await api.deleteUser({ ids });
  message.success('删除成功');
  fetchUserList();
};

onMounted(() => {
  fetchUserList();
});
</script>

<style scoped>
.user-manage-container {
  padding: 24px;
}

.tool-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  margin-top: 10px;
  .right {
    display: flex;
    .btn-item {
    margin-left: 10px;
    }
  }
}

.user-list {
  margin-top: 16px;
}

.delete-link {
  color: #ff4d4f;
}
</style> 