<template>
  <BaseChart 
    :chart-id="chartId" 
    :dashboard-id="dashboardId"
    @data-loaded="onDataLoaded"
    @error="onError"
  >
    <template #default="slotProps">
      <div class="chart-wrapper" v-if="showChat">
        <v-chart :option="processedOptions" autoresize />
      </div>
    </template>
  </BaseChart>
</template>

<script setup lang="ts">
import { ref, markRaw } from 'vue';
import VChart from 'vue-echarts';
import BaseChart from './BaseChart.vue';

// 定义props
const props = defineProps({
  chartId: {
    type: Number,
    required: true
  },
  dashboardId: {
    type: Number,
    required: true
  }
});

// 处理后的图表ECharts配置
const processedOptions = ref<any>({});
const showChat = ref<boolean>(false);

/**
 * 处理雷达图数据
 */
const processRadarData = (rawData: any): any => {
  const { config = {}, data, dataMapping } = rawData;
  
  // 如果没有数据，返回基础配置
  if (!data?.columns || !data?.values || data.values.length === 0) {
    return false;
  }

  const { columns, values } = data;
  
  // 使用映射逻辑或默认逻辑
  let nameField = 0; // 默认第一列为指标名称
  let valueFields: number[] = []; // 需要映射到雷达图的列索引数组
  let seriesNames: string[] = []; // 系列名称数组
  
  // 如果提供了映射配置，使用映射配置
  if (dataMapping) {
    // 查找nameField对应的列索引
    if (dataMapping.nameField) {
      const nameFieldIndex = columns.findIndex((col: string) => col === dataMapping.nameField);
      if (nameFieldIndex !== -1) {
        nameField = nameFieldIndex;
      }
    }
    
    // 查找valueFields对应的列索引数组
    if (dataMapping.valueFields && Array.isArray(dataMapping.valueFields)) {
      valueFields = dataMapping.valueFields
        .map((field: string) => columns.findIndex((col: string) => col === field))
        .filter((index: number) => index !== -1);
    }
    
    // 使用提供的系列名称
    if (dataMapping.seriesNames && Array.isArray(dataMapping.seriesNames)) {
      seriesNames = dataMapping.seriesNames;
    }
  }
  
  // 如果没有指定valueFields或valueFields为空，默认使用除了nameField以外的所有列
  if (valueFields.length === 0) {
    for (let i = 0; i < columns.length; i++) {
      if (i !== nameField) {
        valueFields.push(i);
      }
    }
  }
  
  // 如果没有指定系列名称或名称不够，使用列名
  while (seriesNames.length < valueFields.length) {
    const valueFieldIndex = valueFields[seriesNames.length];
    seriesNames.push(columns[valueFieldIndex] || `系列${seriesNames.length + 1}`);
  }
  
  // 构建雷达图指标配置
  const indicator = values.map((row: any[]) => {
    // 查找指标的最大值
    const max = Math.max(...valueFields.map(fieldIndex => {
      const value = Number(row[fieldIndex]);
      return isNaN(value) ? 0 : value;
    }));
    return {
      name: String(row[nameField]),
      max: max * 1.2 // 将最大值扩大20%以便显示
    };
  });
  
  // 构建系列数据
  const series = valueFields.map((fieldIndex, seriesIndex) => {
    return {
      name: seriesNames[seriesIndex],
      type: 'radar',
      data: [{
        value: values.map((row: any[]) => {
          const value = Number(row[fieldIndex]);
          return isNaN(value) ? 0 : value;
        }),
        name: seriesNames[seriesIndex]
      }]
    };
  });
  
  // 构建返回配置
  return {
    ...config,
    title: {
      show: false
    },
    tooltip: {
      trigger: 'item'
    },
    radar: {
      indicator: indicator
    },
    series: series
  };
};

// 数据加载完成
const onDataLoaded = (rawData: any) => {
  try {
    // 直接处理原始数据
    const processedConfig = processRadarData(rawData);
    if (!processedConfig) {
      showChat.value = false;
      return;
    }
    
    // 使用markRaw避免Vue对复杂对象进行递归响应式处理
    processedOptions.value = markRaw(processedConfig);
    showChat.value = true;
  } catch (error) {
    console.error('处理雷达图数据出错', error);
    processedOptions.value = {};
    showChat.value = false;
  }
};

// 数据加载错误
const onError = (error: string) => {
  console.error('图表数据加载错误', error);
};
</script>

<style scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;
}
</style> 