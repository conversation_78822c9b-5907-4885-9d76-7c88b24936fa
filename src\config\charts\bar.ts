import tooltipOptions from '../e-charts/tooltip';
import legendOptions from '../e-charts/legend';

const barDefaultOptions = {
  dataType: 'static',
  data: {
    columns: ['类别', '销量'],
    values: [
      ['衬衫', 5],
      ['羊毛衫', 20],
      ['雪纺衫', 36],
      ['裤子', 10],
      ['高跟鞋', 10],
      ['袜子', 20]
    ]
  },
  config: {
    type: 'bar',
    tooltip: tooltipOptions,
    legend: legendOptions,
    xAxis: {
      type: 'category',
      data: []
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        type: 'bar',
        data: []
      }
    ]
  },
  dataMapping: {
    xField: '类别',
    yFields: ['销量'],
    seriesNames: ['销量']
  }
};
export default barDefaultOptions; 