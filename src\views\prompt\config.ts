export const typeOptions = [
  {
    value: 'Agent',
    label: 'AGENT',
  },
  {
    value: 'Scene',
    label: 'SCENE',
  },
  {
    value: 'Normal',
    label: 'NORMAL',
  },
  {
    value: 'Evaluate',
    label: 'EVALUATE',
  },
];

export const LangMap = { zh: '中文', en: 'English' };

export const languageOptions = [
  { label: '英文', value: 'en' },
  { label: '中文', value: 'zh' },
];

// chatData 参数
export const chatDataForm : {
  [key: string]: string;
} = {
  chat_scene: 'chat_with_db_execute',
  input_variables: '[\"db_name\",\"dialect\",\"display_type\",\"response\",\"table_info\",\"top_k\",\"user_input\"]',
  model: 'qwen-coder-plus',
  user_name: 'dbgpt',
  prompt_name: 'ChatDB',
  prompt_type: 'Scene',
  prompt_desc: '',
  response_schema: '{}',
  sub_chat_scene: '',
}

export const template = "\n请根据用户选择的数据库和该库的部分可用表结构定义来回答用户问题.\n数据库名:\n    {db_name}\n表结构定义:\n    {table_info}\n\n约束:\n    1. 请根据用户问题理解用户意图，使用给出表结构定义    创建一个语法正确的{dialect} sql，如果不需要sql，则直接回答用户问题。\n    2. 除非用户在问题中指定了他希望获得的具体数据行数，否则始终将查询限制为最多     {top_k} 个结果。\n    3. 只能使用表结构信息中提供的表来生成 sql，如果无法根据提供的表结构中生成 sql ，    请说：“提供的表结构信息不足以生成 sql 查询。” 禁止随意捏造信息。\n    4. 请注意生成SQL时不要弄错表和字段的从属关系\n    5. 请尽量给每个字段添加中文别名，字段名称用反双引号括起来\n    6. 多表查询时，请确定好主表和从表，关联方式尽量使用左外关联\n    7. 涉及排名或者既要明细又要统计结果的查询，请尽量用窗口函数实现\n    8.请从如下给出的展示方式种选择最优的一种用以进行数据渲染，    将类型名称放入返回要求格式的name参数值种，如果找不到最合适的    则使用'Table'作为展示方式，可用数据展示方式如下: {display_type}\n用户问题:\n    {user_input}\n请一步步思考并按照以下JSON格式回复：\n      {response}\n确保返回正确的json并且可以被Python json.loads方法解析.\n\n";