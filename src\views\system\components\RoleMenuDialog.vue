<template>
  <a-modal 
    v-model:visible="showDialog"
    title="添加菜单" 
    @ok="handleOk" 
    @cancel="handleCancel" 
    :width="700"
    cancelText="取消" 
    okText="确定">
    <div class="menu-edit-container">
      <!-- <a-form
        layout="inline"
        :model="searchForm"
        @finish="handleFinish"
      >
        <a-form-item label="名称">
          <a-input v-model:value="searchForm.name" placeholder="请输入菜单名称">
            <template #prefix><UserOutlined style="color: rgba(0, 0, 0, 0.25)" /></template>
          </a-input>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit" >搜索</a-button>
        </a-form-item>
        <a-form-item @click="resetSearchForm">
          <a-button>重置</a-button>
        </a-form-item>
      </a-form> -->

      <div class="menu-list">
        <a-table
          :columns="columns"
          :data-source="menuList"
          :loading="loading"
          rowKey="id"
          :row-selection="{
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectChange,
            checkStrictly: false // 允许父子联动
          }"
          :pagination="{ pageSize: 100 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag color="green" v-if="record.status === 'enabled'">正常</a-tag>
              <a-tag color="red" v-else>停用</a-tag>
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { h, ref, reactive } from 'vue';
import type { PropType, UnwrapRef } from 'vue';
import type { GetMenuListDTO, SaveMenuDTO, AddRoleMenuDTO } from '@/types/system';
import { sortMenuByOrder, genMenuMap, filterParentNode } from '../hooks/helper';
import type { CommonNumberMap } from '../hooks/type';
import type { MenuItemMap } from '../hooks/helper';
import type { FormProps } from 'ant-design-vue';
import { message } from 'ant-design-vue';
import * as api from '@/api/system';

const props = defineProps({
  roleId: {
    type: Number,
  },
  menuIds: {
    type: Array as PropType<(number | string | undefined)[]>,
    default: []
  }
});

const emit = defineEmits([
  'refreshData',
  'refreshDataForCreate',
]);

const showDialog = ref<boolean>(false);
const menuMap = ref<MenuItemMap>({}); // 存储菜单的映射关系

// 表格列定义
const columns = [
{
    title: '名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: 'URL',
    dataIndex: 'url',
    key: 'url',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
];

interface SearchForm {
  name: string;
}

const searchForm: UnwrapRef<SearchForm> = reactive({
  name: '',
});
const selectedRowKeys = ref<(string | number)[]>([]); // 选中的行键数组

// 辅助函数：递归获取所有子节点key
function getAllChildrenKeys(node: any): string[] {
  let keys: string[] = [];
  if (node.children && node.children.length > 0) {
    node.children.forEach((child: any) => {
      keys.push(String(child.id));
      keys = keys.concat(getAllChildrenKeys(child));
    });
  }
  return keys;
}

// 辅助函数：递归查找父节点
function findParentKey(list: any[], childKey: string, parentKey: string | null = null): string | null {
  for (const node of list) {
    if (String(node.id) === childKey) {
      return parentKey;
    }
    if (node.children && node.children.length > 0) {
      const found = findParentKey(node.children, childKey, String(node.id));
      if (found) return found;
    }
  }
  return null;
}

// 辅助函数：获取所有父节点
function getAllParentKeys(list: any[], childKey: string): string[] {
  const parents: string[] = [];
  let currentKey = childKey;
  while (true) {
    const parentKey = findParentKey(list, currentKey);
    if (parentKey) {
      parents.push(parentKey);
      currentKey = parentKey;
    } else {
      break;
    }
  }
  return parents;
}

// 新的 onSelectChange 实现
const onSelectChange = (newSelectedRowKeys: string[], selectedRows: any[]) => {
  let keysSet = new Set(newSelectedRowKeys);

  // 处理父子联动
  function updateSelection(list: any[]) {
    for (const node of list) {
      const nodeKey = String(node.id);
      const childrenKeys = getAllChildrenKeys(node);

      // 如果父节点被选中，则所有子节点都选中
      if (keysSet.has(nodeKey)) {
        childrenKeys.forEach(k => keysSet.add(k));
      } else {
        // 如果父节点未选中，则所有子节点都取消
        childrenKeys.forEach(k => keysSet.delete(k));
      }

      // 递归处理子节点
      if (node.children && node.children.length > 0) {
        updateSelection(node.children);
      }
    }
  }

  // 处理父节点自动选中
  function updateParentSelection(list: any[]) {
    for (const node of list) {
      if (node.children && node.children.length > 0) {
        const childrenKeys = node.children.map((child: any) => String(child.id));
        // 如果有任意子节点被选中，则父节点也选中
        if (childrenKeys.some((k : string) => keysSet.has(k))) {
          keysSet.add(String(node.id));
        } else {
          keysSet.delete(String(node.id));
        }
        updateParentSelection(node.children);
      }
    }
  }

  updateSelection(menuList.value);
  updateParentSelection(menuList.value);

  selectedRowKeys.value = Array.from(keysSet);
};

const handleFinish: FormProps['onFinish'] = values => {
  fetchMenuList();
};

const resetSearchForm = () => {
  searchForm.name = '';
  fetchMenuList();
};

const menuList = ref<SaveMenuDTO[]>([]);
const loading = ref(false);

const fetchMenuList = async () => {
  loading.value = true;
  try {
    const formData: GetMenuListDTO = {}
    if (searchForm.name) {
      formData.name = searchForm.name;
    }
    const res = await api.getMenuList(formData);
    menuList.value = sortMenuByOrder(res.data) as SaveMenuDTO[];
    menuMap.value = genMenuMap(menuList.value);
    selectedRowKeys.value = filterParentNode(menuList.value, props.menuIds) as string[];
  } catch (error: any) {
    message.error(error?.message || '获取角色列表失败');
  } finally {
    loading.value = false;
  }
};

const handleCancel = () => {
  menuList.value = [];
  selectedRowKeys.value = [];
  showDialog.value = false;
}

const handleOpen = async () => {
  fetchMenuList();
  showDialog.value = true;
}

const handleOk = async () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择菜单');
    return;
  }
  if (props.roleId) {
    const fomData :AddRoleMenuDTO = {
      role_menus: [],
    }
    const selectKeyMap : CommonNumberMap = {};
    selectedRowKeys.value.map((item) => {
      selectKeyMap[Number(item)] = true;
      if (menuMap.value.hasOwnProperty(Number(item))) {
        const menuItem = menuMap.value[Number(item)];
        if (menuItem.parent_id !== 0 && menuMap.value.hasOwnProperty(Number(menuItem.parent_id))) {
          const parentItem = menuMap.value[Number(menuItem.parent_id)];
          selectKeyMap[Number(parentItem.id)] = true;
        }
      }
    });
    for (const key in selectKeyMap) {
      fomData.role_menus.push({
        role_id: Number(props.roleId),
        menu_id: Number(key),
      });
    }
    await api.addRoleMenu(fomData);
    message.success('添加角色成功');
    emit('refreshData');
  } else {
    const selectMenus: SaveMenuDTO[] = [];
    menuList.value.map((item) => {
      if (selectedRowKeys.value.includes(item?.id || '')) {
        selectMenus.push(item);
      } else if (item.children && item.children.length > 0) {
        const children: SaveMenuDTO[] = [];
        item.children.map((child) => {
          if (selectedRowKeys.value.includes(child?.id || '')) {
            children.push(child);
          }
        })
        if (children.length > 0) {
          selectMenus.push({
            ...item,
            children,
          })
        }
      }
    });
    emit('refreshDataForCreate', selectMenus)
  }
  menuList.value = [];
  selectedRowKeys.value = [];
  showDialog.value = false;
}

defineExpose({
  handleOpen,
});

</script>

<style scoped lang="scss">
  .menu-list {
    margin-top: 16px;
  }
</style>