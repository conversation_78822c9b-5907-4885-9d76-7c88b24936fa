@use "./reset.scss";
@use "./chat.scss";

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background-color: #cccccc;
}

.el-popover {
  width: auto !important;
  min-width: auto !important;
  padding: 4px 8px !important;
  font-size: 12px !important;
}

.el-empty__image {
  width: 100px !important;
}

:root {
  --base-unit: calc(100vw / 375); 
}
