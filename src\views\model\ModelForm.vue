<template>
  <a-modal
    :visible="localVisible"
    title="创建模型"
    @ok="handleModalOk"
    @cancel="handleModalCancel"
    width="800px"
    :confirmLoading="modalLoading"
  >
    <a-spin :spinning="modelTypesLoading" tip="加载中...">
      <a-form v-if="!modelTypesLoading" ref="newFormRef" layout="horizontal" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <!-- Worker Type 选择 -->
        <a-form-item label="Worker Type" required>
          <a-select 
            v-model:value="selectState.workerType" 
            placeholder="选择Worker类型"
            @change="handleWorkerTypeChange"
          >
            <a-select-option v-for="type in workerTypeList" :key="type" :value="type">{{ type }}</a-select-option>
          </a-select>
        </a-form-item>

        <!-- Provider 选择 -->
        <a-form-item label="Provider" required v-if="providerList.length">
          <provider-selector
            v-model="selectState.provider"
            :providers="providerList"
            @change="handleProviderChange"
          />
        </a-form-item>

        <!-- 模型名称 -->
        <a-form-item label="模型名称" required v-if="modelNameList.length">
          <model-selector
            v-model="selectState.model"
            :models="modelNameList"
            @change="handleModelChange"
          />
        </a-form-item>

        <!-- 动态参数表单 -->
        <template v-for="param in selectedParams" :key="param.param_name">
          <!-- 跳过name字段，因为已经与模型名称合并 -->
          <template v-if="param.param_name !== 'name'">
            <a-form-item 
              :label="param.label" 
              :required="param.required"
            >
              <template #label>
                <span>
                  {{ param.label }}
                  <a-tooltip v-if="param.description" placement="top">
                    <template #title>{{ param.description || '' }}</template>
                    <question-circle-outlined style="margin-left: 4px" />
                  </a-tooltip>
                </span>
              </template>

              <!-- 根据参数类型渲染不同的输入控件 -->
              <template v-if="param.param_type === 'select' && param.valid_values">
                <a-select 
                  v-model:value="selectState.params[param.param_name]" 
                  :placeholder="`选择或输入${param.label}`"
                >
                  <a-select-option 
                    v-for="option in param.valid_values" 
                    :key="option.value" 
                    :value="option.value"
                  >
                    {{ option.label || option.value }}
                  </a-select-option>
                </a-select>
              </template>
              <template v-else-if="param.param_type === 'boolean'">
                <a-checkbox v-model:checked="selectState.params[param.param_name]">
                </a-checkbox>
              </template>
              <template v-else-if="param.param_type === 'integer' || param.param_type === 'number'">
                <a-input-number 
                  v-model:value="selectState.params[param.param_name]" 
                  :placeholder="`请输入${param.label}`" 
                />
              </template>
              <template v-else>
                <a-input 
                  v-model:value="selectState.params[param.param_name]" 
                  :placeholder="`请输入${param.label}`" 
                  :disabled="param.param_name === 'provider'"
                />
              </template>
            </a-form-item>
          </template>
        </template>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue';
import { message } from 'ant-design-vue';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';
import { getModelTypes, createModel } from '../../api/model';
import type { ModelTypeItem, ModelTypeParam } from '../../types/model';
import ProviderSelector from '../../components/selector/ProviderSelector.vue';
import ModelSelector from '../../components/selector/ModelSelector.vue';

const props = defineProps<{
  visible: boolean;
}>();

const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'created'): void;
}>();

// 创建本地可变状态，反映props.visible的值
const localVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 表单相关
const newFormRef = ref();
const modalLoading = ref(false);

// 模型类型数据
const modelTypes = ref<ModelTypeItem[]>([]);
const modelTypesLoading = ref(false);
const workerTypeList = ref<string[]>([]);
const providerList = ref<string[]>([]);
const modelNameList = ref<string[]>([]);
const selectedParams = ref<ModelTypeParam[]>([]);

// 选择状态
interface SelectState {
  workerType: string;
  provider: string;
  model: string;
  params: Record<string, any>;
}

const selectState = reactive<SelectState>({
  workerType: '',
  provider: '',
  model: '',
  params: {}
});

// 处理 Worker Type 选择
const handleWorkerTypeChange = (value: string) => {
  selectState.workerType = value;
  selectState.provider = '';
  selectState.model = '';
  selectState.params = {};
  selectedParams.value = [];
  
  // 过滤出该 Worker Type 下的所有 Provider
  const filteredProviders = modelTypes.value
    .filter(item => item.worker_type === value)
    .map(item => item.provider);
  
  // 去重
  providerList.value = [...new Set(filteredProviders)];
  modelNameList.value = [];
};

// 处理 Provider 选择
const handleProviderChange = (value: string) => {
  selectState.provider = value;
  selectState.model = '';
  selectState.params = {};
  selectedParams.value = [];
  
  // 过滤出该 Provider 下的所有 Model
  const filteredModels = modelTypes.value
    .filter(item => 
      item.worker_type === selectState.workerType && 
      item.provider === value
    )
    .map(item => item.model);
  
  modelNameList.value = [...new Set(filteredModels)];
};

// 处理 Model 选择
const handleModelChange = (value: string) => {
  selectState.model = value;
  selectState.params = {};
  
  // 找到唯一确定的模型配置
  const modelItem = modelTypes.value.find(item => 
    item.worker_type === selectState.workerType && 
    item.provider === selectState.provider && 
    item.model === value
  );
  
  // 设置参数配置
  if (modelItem && modelItem.params) {
    selectedParams.value = modelItem.params;
    
    // 初始化参数表单的默认值
    modelItem.params.forEach(param => {
      // 如果是name参数，使用model的值作为默认值
      if (param.param_name === 'name') {
        selectState.params[param.param_name] = value;
      } 
      // 如果是provider参数，使用当前选择的provider值
      else if (param.param_name === 'provider') {
        selectState.params[param.param_name] = selectState.provider;
      }
      // 如果是worker_type参数，使用当前选择的worker_type值
      else if (param.param_name === 'worker_type') {
        selectState.params[param.param_name] = selectState.workerType;
      }
      else {
        // selectState.params[param.param_name] = param.default_value !== undefined ? param.default_value : '';
        selectState.params[param.param_name] = null;
      }
    });
  } else {
    selectedParams.value = [];
  }
};

// 获取模型类型数据
const fetchModelTypes = async () => {
  try {
    modelTypesLoading.value = true;
    
    const response = await getModelTypes();
    
    if (response.success && response.data) {
      modelTypes.value = response.data;
      
      // 提取所有 worker_type
      const types = response.data.map(item => item.worker_type);
      workerTypeList.value = [...new Set(types)];
    } else {
      message.error('获取模型类型数据失败');
      handleModalCancel(); // 获取数据失败时关闭弹窗
    }
  } catch (error) {
    console.error('获取模型类型数据出错:', error);
    message.error('获取模型类型数据出错');
    handleModalCancel(); // 发生错误时关闭弹窗
  } finally {
    modelTypesLoading.value = false;
  }
};

// 处理模态框确认
const handleModalOk = async () => {
  modalLoading.value = true;
  
  try {
    // 验证必填参数
    if (!selectState.workerType) {
      message.error('请选择 Worker Type');
      modalLoading.value = false;
      return;
    }
    
    if (!selectState.provider) {
      message.error('请选择提供商');
      modalLoading.value = false;
      return;
    }
    
    if (!selectState.model) {
      message.error('请选择模型');
      modalLoading.value = false;
      return;
    }
    
    // 验证必填参数
    const requiredParams = selectedParams.value.filter(param => param.required);
    for (const param of requiredParams) {
      if (selectState.params[param.param_name] === undefined || selectState.params[param.param_name] === '') {
        message.error(`请填写 ${param.label || param.param_name}`);
        modalLoading.value = false;
        return;
      }
    }
    
    // 找到当前选中的模型配置
    const modelItem = modelTypes.value.find(item => 
      item.worker_type === selectState.workerType && 
      item.provider === selectState.provider && 
      item.model === selectState.model
    );

    if (!modelItem) {
      message.error('无法获取模型配置信息');
      modalLoading.value = false;
      return;
    }

    // 确保params中包含必要的字段
    if (!selectState.params.name) {
      selectState.params.name = selectState.model;
    }
    if (!selectState.params.worker_type) {
      selectState.params.worker_type = selectState.workerType;
    }
    if (!selectState.params.provider) {
      selectState.params.provider = selectState.provider;
    }
    
    // 构建提交的数据 - 按照要求的格式：外层包含host、port、model、worker_type，内层params包含动态参数
    const postData = {
      // 外层参数
      host: modelItem.host,
      port: modelItem.port,
      model: selectState.model,
      worker_type: selectState.workerType,
      // 内层参数
      params: {
        ...selectState.params
      }
    };

    console.log('创建模型提交数据:', postData);
    
    // 调用保存接口
    try {
      const response = await createModel(postData);
      
      if (response.success) {
        message.success('创建模型成功');
        emit('created');
        handleModalCancel();
      } else {
        message.error(response.err_msg || '创建模型失败');
      }
    } catch (error) {
      console.error('创建模型出错:', error);
      message.error('创建模型出错');
    }
  } finally {
    modalLoading.value = false;
  }
};

// 取消模态框
const handleModalCancel = () => {
  emit('update:visible', false);
  resetForm();
};

// 重置表单
const resetForm = () => {
  selectState.workerType = '';
  selectState.provider = '';
  selectState.model = '';
  selectState.params = {};
  selectedParams.value = [];
  providerList.value = [];
  modelNameList.value = [];
};

// 监听 visible 变化
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    resetForm();
    fetchModelTypes();
  }
});
</script>
