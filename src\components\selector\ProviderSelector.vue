<template>
  <div class="provider-selector">
    <!-- 选择框 -->
    <div class="selector-dropdown" @click="toggleDropdown">
      <div v-if="selectedProvider" class="selected-provider">
        <div class="provider-icon">
          <img :src="getProviderIcon(selectedProvider)" :alt="selectedProvider" />
        </div>
        <div class="provider-name">{{ getProviderDisplayName(selectedProvider) }}</div>
      </div>
      <div v-else class="placeholder">请选择模型提供商</div>
      <div class="dropdown-arrow" :class="{ 'arrow-up': isDropdownOpen }">
        <svg viewBox="0 0 1024 1024" width="12" height="12">
          <path d="M512 714.666667c-8.533333 0-17.066667-2.133333-23.466667-8.533334l-341.333333-341.333333c-12.8-12.8-12.8-32 0-44.8 12.8-12.8 32-12.8 44.8 0l320 320 320-320c12.8-12.8 32-12.8 44.8 0 12.8 12.8 12.8 32 0 44.8l-341.333333 341.333333c-6.4 6.4-14.933333 8.533333-23.466667 8.533334z" fill="currentColor"></path>
        </svg>
      </div>
    </div>

    <!-- 下拉选项列表 -->
    <div v-if="isDropdownOpen" class="dropdown-panel">
      <div class="search-box">
        <input
          type="text"
          v-model="searchText"
          placeholder="搜索模型提供商..."
          @click.stop
        />
      </div>
      <div class="provider-list">
        <div
          v-for="provider in filteredProviders"
          :key="provider"
          class="provider-item"
          :class="{ active: modelValue === provider }"
          @click.stop="selectProvider(provider)"
        >
          <div class="provider-icon">
            <img :src="getProviderIcon(provider)" :alt="provider" />
          </div>
          <div class="provider-name">{{ getProviderDisplayName(provider) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';

// 引入所有模型提供商图标
import openaiIcon from '../../assets/icons/provider/openai.png';
import claudeIcon from '../../assets/icons/provider/claude.png';
import mistralIcon from '../../assets/icons/provider/mistral.png';
import geminiIcon from '../../assets/icons/provider/gemini.png';
import metaIcon from '../../assets/icons/provider/meta.png';
import baaiIcon from '../../assets/icons/provider/baai.png';
import googleIcon from '../../assets/icons/provider/google.png';
import microsoftIcon from '../../assets/icons/provider/microsoft.png';
import jinaIcon from '../../assets/icons/provider/jina.png';
import vllmIcon from '../../assets/icons/provider/vllm.png';
import zhipuIcon from '../../assets/icons/provider/zhipu.png';
import giteeIcon from '../../assets/icons/provider/gitee.png';
import wenxinIcon from '../../assets/icons/provider/wenxin.png';
import yiIcon from '../../assets/icons/provider/yi.png';
import ollamaIcon from '../../assets/icons/provider/ollama.png';
import huggingfaceIcon from '../../assets/icons/provider/huggingface.png';
import moonshotIcon from '../../assets/icons/provider/moonshot.png';
import siliconcloudIcon from '../../assets/icons/provider/siliconcloud.png';
import baichuanIcon from '../../assets/icons/provider/baichuan.png';
import volcengineIcon from '../../assets/icons/provider/volcengine.png';
import sparkIcon from '../../assets/icons/provider/spark.png';
import modelscopeIcon from '../../assets/icons/provider/modelscope.png';
import deepseekIcon from '../../assets/icons/provider/deepseek.png';
import qianfanIcon from '../../assets/icons/provider/qianfan.png';
import tongyiIcon from '../../assets/icons/provider/tongyi.png';
import llamaCppIcon from '../../assets/icons/provider/llama.cpp.png';

// 定义组件属性
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  providers: {
    type: Array as () => string[],
    default: () => []
  }
});

// 定义事件
const emit = defineEmits(['update:modelValue', 'change']);

// 状态变量
const isDropdownOpen = ref(false);
const searchText = ref('');

// 获取提供商对应的图标
const getProviderIcon = (provider: string) => {
  if (!provider) return huggingfaceIcon;
  
  // 移除可能的 "proxy/" 前缀
  let providerName = provider;
  if (provider.startsWith('proxy/')) {
    providerName = provider.substring(6);
  }
  
  const providerLower = providerName.toLowerCase();
  
  const iconMap: Record<string, any> = {
    'openai': openaiIcon,
    'claude': claudeIcon,
    'anthropic': claudeIcon,
    'mistral': mistralIcon,
    'gemini': geminiIcon,
    'meta': metaIcon,
    'baai': baaiIcon,
    'google': googleIcon,
    'microsoft': microsoftIcon,
    'jina': jinaIcon,
    'vllm': vllmIcon,
    'zhipu': zhipuIcon,
    'gitee': giteeIcon,
    'wenxin': wenxinIcon,
    'yi': yiIcon,
    'ollama': ollamaIcon,
    'huggingface': huggingfaceIcon,
    'moonshot': moonshotIcon,
    'siliconcloud': siliconcloudIcon,
    'siliconflow': siliconcloudIcon, // 添加 siliconflow 映射到 siliconcloud 图标
    'baichuan': baichuanIcon,
    'volcengine': volcengineIcon,
    'spark': sparkIcon,
    'modelscope': modelscopeIcon,
    'deepseek': deepseekIcon,
    'qianfan': qianfanIcon,
    'tongyi': tongyiIcon,
    'llama.cpp': llamaCppIcon
  };
  
  // 处理带有特殊字符的提供商名称
  const specialMappings: Record<string, string> = {
    'llamacpp': 'llama.cpp',
    'llama-cpp': 'llama.cpp',
    'llama_cpp': 'llama.cpp'
  };

  // 检查是否有特殊映射
  if (specialMappings[providerLower]) {
    return iconMap[specialMappings[providerLower]];
  }
  
  // 先尝试直接匹配
  if (iconMap[providerLower]) {
    return iconMap[providerLower];
  }
  
  // 如果没有直接匹配，尝试部分匹配
  for (const [key, icon] of Object.entries(iconMap)) {
    if (providerLower.includes(key)) {
      return icon;
    }
  }
  
  return huggingfaceIcon; // 默认使用huggingface图标
};

// 获取显示用的提供商名称
const getProviderDisplayName = (provider: string) => {
  if (!provider) return '';
  
  // 移除可能的 "proxy/" 前缀
  if (provider.startsWith('proxy/')) {
    return provider.substring(6);
  }
  
  return provider;
};

// 点击外部关闭下拉框
const handleClickOutside = (e: MouseEvent) => {
  const target = e.target as HTMLElement;
  if (!target.closest('.provider-selector')) {
    isDropdownOpen.value = false;
  }
};

// 过滤提供商列表
const filteredProviders = computed(() => {
  if (!searchText.value) return props.providers;
  const search = searchText.value.toLowerCase();
  return props.providers.filter(provider => {
    const displayName = getProviderDisplayName(provider).toLowerCase();
    return displayName.includes(search);
  });
});

// 切换下拉框状态
const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value;
  if (isDropdownOpen.value) {
    searchText.value = '';
  }
};

// 选择提供商
const selectProvider = (provider: string) => {
  emit('update:modelValue', provider);
  emit('change', provider);
  isDropdownOpen.value = false;
};

// 获取当前选中的提供商
const selectedProvider = computed(() => {
  return props.modelValue;
});

// 组件生命周期钩子
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.provider-selector {
  width: 100%;
  position: relative;
}

/* 选择框样式 */
.selector-dropdown {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 8px 12px;
  min-height: 42px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: #fff;
}

.selector-dropdown:hover {
  border-color: #40a9ff;
}

.selected-provider {
  display: flex;
  align-items: center;
  flex: 1;
}

.placeholder {
  color: #bfbfbf;
  font-size: 14px;
}

.dropdown-arrow {
  color: #bfbfbf;
  transition: transform 0.3s;
  margin-left: 8px;
}

.arrow-up {
  transform: rotate(180deg);
}

/* 下拉面板样式 */
.dropdown-panel {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  right: 0;
  background-color: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 360px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-box {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.search-box input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  font-size: 14px;
  background-color: #fff;
  outline: none;
  transition: all 0.3s;
}

.search-box input:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.search-box input::placeholder {
  color: #bfbfbf;
}

/* 提供商列表样式 */
.provider-list {
  overflow-y: auto;
  max-height: 280px;
  padding: 8px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.provider-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  border-radius: 4px;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s;
}

.provider-item:hover {
  background-color: #f5f5f5;
}

.provider-item.active {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
}

.provider-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.provider-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.provider-name {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 适配选中状态和下拉状态样式 */
.selector-dropdown.focus,
.selector-dropdown:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}
</style>
