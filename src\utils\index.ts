import { ElMessage } from "element-plus";
import { message } from 'ant-design-vue';
import DeepseekIcon from '@/assets/images/models/deepseek.png';
import QwenIcon from '@/assets/images/models/qwen2.png';
import GeminiIcon from '@/assets/images/models/gemini.png';
import MoonshotIcon from '@/assets/images/models/moonshot.png';
import DoubaoIcon from '@/assets/images/models/doubao.png';
import ErnieIcon from '@/assets/images/models/ernie.png';
import ChatgptIcon from '@/assets/images/models/chatgpt.png';
import VicunaIcon from '@/assets/images/models/vicuna.jpeg';
import ChatglmIcon from '@/assets/images/models/chatglm.png';
import LlamaIcon from '@/assets/images/models/llama.jpg';
import BaichuanIcon from '@/assets/images/models/baichuan.png';
import ClaudeIcon from '@/assets/images/models/claude.png';
import BardIcon from '@/assets/images/models/bard.gif';
import TongyiIcon from '@/assets/images/models/tongyi.apng';
import YiIcon from '@/assets/images/models/yi.svg';
import BailingIcon from '@/assets/images/models/bailing.svg';
import WizardlmIcon from '@/assets/images/models/wizardlm.png';
import InternlmIcon from '@/assets/images/models/internlm.png';
import SolarLogoIcon from '@/assets/images/models/solar_logo.png';
import GorillaIcon from '@/assets/images/models/gorilla.png';
import ZhipuIcon from '@/assets/images/models/zhipu.png';
import FalconIcon from '@/assets/images/models/falcon.jpeg';
import HuggingfaceIcon from '@/assets/images/models/huggingface.svg';
import ColorfulDoc from '@/components/icons/colorful-doc.vue';
import ColorfulData from '@/components/icons/colorful-data.vue';
import ColorfulExcel from '@/components/icons/colorful-excel.vue';
import ColorfulDB from '@/components/icons/colorful-db.vue';
import ColorfulDashboard from '@/components/icons/colorful-dashboard.vue';
import ColorfulPlugin from '@/components/icons/colorful-plugin.vue';
import ColorfulChat from '@/components/icons/colorful-chat.vue';
import MysqlIcon from '@/assets/images/resource/mysql.png'
import OceanBaseIcon from '@/assets/images/resource/oceanbase.png'
import MssqlIcon from '@/assets/images/resource/mssql.png'
import DuckDbIcon from '@/assets/images/resource/duckdb.png'
import SqliteIcon from '@/assets/images/resource/sqlite.png'
import ClickHouseIcon from '@/assets/images/resource/clickhouse.png'
import OracleIcon from '@/assets/images/resource/oracle.png'
import AccessIcon from '@/assets/images/resource/access.png'
import MongodbIcon from '@/assets/images/resource/mongodb.png'
import DorisIcon from '@/assets/images/resource/doris.png'
import StarrocksIcon from '@/assets/images/resource/starrocks.png'
import DbIcon2 from '@/assets/images/resource/db2.png'
import HBaseIcon from '@/assets/images/resource/hbase.png'
import RedisIcon from '@/assets/images/resource/redis.png'
import CassandraIcon from '@/assets/images/resource/cassandra.png'
import CouchbaseIcon from '@/assets/images/resource/couchbase.png'
import OdcIcon from '@/assets/images/resource/odc.png'
import PostgresqlIcon from '@/assets/images/resource/postgresql.png'
import VerticaIcon from '@/assets/images/resource/vertica.png'
import SparkIcon from '@/assets/images/resource/spark.png'
import HiveIcon from '@/assets/images/resource/hive.png'
import KnowledgeIcon from '@/assets/images/resource/knowledge.png'
import TugraphIcon from '@/assets/images/resource/tugraph.png'
import { format } from 'sql-formatter';
import { appList } from "@/config";

export const DEFAULT_ICON_URL = HuggingfaceIcon;

// 生成随机字符串
export const generateRandomString = (length: number) => {
  const characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    result += characters[randomIndex];
  }
  return result;
}

// 复制文本到剪切板
export const onClickCopy = (content: string) => {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(content).then(() => {
      message.success('复制成功');
    }).catch(err => {
      message.error('复制失败');
    });
  } else {
    fallbackCopyTextToClipboard(content);
  }
}

function fallbackCopyTextToClipboard(text: string) {
  try {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    
    // 使元素不可见
    textArea.style.position = 'fixed';
    textArea.style.top = '0';
    textArea.style.left = '0';
    textArea.style.width = '2em';
    textArea.style.height = '2em';
    textArea.style.padding = '0';
    textArea.style.border = 'none';
    textArea.style.outline = 'none';
    textArea.style.boxShadow = 'none';
    textArea.style.background = 'transparent';
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    const successful = document.execCommand('copy');
    document.body.removeChild(textArea);
    
    if (successful) {
      message.success('复制成功');
    } else {
      message.error('复制失败');
    }
  } catch (err) {
    message.error('复制失败');
  }
}

// 校验手机号
export function isValidPhoneNumber(phoneNumber: string) {
  let str = phoneNumber.replace(/ /g, '');
  const regex = /^1[3-9]\d{9}$/;
  return regex.test(str);
}

// 用户名校验
// 用户名可以由字母（a - z、A - Z）、数字（0 - 9）以及一些特殊字符（如 “_”“.”“-”）组成。不能以特殊字符开头或结尾，且连续的特殊字符可能不被允许。例如，“example”“example.” 这样的用户名是不符合规范的，而 “example_123” 是合法的
export function isValidUsername(username: string) {
  // 正则表达式规则：
  // 1. 以字母或数字开头 ^[a-zA-Z0-9]
  // 2. 以字母或数字结尾 [a-zA-Z0-9]$
  // 3. 中间可包含字母、数字或单个特殊字符（不能连续）
  // 4. 特殊字符必须被字母/数字包围（不能连续）
  const usernameRegex = /^[a-z0-9](?:([a-z0-9]|([._-](?![._-]))))*[a-z0-9]$/i;
  
  return usernameRegex.test(username);
}

// 校验密码
export function isValidPassword(password: string) {
  return password.length >= 5 && password.length <= 20;
}

// 校验邮箱
// 邮箱格式字符通常包括字母、数字和 “-”，且不能以 “-” 开头或结尾。例如，“-domain.com” 和 “domain-.com” 是不合法的，而 “domain-123.com” 是合规的。长度限制：整个域名的长度一般限制在 255 个字符以内，每个部分（不包括 “.”）的长度通常不能超过 63 个字符
export function isValidEmailManas(email: string) {
  // 检查整个域名长度是否在 255 个字符以内
  if (email.length > 255) {
    return  {
      valid: false,
      msg: '邮箱长度不超过 255 个字符'
    };
  }

  // 检查邮箱格式是否包含 @ 符号
  const parts = email.split('@');
  if (parts.length!== 2) {
    return {
      valid: false,
      msg: '邮箱格式不正确'
    };
  }

  const localPart = parts[0];
  const domainPart = parts[1];

  // 检查本地部分和域名部分的长度是否超过 63 个字符
  if (localPart.length > 63 || domainPart.length > 63) {
    return {
      valid: false,
      msg: '各部分不超过63个字符'
    };
  }

  // 检查域名部分是否以 - 开头或结尾
  if (localPart.startsWith('-') || localPart.endsWith('-') ||  domainPart.startsWith('-') || domainPart.endsWith('-')) {
    return {
      valid: false,
      msg: '不能是否以 - 开头或结尾'
    };
  }

  // 检查邮箱格式是否只包含字母、数字和 -
  const validCharsRegex = /^[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*$/;
  if (!validCharsRegex.test(localPart) ||!validCharsRegex.test(domainPart)) {
    return {
      valid: false,
      msg: '邮箱格式只能包含字母、数字和 -'
    };
  }

  // 验证 @ 符号后面的是否是正常的域名
  const domainRegex = /^([a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/;
  if (!domainRegex.test(domainPart)) {
    return {
      valid: false,
      msg: '域名格式不正确'
    };
  }

  return {
    valid: true,
    msg: '',
  };
}

export const MODEL_ICON_INFO = {
  deepseek: {
    label: 'DeepSeek',
    icon: DeepseekIcon,
    patterns: ['deepseek', 'r1'],
  },
  qwen: {
    label: 'Qwen',
    icon: QwenIcon,
    patterns: ['qwen', 'qwen2', 'qwen2.5', 'qwq', 'qvq'],
  },
  gemini: {
    label: 'Gemini',
    icon: GeminiIcon,
    patterns: ['gemini'],
  },
  moonshot: {
    label: 'Moonshot',
    icon: MoonshotIcon,
    patterns: ['moonshot'],
  },
  doubao: {
    label: 'Doubao',
    icon: DoubaoIcon,
    patterns: ['doubao'],
  },
  ernie: {
    label: 'ERNIE',
    icon: ErnieIcon,
    patterns: ['ernie'],
  },
  proxyllm: {
    label: 'Proxy LLM',
    icon: ChatgptIcon,
    patterns: ['proxy'],
  },
  chatgpt: {
    label: 'ChatGPT',
    icon: ChatgptIcon,
    patterns: ['chatgpt', 'gpt', 'o1', 'o3'],
  },
  vicuna: {
    label: 'Vicuna',
    icon: VicunaIcon,
    patterns: ['vicuna'],
  },
  chatglm: {
    label: 'ChatGLM',
    icon: ChatglmIcon,
    patterns: ['chatglm', 'glm'],
  },
  llama: {
    label: 'Llama',
    icon: LlamaIcon,
    patterns: ['llama', 'llama2', 'llama3'],
  },
  baichuan: {
    label: 'Baichuan',
    icon: BaichuanIcon,
    patterns: ['baichuan'],
  },
  claude: {
    label: 'Claude',
    icon: ClaudeIcon,
    patterns: ['claude'],
  },
  bard: {
    label: 'Bard',
    icon: BardIcon,
    patterns: ['bard'],
  },
  tongyi: {
    label: 'Tongyi',
    icon: TongyiIcon,
    patterns: ['tongyi'],
  },
  yi: {
    label: 'Yi',
    icon: YiIcon,
    patterns: ['yi'],
  },
  bailing: {
    label: 'Bailing',
    icon: BailingIcon,
    patterns: ['bailing'],
  },
  wizardlm: {
    label: 'WizardLM',
    icon: WizardlmIcon,
    patterns: ['wizard'],
  },
  internlm: {
    label: 'InternLM',
    icon: InternlmIcon,
    patterns: ['internlm'],
  },
  solar: {
    label: 'Solar',
    icon: SolarLogoIcon,
    patterns: ['solar'],
  },
  gorilla: {
    label: 'Gorilla',
    icon: GorillaIcon,
    patterns: ['gorilla'],
  },
  zhipu: {
    label: 'Zhipu',
    icon: ZhipuIcon,
    patterns: ['zhipu'],
  },
  falcon: {
    label: 'Falcon',
    icon: FalconIcon,
    patterns: ['falcon'],
  },
  huggingface: {
    label: 'Hugging Face',
    icon: HuggingfaceIcon,
    patterns: ['huggingface', 'hf'],
  },
};

type ModelId = keyof typeof MODEL_ICON_INFO;

export function getModelIcon(modelId: ModelId | string): string {
  if (!modelId) return DEFAULT_ICON_URL;

  // Format the model ID for matching
  const formattedModelId = modelId.toLowerCase();

  // 1. Try to match directly
  if (MODEL_ICON_INFO[modelId as ModelId]?.icon) {
    return MODEL_ICON_INFO[modelId as ModelId].icon;
  }

  // 2. Try to match by patterns
  for (const key in MODEL_ICON_INFO) {
    const modelInfo = MODEL_ICON_INFO[key as keyof typeof MODEL_ICON_INFO];

    // Check if the model ID contains one of the patterns
    if (modelInfo.patterns && modelInfo.patterns.some(pattern => formattedModelId.includes(pattern.toLowerCase()))) {
      return modelInfo.icon;
    }
  }

  // Try to match by the model prefix
  const modelParts = formattedModelId.split(/[-_]/);
  if (modelParts.length > 0) {
    const modelPrefix = modelParts[0];
    for (const key in MODEL_ICON_INFO) {
      if (modelPrefix === key.toLowerCase()) {
        return MODEL_ICON_INFO[key as keyof typeof MODEL_ICON_INFO].icon;
      }
    }
  }

  // If no match, return the default icon
  return DEFAULT_ICON_URL;
}

export function getAppIcon(scene: string) {
  switch (scene) {
    case 'chat_knowledge':
      return ColorfulDoc;
    case 'chat_with_db_execute':
      return ColorfulData;
    case 'chat_excel':
      return ColorfulExcel;
    case 'chat_with_db_qa':
    case 'chat_dba':
      return ColorfulDB;
    case 'chat_dashboard':
      return ColorfulDashboard;
    case 'chat_agent':
      return ColorfulPlugin;
    case 'chat_normal':
      return ColorfulChat;
    default:
      return;
  }
}

export function getContentOnly(str: string) {
  if (!str.includes('```vis-thinking')) {
    return str;
  }
  let arr = str.split('\\n```\\n');
  if (arr.length > 1) {
    return arr[1];
  } else {
    arr = str.split('\n```');
    if (arr.length > 1) {
      return arr[1];
    }
    return str;
  }
}

export const dbMapper = {
  mysql: {
    label: 'MySQL',
    icon: MysqlIcon,
    desc: 'Fast, reliable, scalable open-source relational database management system.',
  },
  oceanbase: {
    label: 'OceanBase',
    icon: OceanBaseIcon,
    desc: 'An Ultra-Fast & Cost-Effective Distributed SQL Database.',
  },
  mssql: {
    label: 'MSSQL',
    icon: MssqlIcon,
    desc: 'Powerful, scalable, secure relational database system by Microsoft.',
  },
  duckdb: {
    label: 'DuckDB',
    icon: DuckDbIcon,
    desc: 'In-memory analytical database with efficient query processing.',
  },
  sqlite: {
    label: 'Sqlite',
    icon: SqliteIcon,
    desc: 'Lightweight embedded relational database with simplicity and portability.',
  },
  clickhouse: {
    label: 'ClickHouse',
    icon: ClickHouseIcon,
    desc: 'Columnar database for high-performance analytics and real-time queries.',
  },
  oracle: {
    label: 'Oracle',
    icon: OracleIcon,
    desc: 'Robust, scalable, secure relational database widely used in enterprises.',
  },
  access: {
    label: 'Access',
    icon: AccessIcon,
    desc: 'Easy-to-use relational database for small-scale applications by Microsoft.',
  },
  mongodb: {
    label: 'MongoDB',
    icon: MongodbIcon,
    desc: 'Flexible, scalable NoSQL document database for web and mobile apps.',
  },
  doris: {
    label: 'ApacheDoris',
    icon: DorisIcon,
    desc: 'A new-generation open-source real-time data warehouse.',
  },
  starrocks: {
    label: 'StarRocks',
    icon: StarrocksIcon,
    desc: 'An Open-Source, High-Performance Analytical Database.',
  },
  db2: { 
    label: 'DB2', 
    icon: DbIcon2, 
    desc: 'Scalable, secure relational database system developed by IBM.' 
  },
  hbase: {
    label: 'HBase',
    icon: HBaseIcon,
    desc: 'Distributed, scalable NoSQL database for large structured/semi-structured data.',
  },
  redis: {
    label: 'Redis',
    icon: RedisIcon,
    desc: 'Fast, versatile in-memory data structure store as cache, DB, or broker.',
  },
  cassandra: {
    label: 'Cassandra',
    icon: CassandraIcon,
    desc: 'Scalable, fault-tolerant distributed NoSQL database for large data.',
  },
  couchbase: {
    label: 'Couchbase',
    icon: CouchbaseIcon,
    desc: 'High-performance NoSQL document database with distributed architecture.',
  },
  omc: { 
    label: 'Omc', 
    icon: OdcIcon, 
    desc: 'Omc meta data.' 
  },
  postgresql: {
    label: 'PostgreSQL',
    icon: PostgresqlIcon,
    desc: 'Powerful open-source relational database with extensibility and SQL standards.',
  },
  vertica: {
    label: 'Vertica',
    icon: VerticaIcon,
    desc: 'Vertica is a strongly consistent, ACID-compliant, SQL data warehouse, built for the scale and complexity of today’s data-driven world.',
  },
  spark: { 
    label: 'Spark', 
    icon: SparkIcon, 
    desc: 'Unified engine for large-scale data analytics.' 
  },
  hive: { 
    label: 'Hive', 
    icon: HiveIcon, 
    desc: 'A distributed fault-tolerant data warehouse system.' 
  },
  space: { 
    label: 'Space', 
    icon: KnowledgeIcon, 
    desc: 'knowledge analytics.' 
  },
  tugraph: {
    label: 'TuGraph',
    icon: TugraphIcon,
    desc: 'TuGraph is a high-performance graph database jointly developed by Ant Group and Tsinghua University.',
  },
};

export function getChatModeByRoute() {
  const pathname = location.pathname;
  const mapList = [
    { path: '/chatNormal', chatMode: 'chat_normal' },
    { path: '/chatDb', chatMode: 'chat_with_db_qa' },
    { path: '/chatData', chatMode: 'chat_with_db_execute' },
    { path: '/chat', chatMode: 'chat_normal' },
  ]
  const index = mapList.findIndex(item => pathname.startsWith(item.path));

  return mapList[index].chatMode;
}

export function getChatAppCodeByRoute() {
  const pathname = location.pathname;
  const chatDataAppCode = '0e221f70-3131-11f0-8513-9f8972b3f61a';
    // import.meta.env.MODE === 'production' ? 
    // '0e221f70-3131-11f0-8513-9f8972b3f61a' : 
    // 'chat_with_db_execute';
  const mapList = [
    { path: '/chatNormal', appCode: 'chat_normal' },
    { path: '/chatDb', appCode: 'chat_with_db_qa' },
    { path: '/chatData', appCode:  chatDataAppCode },
    { path: '/chat', appCode: 'chat_normal' },
  ]
  const index = mapList.findIndex(item => pathname.startsWith(item.path));

  return mapList[index].appCode;
}

export function getPrologByRoute() {
  const pathname = location.pathname;
  let chatNormalProlog = '';
  for (let i = 0; i < appList.length; i++) {
    if (pathname.startsWith(appList[i].path)) {
      return appList[i].prolog;
    }
    if (appList[i].path === '/chatNormal') {
      chatNormalProlog = appList[i].prolog;
    }
  }
  return chatNormalProlog;
} 

export function getAppInfoByRoute() {
  const pathname = location.pathname;
  let chatNormalInfo;
  for (let i = 0; i < appList.length; i++) {
    if (pathname.startsWith(appList[i].path)) {
      return appList[i];
    }
    if (appList[i].path === '/chatNormal') {
      chatNormalInfo = appList[i];
    }
  }
  return chatNormalInfo;
} 

export function getPathByMode(mode: string) {
  switch (mode) {
    case 'chat_normal':
      return 'chat';
    case 'chat_with_db_qa':
      return 'chatDb';
    case 'chat_with_db_execute':
      return 'chatData';
    default:
      return 'chat';
  }
}

export function decodeHTMLEntities(text: string): string {
  const textarea = document.createElement('textarea');
  textarea.innerHTML = text;
  return textarea.value;
}

export function formatSql(sql: string, lang?: string) {
  if (!sql) return '';
  // 先对 SQL 进行 HTML 实体解码
  const decodedSql = decodeHTMLEntities(sql);
  try {
    return format(decodedSql, { language: lang as any });
  } catch {
    return decodedSql;
  }
}

export function getNowDate() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const createTime = `${year}-${month}-${day}`;
  return createTime;
}

export function hasRoutePermission(map: Record<string, boolean>, path: string) {
  for (const i in map) {
    if (i.includes(path)) {
      return true;
    }
  }
  return false;
}