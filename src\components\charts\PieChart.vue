<template>
  <BaseChart 
    :chart-id="chartId" 
    :dashboard-id="dashboardId"
    @data-loaded="onDataLoaded"
    @error="onError"
  >
    <template #default="slotProps">
      <div class="chart-wrapper" v-if="showChat">
        <v-chart :option="processedOptions" autoresize />
      </div>
    </template>
  </BaseChart>
</template>

<script setup lang="ts">
import { ref, markRaw } from 'vue';
import VChart from 'vue-echarts';
import BaseChart from './BaseChart.vue';

// 定义props
const props = defineProps({
  chartId: {
    type: Number,
    required: true
  },
  dashboardId: {
    type: Number,
    required: true
  }
});

// 处理后的图表ECharts配置
const processedOptions = ref<any>({});
const showChat = ref<boolean>(false);

/**
 * 处理饼图数据
 */
const processPieData = (rawData: any): any => {
  const { config = {}, data, dataMapping } = rawData;
  
  // 如果没有数据，返回基础配置
  if (!data?.columns || !data?.values || data.values.length === 0) {
    return false;
  }

  const { columns, values } = data;
  
  // 使用映射逻辑或默认逻辑
  let nameField = 0; // 默认第一列为名称
  let valueField = 1; // 默认第二列为值
  
  // 如果提供了映射配置，使用映射配置
  if (dataMapping) {
    // 查找nameField对应的列索引
    if (dataMapping.nameField) {
      const nameFieldIndex = columns.findIndex((col: string) => col === dataMapping.nameField);
      if (nameFieldIndex !== -1) {
        nameField = nameFieldIndex;
      }
    }
    
    // 查找valueField对应的列索引
    if (dataMapping.valueField) {
      const valueFieldIndex = columns.findIndex((col: string) => col === dataMapping.valueField);
      if (valueFieldIndex !== -1) {
        valueField = valueFieldIndex;
      }
    }
  }
  
  // 构建饼图数据
  const seriesData = values.map((row: any[]) => ({
    name: String(row[nameField]),
    value: Number(row[valueField]) || 0
  }));
  
  // 构建返回配置
  return {
    ...config,
    title: {
      show: false
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    series: [{
      type: 'pie',
      radius: '50%',
      data: seriesData,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  };
};

// 数据加载完成
const onDataLoaded = (rawData: any) => {
  try {
    // 直接处理原始数据
    const processedConfig = processPieData(rawData);
    if (!processedConfig) {
      showChat.value = false;
      return;
    }
    
    // 使用markRaw避免Vue对复杂对象进行递归响应式处理
    processedOptions.value = markRaw(processedConfig);
    showChat.value = true;
  } catch (error) {
    console.error('处理饼图数据出错', error);
    processedOptions.value = {};
    showChat.value = false;
  }
};

// 数据加载错误
const onError = (error: string) => {
  console.error('图表数据加载错误', error);
};
</script>

<style scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;
}
</style> 