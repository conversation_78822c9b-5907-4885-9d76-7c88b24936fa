<template>
  <div class="think-loading-step">
    <img :src="thinkSrc" />
    <div>回答中<span v-for="_ in thinkSteps">.</span></div>
  </div>
</template>

<script setup lang="ts">
import thinkSrc from '@/assets/images/think.gif';
import { onMounted, onUnmounted, ref } from 'vue';

const thinkTimer = ref();
const thinkSteps = ref<number>(3);

onMounted(() => {
  thinkTimer.value = setInterval(() => {
    if (thinkSteps.value === 3) {
      thinkSteps.value = 1;
    } else {
      thinkSteps.value++;
    }
  }, 300)
});

onUnmounted(() => {
  clearInterval(thinkTimer.value);
})
</script>
