import request from '../utils/request';
import type { 
  GetUserListDTO,
  SaveUserDTO,
  GetRoleListDTO,
  SaveRoleDTO,
  GetMenuListDTO,
  SaveMenuDTO,
  DeleteDTO,
  GetUserRoleListDTO,
  UserRoleItem,
  AddUserRoleDTO,
  GetRoleMenuListDTO,
  RoleMenuItem,
  AddRoleMenuDTO,
} from '../types/system';

// API响应类型接口
interface ApiResponse<T> {
  code: number;
  data: T;
  message?: string;
}

export function getUserList(data: GetUserListDTO): Promise<ApiResponse<SaveUserDTO[]>> {
  return request.post(`/v2/serve/datasources/getUserList`, data);
}

export function createUser(data: SaveUserDTO): Promise<ApiResponse<boolean>> {
  return request.post('/v2/serve/datasources/addUser', data);
}

export function updateUser(data: SaveUserDTO): Promise<ApiResponse<boolean>> {
  return request.put('/v2/serve/datasources/updateUser', data);
}

export function deleteUser(data: DeleteDTO): Promise<ApiResponse<boolean>> {
  return request.post('/v2/serve/datasources/deleteUser', data);
}

export function getRoleList(data: GetRoleListDTO): Promise<ApiResponse<SaveRoleDTO[]>> {
  return request.post(`/v2/serve/datasources/getRoleList`, data);
}

export function createRole(data: SaveRoleDTO): Promise<ApiResponse<boolean>> {
  return request.post('/v2/serve/datasources/addRole', data);
}

export function updateRole(data: SaveRoleDTO): Promise<ApiResponse<boolean>> {
  return request.put('/v2/serve/datasources/updateRole', data);
}

export function deleteRole(data: DeleteDTO): Promise<ApiResponse<boolean>> {
  return request.post('/v2/serve/datasources/deleteRole', data);
}

export function getMenuList(data: GetMenuListDTO): Promise<ApiResponse<SaveMenuDTO[]>> {
  return request.post(`/v2/serve/datasources/getMenuList`, data);
}

export function createMenu(data: SaveMenuDTO): Promise<ApiResponse<boolean>> {
  return request.post('/v2/serve/datasources/addMenu', data);
}

export function updateMenu(data: SaveMenuDTO): Promise<ApiResponse<boolean>> {
  return request.put('/v2/serve/datasources/updateMenu', data);
}

export function deleteMenu(data: DeleteDTO): Promise<ApiResponse<boolean>> {
  return request.post('/v2/serve/datasources/deleteMenu', data);
}

export function getUserRoleList(data: GetUserRoleListDTO): Promise<ApiResponse<UserRoleItem[]>> {
  return request.post(`/v2/serve/datasources/getUsernRoleList`, data);
}
export function addUserRole(data: AddUserRoleDTO): Promise<ApiResponse<boolean>> {
  return request.post(`/v2/serve/datasources/addUserRole`, data);
}

export function deleteUserRole(id: number | string, data: DeleteDTO): Promise<ApiResponse<boolean>> {
  return request.post(`/v2/serve/datasources/deleteUserRole/${id}`, data);
}

export function getRoleMenuList(data: GetRoleMenuListDTO): Promise<ApiResponse<RoleMenuItem[]>> {
  return request.post(`/v2/serve/datasources/getRoleMenuList`, data);
}
export function addRoleMenu(data: AddRoleMenuDTO): Promise<ApiResponse<boolean>> {
  return request.post(`/v2/serve/datasources/addRoleMenu`, data);
}

export function deleteRoleMenu(id: number | string, data: DeleteDTO): Promise<ApiResponse<boolean>> {
  return request.post(`/v2/serve/datasources/deleteRoleMenu/${id}`, data);
}