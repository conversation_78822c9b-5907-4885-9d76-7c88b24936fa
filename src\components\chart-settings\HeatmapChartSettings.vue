<template>
  <common-chart-settings
    :options="props.options"
    chart-type="heatmap"
    @update="updateOptions"
  >
    <!-- 样式配置 -->
    <template #style-settings>
      <!-- 基本设置 -->
      <a-divider orientation="left">基本设置</a-divider>
      <a-form-item label="图表标题">
        <a-input v-model:value="chartConfig.title.text" placeholder="请输入图表标题" />
      </a-form-item>

      <!-- X轴设置 -->
      <a-divider orientation="left">X轴设置</a-divider>
      <a-form-item label="X轴名称">
        <a-input v-model:value="chartConfig.xAxis.name" placeholder="请输入X轴名称" />
      </a-form-item>
      <a-form-item label="X轴数据">
        <a-textarea 
          v-model:value="xAxisDataText" 
          :rows="3" 
          placeholder="请输入X轴数据，多个值用逗号分隔"
        />
      </a-form-item>

      <!-- Y轴设置 -->
      <a-divider orientation="left">Y轴设置</a-divider>
      <a-form-item label="Y轴名称">
        <a-input v-model:value="chartConfig.yAxis.name" placeholder="请输入Y轴名称" />
      </a-form-item>
      <a-form-item label="Y轴数据">
        <a-textarea 
          v-model:value="yAxisDataText" 
          :rows="3" 
          placeholder="请输入Y轴数据，多个值用逗号分隔"
        />
      </a-form-item>

      <!-- 视觉映射设置 -->
      <a-divider orientation="left">视觉映射设置</a-divider>
      <a-form-item label="最小值颜色">
        <a-input 
          v-model:value="visualMapMin.color" 
          type="color"
          style="width: 100px" 
        />
      </a-form-item>
      <a-form-item label="最大值颜色">
        <a-input 
          v-model:value="visualMapMax.color" 
          type="color"
          style="width: 100px" 
        />
      </a-form-item>
      <a-form-item label="最小值">
        <a-input-number 
          v-model:value="visualMapMin.value" 
          :min="0"
          style="width: 100px" 
        />
      </a-form-item>
      <a-form-item label="最大值">
        <a-input-number 
          v-model:value="visualMapMax.value" 
          :min="1"
          style="width: 100px" 
        />
      </a-form-item>

      <!-- 颜色与背景 -->
      <a-divider orientation="left">颜色与背景</a-divider>
      <a-form-item label="背景颜色">
        <a-input 
          v-model:value="chartConfig.backgroundColor" 
          placeholder="例如: #ffffff" 
          :addon-before="'颜色'"
          @change="updateConfig"
        />
      </a-form-item>

      <!-- 动画设置 -->
      <a-divider orientation="left">动画设置</a-divider>
      <a-form-item label="开启动画">
        <a-switch v-model:checked="chartConfig.animation" @change="updateConfig" />
      </a-form-item>
      <a-form-item v-if="chartConfig.animation" label="动画时长">
        <a-input-number 
          v-model:value="chartConfig.animationDuration" 
          :min="100" 
          :max="5000"
          :step="100"
          @change="updateConfig" 
        />
      </a-form-item>

      <!-- 提示框设置 -->
      <a-divider orientation="left">提示框设置</a-divider>
      <a-form-item label="显示提示框">
        <a-switch v-model:checked="tooltipShow" @change="updateTooltip" />
      </a-form-item>
      <a-form-item v-if="tooltipShow" label="提示框触发">
        <a-select v-model:value="tooltipTrigger" @change="updateTooltip">
          <a-select-option value="item">数据项</a-select-option>
          <a-select-option value="axis">坐标轴</a-select-option>
          <a-select-option value="none">无</a-select-option>
        </a-select>
      </a-form-item>
    </template>
  </common-chart-settings>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import CommonChartSettings from './CommonChartSettings.vue';
import type { ChartOptions } from '@/types/chart';

const props = defineProps({
  options: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update']);

// 视觉映射相关
const visualMapMin = ref({ value: 0, color: '#ebedf0' });
const visualMapMax = ref({ value: 100, color: '#196127' });

// 提示框设置
const tooltipShow = ref(true);
const tooltipTrigger = ref('item');

// 直接访问config部分的计算属性
const chartConfig = computed({
  get: () => {
    return props.options.config || getDefaultOptions().config;
  },
  set: (newConfig) => {
    updateConfig(newConfig);
  }
});

// 处理X轴数据
const xAxisDataText = computed({
  get: () => {
    return chartConfig.value.xAxis?.data?.join(',') || '';
  },
  set: (value) => {
    const data = value.split(',').map((item: string) => item.trim()).filter((item: string) => item);
    const config = { ...chartConfig.value };
    config.xAxis.data = data;
    updateConfig(config);
    updateDataMatrix();
  }
});

// 处理Y轴数据
const yAxisDataText = computed({
  get: () => {
    return chartConfig.value.yAxis?.data?.join(',') || '';
  },
  set: (value) => {
    const data = value.split(',').map((item: string) => item.trim()).filter((item: string) => item);
    const config = { ...chartConfig.value };
    config.yAxis.data = data;
    updateConfig(config);
    updateDataMatrix();
  }
});

// X轴和Y轴数据
const xAxisData = computed(() => chartConfig.value.xAxis?.data || []);
const yAxisData = computed(() => chartConfig.value.yAxis?.data || []);

// 数据矩阵
const dataMatrix = ref<number[][]>([]);

// 获取默认配置
const getDefaultOptions = (): any => {
  return {
    dataType: 'static',
    data: {
      columns: [],
      values: []
    },
    config: {
      type: 'heatmap',
      title: {
        text: '热力图'
      },
      tooltip: {
        position: 'top',
        show: true,
        trigger: 'item'
      },
      xAxis: { 
        type: 'category', 
        name: 'X轴',
        data: ['类别1', '类别2', '类别3'] 
      },
      yAxis: { 
        type: 'category', 
        name: 'Y轴',
        data: ['项目1', '项目2', '项目3'] 
      },
      visualMap: {
        min: 0,
        max: 100,
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '15%',
        inRange: {
          color: ['#ebedf0', '#c6e48b', '#7bc96f', '#239a3b', '#196127']
        }
      },
      backgroundColor: '#ffffff',
      animation: true,
      animationDuration: 1000,
      series: [{
        type: 'heatmap',
        data: [],
        label: {
          show: true
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }
  };
};

// 更新配置
const updateOptions = (newOptions: ChartOptions) => {
  emit('update', newOptions);
};

// 更新config
const updateConfig = (newConfig: any) => {
  const updatedOptions = {
    ...props.options,
    config: newConfig
  };
  emit('update', updatedOptions);
};

// 提示框相关
const updateTooltip = () => {
  chartConfig.value.tooltip = {
    ...(chartConfig.value.tooltip || {}),
    show: tooltipShow.value,
    trigger: tooltipTrigger.value
  };
  updateConfig(chartConfig.value);
};

// 更新数据矩阵
const updateDataMatrix = () => {
  const yLength = yAxisData.value.length;
  const xLength = xAxisData.value.length;
  
  // 初始化数据矩阵
  const newMatrix: number[][] = [];
  
  // 从series中获取已有数据
  const existingData = chartConfig.value.series?.[0]?.data || [];
  
  for (let y = 0; y < yLength; y++) {
    newMatrix[y] = [];
    for (let x = 0; x < xLength; x++) {
      // 查找已有数据中是否存在对应坐标的值
      const existingItem = existingData.find(
        (item: [number, number, number]) => item[0] === x && item[1] === y
      );
      
      newMatrix[y][x] = existingItem ? existingItem[2] : 0;
    }
  }
  
  dataMatrix.value = newMatrix;
};

// 从序列数据生成矩阵数据
const matrixToSeriesData = computed(() => {
  const data: [number, number, number][] = [];
  
  dataMatrix.value.forEach((row, yIndex) => {
    row.forEach((value, xIndex) => {
      data.push([xIndex, yIndex, value]);
    });
  });
  
  return data;
});

// 初始化默认配置
const initDefaultOptions = () => {
  const config = { ...chartConfig.value };
  
  if (!config.title) {
    config.title = { text: '热力图' };
  }

  if (!config.xAxis) {
    config.xAxis = { 
      type: 'category', 
      name: 'X轴',
      data: ['类别1', '类别2', '类别3'] 
    };
  }

  if (!config.yAxis) {
    config.yAxis = { 
      type: 'category', 
      name: 'Y轴',
      data: ['项目1', '项目2', '项目3'] 
    };
  }

  if (!config.visualMap) {
    config.visualMap = {
      min: 0,
      max: 100,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: '15%',
      inRange: {
        color: ['#ebedf0', '#c6e48b', '#7bc96f', '#239a3b', '#196127']
      }
    };
  }

  if (!config.series || !config.series[0]) {
    config.series = [{
      type: 'heatmap',
      data: [],
      label: {
        show: true
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }];
  }
  
  updateConfig(config);
};

// 更新图表配置并触发变更事件
const updateChartOptions = () => {
  const config = { ...chartConfig.value };
  
  // 确保series存在
  if (!config.series) {
    config.series = [{}];
  }
  
  // 确保series是数组
  if (!Array.isArray(config.series)) {
    config.series = [config.series];
  }
  
  // 确保第一个系列存在
  if (!config.series[0]) {
    config.series[0] = {};
  }
  
  // 设置类型为热力图
  config.series[0].type = 'heatmap';
  
  // 更新数据
  config.series[0].data = matrixToSeriesData.value;
  
  // 更新配置
  updateConfig(config);
};

// 初始化
onMounted(() => {
  initDefaultOptions();
  updateDataMatrix();
  
  // 初始化视觉映射设置
  if (chartConfig.value.visualMap) {
    visualMapMin.value = {
      value: chartConfig.value.visualMap.min || 0,
      color: chartConfig.value.visualMap.inRange?.color?.[0] || '#ebedf0'
    };
    
    visualMapMax.value = {
      value: chartConfig.value.visualMap.max || 100,
      color: chartConfig.value.visualMap.inRange?.color?.[chartConfig.value.visualMap.inRange.color.length - 1] || '#196127'
    };
  }
});

// 监听数据矩阵变化更新series数据
watch(dataMatrix, () => {
  updateChartOptions();
}, { deep: true });

// 监听视觉映射设置变化
watch([visualMapMin, visualMapMax], () => {
  const config = { ...chartConfig.value };
  if (config.visualMap) {
    config.visualMap.min = visualMapMin.value.value;
    config.visualMap.max = visualMapMax.value.value;
    
    if (!config.visualMap.inRange) {
      config.visualMap.inRange = { color: [] };
    }
    
    config.visualMap.inRange.color = [
      visualMapMin.value.color,
      '#c6e48b',
      '#7bc96f',
      '#239a3b',
      visualMapMax.value.color
    ];
    
    updateConfig(config);
  }
}, { deep: true });

// 监听配置变化
watch(() => props.options, () => {
  // updateDataMatrix();
  
  // 更新视觉映射设置
  // if (chartConfig.value.visualMap) {
  //   visualMapMin.value = {
  //     value: chartConfig.value.visualMap.min || 0,
  //     color: chartConfig.value.visualMap.inRange?.color?.[0] || '#ebedf0'
  //   };
    
  //   visualMapMax.value = {
  //     value: chartConfig.value.visualMap.max || 100,
  //     color: chartConfig.value.visualMap.inRange?.color?.[chartConfig.value.visualMap.inRange.color.length - 1] || '#196127'
  //   };
  // }
}, { deep: true });
</script>

<style scoped>
.chart-settings-container {
  padding: 16px;
}
</style> 