@use "@/assets/variable.scss";

// 思考链样式
.think, think {
  display: block;
  margin-bottom: 10px;
  color: #939393;
  font-size: 13px;
  line-height: 1.5;
  background: rgba(40, 109, 255, 0.05);
  padding: 10px 16px;
  border-radius: 6px;
  &:empty {
    display: none;
  }
}

// 聊天页标题样式
.chat-messages-title {
  position: absolute;
  top: 64px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  height: 80px;
  width: calc(100% - 220px);
  // background: linear-gradient(180deg, #DFE9FF 70%, rgba(221, 232, 255, 0) 100%);
  background: #f0f2f5;
  z-index: 1;
  font-weight: 500;
  .chat-title-container {
    padding: 14px 20px;
    background: #FFFFFF;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    display: flex;
    align-items: center;
    .icon {
      width: 36px;
      height: 36px;
      margin-right: 10px;
    }
    .title-container {
      display: flex;
      align-items: center;
      .title {
        text-align: left;
        margin-right: 10px;
      }
    }
    .sub-title {
      color: rgb(82 89 100/ 1);
      font-size: 14px;
      margin-top: 7px;
      text-align: left;
    }
  }
  &.mobile {
    height: 44px;
    font-size: 16px;
    justify-content: space-between;
    img {
      width: 24px;
      width: 24px;
    }
    .left-icon {
      margin-left: calc(12 * var(--base-unit));
    }
    .right-icon {
      margin-right: calc(12 * var(--base-unit));
    }
  }
}

// 聊天页内容样式
.chat-messages-container {
  position: relative;
  flex: 1;
  display: flex;
  justify-content: center;
  height: 100vh;
  line-height: 1.5;
  .el-scrollbar__wrap {
    width: 100%;
  }

  .chat-messages-content {
    margin: 0 auto;
    padding-top: 80px;
    padding-bottom: 10px;
    width: 100%;
    max-width: variable.$chat-width;
  }

  .chat-messages {
    width: 100%;
    flex: 1;
  }
}

// 消息默认样式
.message {
  display: flex;
  padding-bottom: 30px;

  &:last-child {
    padding-bottom: 0;
  }

  &.mobile {
    display: block;
    padding-bottom: calc(16 * var(--base-unit));
    .message-container {
      display: flex;
    }
  }

  .message-content {
    max-width: calc(100% - 108px);
    padding: 10px 16px;
    border-radius: 12px;
    box-sizing: border-box;
    line-height: 24px;
    text-align: left;
  }
}

// 聊天头像默认样式
.chat-avatar {
  width: 44px;
  height: 44px;
}

// 用户消息样式
.user-message {
  position: relative;
  justify-content: flex-end;
  .user-avatar {
    margin-left: 10px;
  }
  .message-content {
    background-color: #286DFF;
    color: white;
    box-shadow: 0px 4px 15px 0px rgba(21, 83, 214, 0.15);
    // 英文单词不换行
    word-break: keep-all; 
    // 超过一行的连续数字允许换行
    overflow-wrap: break-word; 
    // 强制数字换行
    word-wrap: break-word; 
    // 仅在空格处换行
    white-space: pre-wrap; 
  }
  &:hover{
    .user-message-actions {
      opacity: 1;
      &.mobile {
        bottom: -14px;
        right: 20px;
      }
    }
  }
  &-actions {
    opacity: 0;
    position: absolute;
    bottom: 4px;
    right: 64px;
    .icon {
      display: inline-block;
      .el-icon{
        width: 24px;
        height: 24px;
        margin-left: 6px;
        cursor: pointer;
        border-radius: 6px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 16px;
        color: #666666;
        &:hover {
          background-color: #F1F1F1;
        }
      }
    }
  }
  &.mobile {
    padding-right: calc(12 * var(--base-unit));
    padding-bottom: calc(16 * var(--base-unit));
    .message-container {
      justify-content: flex-end;
      .message-content {
        max-width: calc(351 * var(--base-unit));
        border-top-left-radius: calc(12 * var(--base-unit));
        border-top-right-radius: calc(12 * var(--base-unit));
        border-bottom-left-radius: calc(12 * var(--base-unit));
        border-bottom-right-radius: 0;
      }
    }
  }
}

// 机器人消息样式
.bot-message {
  justify-content: flex-start;
  .bot-avatar {
    margin-right: 10px;
  }
  .message-content {
    color: variable.$text-color;
    box-shadow: 0px 4px 15px 0px rgba(21, 83, 214, 0.1);
    background-color: #ffffff;
  }

  &.mobile {
    padding-left: calc(12 * var(--base-unit));
    .message-content {
      margin-top: calc(4 * var(--base-unit));
      max-width: calc(351 * var(--base-unit));
      border-top-left-radius: 0;
      border-top-right-radius: calc(12 * var(--base-unit));
      border-bottom-left-radius: calc(12 * var(--base-unit));
      border-bottom-right-radius: calc(12 * var(--base-unit));
    }
  }
}

// 默认输入框样式
#chat-input-container, .chat-input-container {
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: center;
  padding-bottom: 30px;
  .chat-input-header {
    display: flex;
    padding: 2px 10px;
    margin-bottom: 4px;
    align-items: center;
    justify-content: space-between;
    .left {
      display: flex;
      align-items: center;
    }
    .tool-icon {
      font-size: 16px;
      margin-left: 14px;
      cursor: pointer;
    }
    .tool-label {
      margin-left: 10px;
    }
  }
  &.chat-init-input {
    position: relative;
  }

  &.mobile {
    padding-bottom: 10px;
    .sidebar-line {
      display: flex;
      width: 100vw;
      overflow-y: auto;
      padding-left: calc(12 * var(--base-unit));
      margin-bottom: calc(10 * var(--base-unit));
      .sidebar-item {
        white-space: nowrap;
        text-overflow: ellipsis;
        display: flex;
        padding: calc(5 * var(--base-unit)) calc(6 * var(--base-unit));
        border-radius: calc(4 * var(--base-unit));
        border: 1px solid rgba(0, 0, 0, 0.08);
        align-items: center;
        margin-right: calc(8 * var(--base-unit));
        cursor: pointer;
        background: rgba(255, 255, 255, 1);
        img {
          width: calc(18 * var(--base-unit));
          height: calc(18 * var(--base-unit));
        }
        .label {
          font-size: calc(13 * var(--base-unit));
          font-weight: 500;
          color: #2D2F35;
          margin-left: calc(3 * var(--base-unit));
        }
      }
      .sidebar-item:last-child {
        margin-right: calc(12 * var(--base-unit));
      }
    }
    .chat-input-content {
      display: flex;
      justify-content: center;
    }
  }

  .chat-input-box {
    width: variable.$chat-width;
    background-color: #FFFFFF;
    border: 1px solid rgba(0, 0, 0, 0.08);
    // overflow: hidden;
    border-radius: 20px;
    padding: 10px;
    box-sizing: border-box;
    &.mobile {
      width: calc(351 * var(--base-unit));
      padding: calc(8 * var(--base-unit));
      border-radius: calc(8 * var(--base-unit));
      background: #FFFFFF;
      box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.05);
      padding-top: calc(4 * var(--base-unit)) !important;
      // border: none;
      .network-btn {
        &.mobile {
          margin-top: calc(10 * var(--base-unit));
          width: calc(94 * var(--base-unit));
          border-radius: calc(4 * var(--base-unit));
          display: flex;
          height: calc(30 * var(--base-unit));
          align-items: center;
          justify-content: center;
          font-size: calc(14 * var(--base-unit));
          background: rgba(40, 109, 255, 0.3);
          color: #FFFFFF;
          cursor: pointer;

          .label {
            margin-left: calc(5 * var(--base-unit));
          }
          
          img {
            width: calc(16 * var(--base-unit));
            height: calc(16 * var(--base-unit));
          }

          &.enable {
            background: #286DFF;
          }
        }
      }
      .divider {
        width: 100%;
        border-top: 1px solid #F6F7FA;
        margin-top: calc(10 * var(--base-unit));
      }
    }
    .chat-input-field-container {
      position: relative;

      .input-field {
        border: none;
        padding: 0px 4px;
        background-color: #FFFFFF;
        border-radius: 10px;
        font-size: 14px;
        position: relative;
        &::after {
          border-bottom: none;
        }
      }

      .input-bottom, &.mobile {
        display: flex;
        justify-content: space-between;
        padding-top: 6px;

        .left-side {
          display: flex;
          align-items: center;
          & > * {
            margin-right: 10px;
          }
        }

        .right-side {
          display: flex;
          align-items: center;
          & > * {
            margin-left: 10px;
          }
        }

        .network-btn {
          border-radius: 200px;
          display: flex;
          height: 30px;
          justify-content: center;
          align-items: center;
          padding: 0 10px;
          font-size: 14px;
          border: 1px solid rgba(0, 0, 0, 0.08);
          background: #FDFDFE;
          color: #5E6772;
          cursor: pointer;

          .label {
            margin-left: 4px;
          }
          
          img {
            width: 20px;
            height: 20px;
          }

          &.enable {
            background: #0084FF;
            color: #FFFFFF;
          }
        }

        .send-btn {
          width: 38px;
          height: 38px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          border-radius: 50%;
          background-color: variable.$primary-color;
          img {
            width: 20px;
            height: 20px;
          }
        }
        .send-btn-disabled {
          cursor: not-allowed;
          background-color: #F5F5F5;
        }
        .send-btn-stop {
          &::after {
            content: '';
            width: 12px;
            height: 12px;
            background-color: white;
            border-radius: 2px;
          }
        }
      }

      &.mobile {
        .send-btn-disabled {
          cursor: not-allowed;
          background: rgba(40, 109, 255, 0.3);
        }
      }
    }
  }
}

// 思考中样式
.think-loading-step {
  display: flex;
  color: #2D2F35;
  align-items: center;
  width: 110px;
  img {
    margin-right: 8px;
    width: 28px;
    height: 28px;
  }
}

// 建议问样式
.suggested-questions {
  margin-top: -10px;
  margin-left: 54px;
  &:empty {
    display: none;
  }
  &.mobile {
    margin-left: calc(12 * var(--base-unit));
  }
  .suggested-item {
    display: inline-block;
    border: 1px solid variable.$primary-color;
    background-color: white;
    padding: 4px 8px;
    border-radius: 20px;
    font-size: 12px;
    color: variable.$primary-color;
    margin: 0 6px 6px 0;
    cursor: pointer;
    &:empty {
      display: none;
    }
  }
}

.chat-view-container {
  min-width: 600px;
}
