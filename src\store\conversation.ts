import { defineStore } from 'pinia'
import { ref } from 'vue';
import type { ConversationDbVO } from "@/types/app";
import * as api from "@/api/chat";
import { useMessageDynamicStore } from '@/store/message_dynamic';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';

export const useConversationStore = defineStore('conversation', () => {
  const route = useRoute();
  const router = useRouter();
  
  const conversationList = ref<ConversationDbVO[]>([]);
  const activeConversation = ref<ConversationDbVO>();

  const getData = async () => {
    const res = await api.getConversationList();
    conversationList.value = res.data as ConversationDbVO[];
  }

  const setActiveConversation = (conversation?: ConversationDbVO) => {
    activeConversation.value = conversation
  }

  const addNewConversation = async (item: ConversationDbVO) => {
    conversationList.value.unshift(item)
    activeConversation.value = item
  }

  const deleteConversation = async (historyItem: ConversationDbVO, goChat: boolean) => {
    await api.deleteConversation({ con_uid: historyItem.conv_uid || '' });
    message.success('删除成功');
    getData();
    if (goChat) {
      router.replace('/chatData');
    }
  };

  const autoRenameConversation = async (id?: string) => {
    // if (!id) return
    // const res = await api.genTitle(id)
    // await getData()
    // const active = conversationList.value.find(item => item.id === id);
    // setActiveConversation(active)
  }

  return {
    conversationList,
    activeConversation,
    getData,
    setActiveConversation,
    addNewConversation,
    deleteConversation,
    autoRenameConversation
  }
})
