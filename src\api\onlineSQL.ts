import type {ApiResponse} from "@/types/api.ts";
import request from "@/utils/request.ts";

export function getDatasourceConfig(): Promise<ApiResponse<any>> {
    return request.get('/v1/chat/datasource/config');
}

export function handleSql(data: any): Promise<ApiResponse<any>> {
    return request.post('/v1/chat/data/sql', data);
}

export function getTableStructure(datasource_id: number): Promise<ApiResponse<any>> {
    return request.get(`/v2/serve/datasources/getTableStructure/${datasource_id}`);
}