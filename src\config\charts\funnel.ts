import legendOptions from '../e-charts/legend';
import tooltipOptions from '../e-charts/tooltip';

const funnelDefaultOptions = {
  dataType: 'static',
  data: {
    columns: ['阶段', '转化率'],
    values: [
      ['展现', 100],
      ['点击', 80],
      ['访问', 60],
      ['咨询', 40],
      ['订单', 20]
    ]
  },
  config: {
    type: 'funnel',
    tooltip: tooltipOptions,
    legend: legendOptions,
    series: [
      {
        type: 'funnel',
        sort: 'descending',
        data: []
      }
    ]
  },
  dataMapping: {
    funnel: {
      nameField: '阶段',
      valueField: '转化率'
    }
  }
};
export default funnelDefaultOptions; 