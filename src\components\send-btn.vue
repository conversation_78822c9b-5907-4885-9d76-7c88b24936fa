<template>
  <el-popover v-if="loading" effect="dark" content="停止生成">
    <template #reference>
      <div class="send-btn send-btn-stop" @click="stopReceive" />
    </template>
  </el-popover>
  <div v-else-if="disabled || loading" class="send-btn send-btn-disabled">
    <img :src="arrowUpIcon" />
  </div>
  <el-popover v-else effect="dark" content="发送">
    <template #reference>
      <div class="send-btn" @click="sendMessage">
        <img :src="arrowUpWhiteIcon" />
      </div>
    </template>
  </el-popover>
</template>

<script setup lang="ts">
import arrowUpWhiteIcon from '@/assets/images/icon/arrow-up-white.png';
import arrowUpIcon from '@/assets/images/icon/arrow-up.png';

defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
})

const emits = defineEmits([
  'stop',
  'submit'
]);

const sendMessage = () => {
  emits('submit')
}

const stopReceive = () => {
  emits('stop')
}

</script>