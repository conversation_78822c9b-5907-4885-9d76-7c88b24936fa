# 开发环境配置
NODE_ENV=development
# API基础URL
 VITE_API_BASE_URL=https://manasdatagpttest.manascloud.com/api/api
# VITE_API_BASE_URL=http://172.28.171.124:5670/api
# VITE_API_BASE_URL=http://172.28.174.28:5670/api
VITE_ROOT_API_BASE_URL=/root-api
# 应用标题
VITE_APP_TITLE=DataGPT-web (开发环境)
# 是否启用mock
VITE_ENABLE_MOCK=true
# 需要mock的API路径列表，逗号分隔，支持通配符：
# * 匹配单层路径: /user/* 匹配 /user/login 但不匹配 /user/profile/get
# ** 匹配多层路径: /api/** 匹配 /api 的所有子路径
VITE_MOCK_PATHS=/api/user/*,/api/prompt/*,/api/dashboard/**,/api/chart/**
# 请求超时时间（毫秒）
VITE_REQUEST_TIMEOUT=10000 