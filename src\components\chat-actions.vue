<template>
  <div class="btn-group">
    <div v-if="content" class="copy-btn" @click="() => onClickCopy(content)">
      <el-icon>
        <CopyDocument/>
      </el-icon>
      复制
    </div>
    <div v-if="showSaveButton" class="copy-btn" @click="() => onClickSave(content)">
      <el-icon>
        <Promotion/>
      </el-icon>
      保存为...
    </div>
    <div v-if="id && showDownload" class="copy-btn" @click="download">
      <el-icon>
        <Download/>
      </el-icon>
      下载Word
    </div>
    <div class="link-group">
      <el-popover placement="top" effect="dark" content="喜欢">
        <template #reference>
          <div class="link" @click="feedBack(1)">
            <van-icon v-if="isLike === 1" name="good-job"/>
            <van-icon v-else name="good-job-o"/>
          </div>
        </template>
      </el-popover>
      <div class="link" v-if="isLike === -1">
        <van-icon name="good-job" class="flipped-icon" @click="feedBack(-1)"/>
      </div>
      <a-popover
          trigger="click"
          placement="top"
          @visibleChange="handlePopoverVisibleChange"
          :autoAdjustOverflow="true"
          v-else
      >
        <template #content>
          <div class="unlike-container">
            <div class="unlike-button-container">
              <div class="unlike-item"
                   :class="{'active': unlikeItem.check}"
                   v-for="unlikeItem in unlikeList"
                   @click="unlikeItem.check = !unlikeItem.check">
                {{ unlikeItem.label }}
              </div>
            </div>
            <a-textarea
                v-model:value="unlikeReason"
                placeholder="描述一下具体问题或更优的答案"
                :auto-size="{ minRows: 2 }"
            />
            <div class="unlike-footer">
              <a-button type="primary" size="small" class="footer-item" @click="feedBack(-1)">确认</a-button>
            </div>
          </div>
        </template>
        <div class="tool-tip-custom">
          <div class="tool-tip-trigger" style="display: flex; align-items: center; cursor: pointer;">
            <div class="link">
              <van-icon name="good-job-o" class="flipped-icon"/>
            </div>
          </div>
          <div class="tool-tip-content">不喜欢</div>
        </div>
      </a-popover>
      <!-- <el-popover placement="top" effect="dark" content="不喜欢">
        <template #reference>
          <div class="link" @click="feedBack(-1)">
            <van-icon v-if="isLike === -1" name="good-job" class="flipped-icon"/>
            <van-icon v-else name="good-job-o" class="flipped-icon"/>
          </div>
        </template>
      </el-popover> -->
    </div>

    <!-- 添加保存图表弹窗 -->
    <a-modal
        v-model:visible="saveModalVisible"
        title="保存图表"
        @ok="handleSaveChart"
        :maskClosable="false"
        :keyboard="false"
    >
      <a-form  :label-col="{ style: { width: '120px' } }">
        <a-form-item label="图表名称" required>
          <a-input
              v-model:value="chartName"
              placeholder="请输入图表名称"
              @pressEnter="handleSaveChart"
              @change="nameError = ''"
          />
          <span v-if="nameError" style="color: red; font-size: 12px">{{ nameError }}</span>
        </a-form-item>
        <a-form-item label="选择看板组" required>
          <a-select
              v-model:value="selectedDashboardGroupId"
              placeholder="请选择看板组"
              :options="dashboardGroups.map(g => ({ label: g.title, value: g.id }))"
              style="width: 100%"
              @change="onDashboardGroupChange"
              @clear="onDashboardGroupClear"
          />
          <span v-if="groupError && !selectedDashboardGroupId" style="color: red; font-size: 12px">{{ groupError }}</span>
        </a-form-item>
        <a-form-item label="选择看板" required>
          <a-select
              v-model:value="selectedDashboardId"
              placeholder="请选择看板"
              :options="dashboards.map(g => ({ label: g.title, value: g.id }))"
              style="width: 100%"
          />
          <span v-if="dashboardError && !selectedDashboardId" style="color: red; font-size: 12px">{{ dashboardError }}</span>
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button @click="saveModalVisible = false">取消</a-button>
        <a-button type="primary" @click="handleSaveChart">保存</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import * as api from '@/api/chat'
import {ref, computed} from 'vue';
import type {PropType} from 'vue';
import {defineEmits} from 'vue';
import {CopyDocument, Promotion} from '@element-plus/icons-vue';
import type {Feedback, AddFeedbackDTO} from '@/types/app';
import {useRoute} from 'vue-router';
import {onClickCopy, decodeHTMLEntities} from '@/utils';
import {saveChat} from "@/api/chat";
import {useMessageDynamicStore} from "@/store/message_dynamic.ts";
import {message} from "ant-design-vue";
import { getDashboardGroups, getDashboardList } from '@/api/dashboard';

const props = defineProps({
  // 消息id
  id: {
    type: String,
    default: ''
  },
  content: {
    type: String,
    default: ''
  },
  likes: {
    type: Object as PropType<Feedback>,
    default: undefined
  },
  showDownload: {
    type: Boolean,
    default: false
  },
  chartType: {
    type: String,
    default: ''
  },
  selectedParamId: { // 新增 prop 接收 paramItem.id
    type: String,
    default: ''
  }
})

const data = ref<any>();
const sql = ref<string>('');
const type = ref<string>('');

// 定义要触发的自定义事件
const emits = defineEmits(['download', 'refreshFeedback', 'save']);
const route = useRoute();
const getConversationId = () => route.params.conversationId as string;

// 添加弹窗显示状态和图表名称
const saveModalVisible = ref(false);
const chartName = ref('');
const nameError = ref('');
const groupError = ref('');
const dashboardError = ref('');

const unlikeList = ref([
  {
    value: 'WRONG_ANSWER',
    label: '错误答案',
    check: false,
  },
  {
    value: 'WRONG_SOURCE',
    label: '错误来源',
    check: false,
  },
  {
    value: 'OUTDATED_CONTENT',
    label: '内容过时',
    check: false,
  },
  {
    value: 'UNREAL_CONTENT',
    label: '数据不准确',
    check: false,
  },
  {
    value: 'ILLEGAL_CONTENT',
    label: '非法内容',
    check: false,
  },
  {
    value: 'OTHERS',
    label: '其他',
    check: false,
  },
]);
const unlikeReason = ref<string>('');

const isLike = computed(() => {
  if (!props.likes) {
    return 0;
  }
  if (props.likes && props.likes.feedback_type) {
    if (props.likes.feedback_type === 'like') {
      return 1;
    } else {
      return -1;
    }
  }
  return 0;
});

// 定义计算属性，判断是否显示保存按钮
const showSaveButton = computed(() => {
  if (!props.content) return false;
  // 尝试匹配 SQL 内容
  const match = props.content.match(/content="([^"]*)"/);
  if (!match) return false;
  const decodedString = match[1].replace(/&quot;/g, '"');
  try {
    const jsonData = JSON.parse(decodedString);
    // 检查是否存在 SQL 内容
    console.log('showSaveButton', !!jsonData?.sql)
    return !!jsonData?.sql;
  } catch (error) {
    return false;
  }
});

//对话保存
// const onClickSave = async (content: string) => {
//   // 新增校验逻辑
//   const allowedChartTypes = [
//     'multi_line_chart', 'multi_measure_line_chart', 'multi_measure_column_chart',
//     'column_chart', 'pie_chart', 'scatter_plot', 'area_chart',
//     'heatmap', 'bar_chart', 'line_chart'
//   ];
//
//   if (!props.chartType || !allowedChartTypes.includes(props.chartType)) {
//     message.error('当前图表类型不支持保存');
//     return;
//   }
//
//   //组件传递获取出表格类型
//   const selectedChartType = props.chartType;
//
//   //组件传递获取出数据库连接id
//   const selectedParamId = props.selectedParamId; // 获取选中的 paramItem.id
//
//   const match = content.match(/content="([^"]*)"/);
//   if (!match) {
//     data.value = null;
//     sql.value = '';
//     type.value = '';
//     return;
//   }
//   // 对匹配到的字符串进行 HTML 实体解码并解析为 JSON 对象
//   const decodedString = match[1].replace(/&quot;/g, '"');
//   const jsonData = JSON.parse(decodedString);
//   sql.value = jsonData?.sql;
//   type.value = selectedChartType; //store中获取到下拉框中的值
//
//   const conversationId = getConversationId();
//   console.log('conversationId', conversationId);
//   const messageStore = useMessageDynamicStore.getStore(conversationId);
//   const key = `${conversationId}_${props.id}`;
//   const encode = messageStore.getChartConfig(key);
//
//   const params = {
//     sql: sql.value,
//     type: type.value,
//     connect_config_id: selectedParamId,
//     chart_config: JSON.stringify(encode)
//   }
//   console.log('params', params);
//
//   const response = await saveChat({
//     sql: decodeHTMLEntities(sql.value),
//     type: type.value,
//     connect_config_id: selectedParamId,
//     chart_config: JSON.stringify(encode) || '',
//     name: ''
//   });
//   if (response.success) {
//     message.success("保存成功");
//   }
//   console.log('response', response);
// }

// 看板组
const dashboardGroups = ref<any[]>([]);
const selectedDashboardGroupId = ref<number | null>(null);

// 看板
const dashboards = ref<any[]>([]);
const selectedDashboardId = ref<number | null>(null);

// 看板组选择change
const onDashboardGroupChange = async () =>{
  groupError.value = ''
  dashboards.value = []
  selectedDashboardId.value = ''
  const {data} = await getDashboardList(selectedDashboardGroupId.value)
  dashboards.value = data
}

// 清空看板组选择
const onDashboardGroupClear = () =>{
  dashboards.value = []
  selectedDashboardId.value = ''
}

const onClickSave = async (content: string) => {
  // 重置错误信息
  nameError.value = '';
  groupError.value = '';

  // 原有校验逻辑保持不变
  const allowedChartTypes = [
    'multi_line_chart', 'multi_measure_line_chart', 'multi_measure_column_chart',
    'column_chart', 'pie_chart', 'scatter_plot', 'area_chart',
    'heatmap', 'bar_chart', 'line_chart'
  ];

  if (!props.chartType || !allowedChartTypes.includes(props.chartType)) {
    message.error('当前图表类型不支持保存');
    return;
  }

  const match = content.match(/content="([^"]*)"/);
  if (!match) {
    data.value = null;
    sql.value = '';
    type.value = '';
    return;
  }
  // 对匹配到的字符串进行 HTML 实体解码并解析为 JSON 对象
  const decodedString = match[1].replace(/&quot;/g, '"');
  const jsonData = JSON.parse(decodedString);
  sql.value = jsonData?.sql;
  type.value = props.chartType;

  // 显示保存弹窗前拉取看板组
  const res = await getDashboardGroups();
  if (res.success) {
    dashboardGroups.value = res.data || [];
  }
  saveModalVisible.value = true;
}

// 添加保存图表的处理方法
const handleSaveChart = async () => {
  if (!chartName.value.trim()) {
    nameError.value = '图表名称不能为空';
    return;
  }
  if (selectedDashboardGroupId.value!== 0 && !selectedDashboardGroupId.value) {
    groupError.value = '请选择看板组';
    return;
  }
  if (selectedDashboardId.value!== 0 && !selectedDashboardId.value) {
    dashboardError.value = '请选择看板';
    return;
  }
  console.log('selectedDashboardId.value', selectedDashboardId.value);
  
  return
  try {
    const conversationId = getConversationId();
    const messageStore = useMessageDynamicStore.getStore(conversationId);
    const key = `${conversationId}_${props.id}`;
    const encode = messageStore.getChartConfig(key);

    const response = await saveChat({
      sql: decodeHTMLEntities(sql.value),
      type: type.value,
      connect_config_id: props.selectedParamId,
      chart_config: JSON.stringify(encode) || '',
      name: chartName.value.trim(), // 使用用户输入的图表名称
      dashboard_group_id: selectedDashboardGroupId.value,
      dashboard_id: selectedDashboardId.value,
    });

    if (response.success) {
      message.success("保存成功");
      saveModalVisible.value = false;
      chartName.value = ''; // 清空输入框
      selectedDashboardGroupId.value = null;
    } else {
      message.error(response.err_msg || "保存失败");
    }
  } catch (error) {
    console.error('保存失败:', error);
    message.error("保存失败");
  }
}

const download = () => {
  emits('download')
}

const resetUnlike = () => {
  unlikeList.value.forEach((item: any) => {
    item.check = false;
  })
  unlikeReason.value = '';
}

const handlePopoverVisibleChange = (visible: boolean) => {
  if (!visible) {
    resetUnlike();
  }
}

const feedBack = async (value: any) => {
  if (!props.id) return;
  if (isLike.value === value) {
    await api.cancelFeedback({
      conv_uid: getConversationId(),
      message_id: props.id + '',
    });
    emits('refreshFeedback', props.id);
  } else {
    let formData: AddFeedbackDTO = {
      conv_uid: getConversationId(),
      feedback_type: (value === 1 ? 'like' : 'unlike'),
      message_id: props.id + '',
    }
    if (value === -1) {
      const reasonTypes: string[] = [];
      unlikeList.value.forEach((item: any) => {
        if (item.check) {
          reasonTypes.push(item.value);
        }
      });
      formData.reason_types = reasonTypes;
      formData.remark = unlikeReason.value;
    }
    let res = await api.addFeedback(formData);
    emits('refreshFeedback', props.id, res.data);
    resetUnlike();
  }
}
</script>

<style lang="scss" scoped>
.unlike-container {
  width: 300px;
  position: relative;
  padding-bottom: 34px;

  .unlike-item {
    display: inline-block;
    height: auto;
    margin-inline-end: 8px;
    padding-inline: 7px;
    white-space: nowrap;
    background: #fafafa;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 12px;
    margin-bottom: 8px;
    cursor: pointer;

    &.active {
      border: 1px solid #0C75FC;
    }
  }

  .unlike-footer {
    display: flex;
    position: absolute;
    bottom: 0;
    right: 0;

    .footer-item {
      margin-left: 10px;
    }
  }
}

.tool-tip-custom {
  position: relative;
  display: inline-block;
  overflow: visible;
}

.tool-tip-content {
  visibility: hidden;
  width: max-content;
  max-width: 200px;
  background-color: #333;
  color: #fff;
  text-align: center;
  font-size: 12px;
  border-radius: 4px;
  padding: 4px 7px;
  position: absolute;
  bottom: 38px; /* 定位到上方 */
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

/* 添加三角箭头 */
.tool-tip-content::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #333 transparent transparent transparent;
}

.tool-tip-custom:hover .tool-tip-content {
  visibility: visible;
  opacity: 1;
}

.btn-group {
  display: flex;
  align-items: center;
  margin-top: 10px;

  &:empty {
    display: none;
  }
}

.flipped-icon {
  transform: rotate(180deg);
  display: inline-block;
}

.copy-btn {
  margin-right: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #5E6772;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 200px;
  height: 30px;
  line-height: 30px;
  padding: 0 10px;
  cursor: pointer;

  .el-icon {
    margin-right: 6px;
  }
}

.link-group {
  display: flex;
  border-left: 1px solid rgba(0, 0, 0, 0.08);
}

.link {
  margin-left: 10px;
  width: 26px;
  height: 26px;
  font-size: 22px;
  border-radius: 6px;
  line-height: 26px;
  text-align: center;
  cursor: pointer;
  color: #5E6772;

  &:hover {
    background: rgba(0, 0, 0, 0.05);
  }
}
</style>
