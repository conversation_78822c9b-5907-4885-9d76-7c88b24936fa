<template>
  <BaseChart 
    :chart-id="chartId" 
    :dashboard-id="dashboardId"
    @data-loaded="onDataLoaded"
    @error="onError"
  >
    <template #default>
      <div class="chart-wrapper" v-if="showChat">
        <v-chart :option="processedOptions" autoresize />
      </div>
    </template>
  </BaseChart>
</template>

<script setup lang="ts">
import { ref, markRaw } from 'vue';
import VChart from 'vue-echarts';
import BaseChart from './BaseChart.vue';

// 定义props
defineProps({
  chartId: {
    type: Number,
    required: true
  },
  dashboardId: {
    type: Number,
    required: true
  }
});

// 定义节点类型
interface TreemapNode {
  name: string;
  value: number;
  children?: TreemapNode[];
}

// 处理后的图表ECharts配置
const processedOptions = ref<any>({});
const showChat = ref<boolean>(false);

/**
 * 处理矩形树图数据
 */
const processTreemapData = (rawData: any): any => {
  // 防止未定义的数据
  if (!rawData) {
    console.error('TreemapChart: 原始数据为空');
    return false;
  }
  
  const { config = {}, data, dataMapping } = rawData;
  
  // 检查数据完整性
  if (!data || !Array.isArray(data.columns) || !Array.isArray(data.values)) {
    console.error('TreemapChart: 数据格式不正确', data);
    return false;
  }

  const { columns, values } = data;
  
  // 检查数据为空的情况
  if (columns.length === 0 || values.length === 0) {
    console.warn('TreemapChart: 数据为空');
    return false;
  }
  
  // 查找字段索引
  let parentFieldIdx = -1; 
  let childFieldIdx = 0;   
  let valueFieldIdx = 1;   
  
  // 获取映射配置
  if (dataMapping && dataMapping.treemap) {
    const { parentField, childField, valueField } = dataMapping.treemap;
    
    console.log('TreemapChart 映射配置:', dataMapping.treemap);
    
    // 获取每个字段在数据列中的索引位置
    if (parentField && typeof parentField === 'string') {
      const idx = columns.findIndex((col: string) => col === parentField);
      if (idx !== -1) {
        parentFieldIdx = idx;
        console.log(`找到parentField '${parentField}' 索引:`, idx);
      } else {
        console.warn(`找不到parentField '${parentField}' 在列中:`, columns);
      }
    }
    
    if (childField && typeof childField === 'string') {
      const idx = columns.findIndex((col: string) => col === childField);
      if (idx !== -1) {
        childFieldIdx = idx;
        console.log(`找到childField '${childField}' 索引:`, idx);
      } else {
        console.warn(`找不到childField '${childField}' 在列中:`, columns);
      }
    }
    
    if (valueField && typeof valueField === 'string') {
      const idx = columns.findIndex((col: string) => col === valueField);
      if (idx !== -1) {
        valueFieldIdx = idx;
        console.log(`找到valueField '${valueField}' 索引:`, idx);
      } else {
        console.warn(`找不到valueField '${valueField}' 在列中:`, columns);
      }
    }
  }
  
  console.log('TreemapChart 字段索引:', { parentFieldIdx, childFieldIdx, valueFieldIdx });
  console.log('TreemapChart 数据样例:', values.length > 0 ? values[0] : '无数据');
  
  // 创建矩形树图数据
  const treemapData: TreemapNode[] = [];
  
  try {
    // 根据是否有父节点字段来决定数据结构
    if (parentFieldIdx !== -1) {
      // 使用Map按父节点分组
      const parentMap = new Map<string, TreemapNode>();
      
      // 第一步：构建所有父节点
      for (const row of values) {
        if (!Array.isArray(row)) continue;
        
        const parent = row[parentFieldIdx] !== undefined ? String(row[parentFieldIdx]) : '';
        if (!parent) continue;
        
        if (!parentMap.has(parent)) {
          parentMap.set(parent, {
            name: parent,
            value: 0,
            children: []
          });
        }
      }
      
      // 第二步：将子节点添加到对应的父节点下
      for (const row of values) {
        if (!Array.isArray(row)) continue;
        
        const parent = row[parentFieldIdx] !== undefined ? String(row[parentFieldIdx]) : '';
        const child = row[childFieldIdx] !== undefined ? String(row[childFieldIdx]) : '';
        const value = row[valueFieldIdx] !== undefined 
          ? (typeof row[valueFieldIdx] === 'number' 
             ? row[valueFieldIdx] 
             : Number(row[valueFieldIdx]) || 0) 
          : 0;
        
        if (!parent || !child) continue;
        
        const parentNode = parentMap.get(parent);
        if (parentNode) {
          parentNode.children!.push({
            name: child,
            value: value
          });
          parentNode.value += value;
        }
      }
      
      // 将Map转换为数组
      for (const node of parentMap.values()) {
        if (node.children && node.children.length > 0) {
          treemapData.push(node);
        }
      }
    } else {
      // 没有父节点字段，构建扁平结构
      for (const row of values) {
        if (!Array.isArray(row)) continue;
        
        const name = row[childFieldIdx] !== undefined ? String(row[childFieldIdx]) : '';
        const value = row[valueFieldIdx] !== undefined 
          ? (typeof row[valueFieldIdx] === 'number' 
             ? row[valueFieldIdx] 
             : Number(row[valueFieldIdx]) || 0) 
          : 0;
        
        if (name) {
          treemapData.push({ name, value });
        }
      }
    }
    
    console.log('TreemapChart 生成的树形数据:', treemapData);
  } catch (error) {
    console.error('TreemapChart 处理数据时出错:', error);
  }
  
  // 构建返回配置
  return {
    ...config,
    title: {
      show: false
    },
    series: [{
      type: 'treemap',
      data: treemapData,
      label: {
        show: true,
        formatter: '{b}'
      },
      upperLabel: {
        show: true,
        height: 30
      },
      itemStyle: {
        borderColor: '#fff'
      },
      levels: [
        {
          itemStyle: {
            borderWidth: 0,
            gapWidth: 5
          }
        },
        {
          itemStyle: {
            borderWidth: 5,
            gapWidth: 1
          }
        },
        {
          colorSaturation: [0.35, 0.5],
          itemStyle: {
            borderWidth: 5,
            gapWidth: 1,
            borderColorSaturation: 0.6
          }
        }
      ]
    }]
  };
};

// 数据加载完成
const onDataLoaded = (rawData: any) => {
  try {
    console.log("TreemapChart收到原始数据:", rawData);
    
    // 检查dataMapping是否存在并输出
    if (rawData.dataMapping && rawData.dataMapping.treemap) {
      console.log("TreemapChart映射配置:", rawData.dataMapping.treemap);
    } else {
      console.warn("TreemapChart缺少映射配置!");
    }
    
    // 直接处理原始数据
    const processedConfig = processTreemapData(rawData);
    console.log("TreemapChart处理后配置:", processedConfig);
    if (!processedConfig) {
      showChat.value = false;
      return;
    }
    
    // 检查是否存在有效的series数据
    if (processedConfig.series && 
        processedConfig.series[0] && 
        processedConfig.series[0].data && 
        processedConfig.series[0].data.length > 0) {
      console.log("TreemapChart数据有效，共有节点:", processedConfig.series[0].data.length);
    } else {
      console.warn("TreemapChart数据无效或为空!");
    }
    
    // 使用markRaw避免Vue对复杂对象进行递归响应式处理
    processedOptions.value = markRaw(processedConfig);
    showChat.value = true;
  } catch (error) {
    console.error('处理矩形树图数据出错', error);
    processedOptions.value = {};
    showChat.value = false;
  }
};

// 数据加载错误
const onError = (error: string) => {
  console.error('图表数据加载错误', error);
};
</script>

<style scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;
}
</style> 