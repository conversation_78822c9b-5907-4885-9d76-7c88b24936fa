import legendOptions from '../e-charts/legend';
import tooltipOptions from '../e-charts/tooltip';

const gaugeDefaultOptions = {
  dataType: 'static',
  data: {
    columns: ['指标', '值'],
    values: [
      ['完成率', 75]
    ]
  },
  config: {
    type: 'gauge',
    tooltip: tooltipOptions,
    legend: legendOptions,
    series: [
        {
        type: 'gauge',
        detail: {
          valueAnimation: true,
          formatter: '{value}%'
        },
        data: []
      }
    ]
  },
  dataMapping: {
    gauge: {
      nameField: '指标',
      valueField: '值'
    }
  }
};
export default gaugeDefaultOptions; 