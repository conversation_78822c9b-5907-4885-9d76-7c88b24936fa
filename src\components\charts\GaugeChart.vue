<template>
  <BaseChart 
    :chart-id="chartId" 
    :dashboard-id="dashboardId"
    @data-loaded="onDataLoaded"
    @error="onError"
  >
    <template #default>
      <div class="chart-wrapper" v-if="showChat">
        <v-chart :option="processedOptions" autoresize />
      </div>
    </template>
  </BaseChart>
</template>

<script setup lang="ts">
import { ref, markRaw } from 'vue';
import VChart from 'vue-echarts';
import BaseChart from './BaseChart.vue';

// 定义props
defineProps({
  chartId: {
    type: Number,
    required: true
  },
  dashboardId: {
    type: Number,
    required: true
  }
});

// 处理后的图表ECharts配置
const processedOptions = ref<any>({});
const showChat = ref<boolean>(false);

/**
 * 处理仪表盘数据
 */
const processGaugeData = (rawData: any): any => {
  const { config = {}, data, dataMapping } = rawData;
  
  // 如果没有数据，返回基础配置
  if (!data?.columns || !data?.values || data.values.length === 0 || !Array.isArray(data.values)) {
    return false;
  }

  const { columns, values } = data;
  
  // 使用映射逻辑或默认逻辑
  let nameField = 0; // 默认第一列为名称
  let valueField = 1; // 默认第二列为值
  
  // 如果提供了映射配置，使用映射配置
  if (dataMapping) {
    // 查找nameField对应的列索引
    if (dataMapping.nameField) {
      const nameFieldIndex = columns.findIndex((col: string) => col === dataMapping.nameField);
      if (nameFieldIndex !== -1) {
        nameField = nameFieldIndex;
      }
    }
    
    // 查找valueField对应的列索引
    if (dataMapping.valueField) {
      const valueFieldIndex = columns.findIndex((col: string) => col === dataMapping.valueField);
      if (valueFieldIndex !== -1) {
        valueField = valueFieldIndex;
      }
    }
  }
  
  // 获取第一个数据行作为仪表盘值
  const firstRow = values[0];
  const name = String(firstRow[nameField] || '指标');
  const value = Number(firstRow[valueField]) || 0;
  console.log('value', value)
  
  // 获取配置或使用默认值
  const min = config.min || 0;
  const max = config.max || 100;
  
  // 构建返回配置
  return {
    ...config,
    title: {
      show: false
    },
    tooltip: {
      formatter: '{b}: {c}%'
    },
    series: [{
      type: 'gauge',
      min: min,
      max: max,
      radius: '90%',
      axisLine: {
        lineStyle: {
          width: 30,
          color: [
            [0.3, '#67e0e3'],
            [0.7, '#37a2da'],
            [1, '#fd666d']
          ]
        }
      },
      pointer: {
        itemStyle: {
          color: 'auto'
        },
        width: 6
      },
      axisTick: {
        distance: -30,
        length: 8,
        lineStyle: {
          color: '#fff',
          width: 2
        }
      },
      splitLine: {
        distance: -30,
        length: 30,
        lineStyle: {
          color: '#fff',
          width: 4
        }
      },
      axisLabel: {
        color: 'inherit',
        distance: 40,
        fontSize: 16
      },
      title: {
        fontSize: 14,
        offsetCenter: [0, '70%']
      },
      detail: {
        valueAnimation: true,
        fontSize: 14,
        offsetCenter: [0, '20%'],
        formatter: '{value}%',
        color: 'inherit'
      },
      data: [{
        value: value,
        name: name
      }]
    }]
  };
};

// 数据加载完成
const onDataLoaded = (rawData: any) => {
  try {
    // 直接处理原始数据
    const processedConfig = processGaugeData(rawData);
    if (!processedConfig) {
      processedOptions.value = {};
      showChat.value = false;
      return;
    }
    console.log("Gauge Data",processedConfig);
    // 使用markRaw避免Vue对复杂对象进行递归响应式处理
    processedOptions.value = markRaw(processedConfig);
    showChat.value = true; // 显示图表
  } catch (error) {
    console.error('处理仪表盘数据出错', error);
    processedOptions.value = {};
    showChat.value = false;
  }
};

// 数据加载错误
const onError = (error: string) => {
  console.error('图表数据加载错误', error);
};
</script>

<style scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;
}
</style> 