import { MockMethod } from 'vite-plugin-mock'
import pkg from 'mockjs'
const { Random } = pkg

// 提示词列表数据
const promptList = [
  {
    id: '1',
    name: '问题解答模板',
    type: 'system',
    content: '你是一个专业的解答助手，请根据用户的问题提供清晰、准确的答案。回答应该简洁明了，并尽可能提供具体的例子或参考资料。如果不确定，请表明这是基于现有知识的推测。',
    description: '用于问答场景的系统提示词，引导AI生成清晰的回答',
    tags: ['问答', '解释', '通用'],
    createdAt: '2023-10-01',
    updatedAt: '2023-10-01'
  },
  {
    id: '2',
    name: '创意写作助手',
    type: 'system',
    content: '你是一个创意写作助手，专长于生成有创意和富有想象力的内容。用户可能会要求你写故事、诗歌或其他创意文本。请确保你的回答有创意、原创，并符合用户的要求。',
    description: '用于创意写作的系统提示词，鼓励AI生成有创意的内容',
    tags: ['写作', '创意', '故事'],
    createdAt: '2023-10-02',
    updatedAt: '2023-10-02'
  },
  {
    id: '3',
    name: '代码审查专家',
    type: 'system',
    content: '你是一个专业的代码审查专家，擅长分析代码质量、性能和安全性。请对用户提供的代码进行详细分析，指出潜在问题，并提供改进建议。考虑代码可读性、性能优化、安全漏洞和最佳实践。',
    description: '用于代码审查的系统提示词，帮助识别代码问题并提供改进建议',
    tags: ['编程', '代码审查', '技术'],
    createdAt: '2023-10-03',
    updatedAt: '2023-10-03'
  },
  {
    id: '4',
    name: '用户问题模板',
    type: 'user',
    content: '我需要了解关于[主题]的信息，特别是[具体方面]。你能提供相关的解释和例子吗？',
    description: '用户问题的通用模板，可以根据需要填充具体主题',
    tags: ['问题', '模板', '用户'],
    createdAt: '2023-10-04',
    updatedAt: '2023-10-04'
  },
  {
    id: '5',
    name: '幽默回答模式',
    type: 'assistant',
    content: '当然，我很乐意帮助你！让我用一点幽默感来解释这个问题...[内容]希望这个解释既有用又让你开心！',
    description: '带有幽默风格的助手回答模板',
    tags: ['幽默', '风格', '助手'],
    createdAt: '2023-10-05',
    updatedAt: '2023-10-05'
  }
]

export default [
  // 获取提示词列表
  {
    url: '/api/prompt/list',
    method: 'get',
    response: () => {
      return {
        code: 0,
        data: promptList,
        message: '获取成功'
      }
    }
  },
  
  // 获取提示词详情
  {
    url: '/api/prompt/:id',
    method: 'get',
    response: (request) => {
      const { id } = request.params
      const prompt = promptList.find(item => item.id === id)
      
      if (prompt) {
        return {
          code: 0,
          data: prompt,
          message: '获取成功'
        }
      } else {
        return {
          code: 1,
          data: null,
          message: '提示词不存在'
        }
      }
    }
  },
  
  // 创建提示词
  {
    url: '/api/prompt/create',
    method: 'post',
    response: (request) => {
      const prompt = request.body
      const newPrompt = {
        id: Random.id(),
        ...prompt,
        createdAt: new Date().toISOString().split('T')[0],
        updatedAt: new Date().toISOString().split('T')[0]
      }
      
      promptList.unshift(newPrompt)
      
      return {
        code: 0,
        data: newPrompt,
        message: '创建成功'
      }
    }
  },
  
  // 更新提示词
  {
    url: '/api/prompt/:id',
    method: 'put',
    response: (request) => {
      const { id } = request.params
      const updateData = request.body
      
      const index = promptList.findIndex(item => item.id === id)
      if (index !== -1) {
        promptList[index] = {
          ...promptList[index],
          ...updateData,
          updatedAt: new Date().toISOString().split('T')[0]
        }
        
        return {
          code: 0,
          data: promptList[index],
          message: '更新成功'
        }
      } else {
        return {
          code: 1,
          data: null,
          message: '提示词不存在'
        }
      }
    }
  },
  
  // 删除提示词
  {
    url: '/api/prompt/:id',
    method: 'delete',
    response: (request) => {
      const { id } = request.params
      
      const index = promptList.findIndex(item => item.id === id)
      if (index !== -1) {
        promptList.splice(index, 1)
        
        return {
          code: 0,
          data: null,
          message: '删除成功'
        }
      } else {
        return {
          code: 1,
          data: null,
          message: '提示词不存在'
        }
      }
    }
  }
] as MockMethod[] 