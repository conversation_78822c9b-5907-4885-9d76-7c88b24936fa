
export interface GetUserListDTO {
  username?: string;
}

export interface SaveUserDTO {
  id?: string | number;
  username?: string;
  password?: string;
  nickname?: string;
  email?: string;
  phone?: string;
  status?: string;
  user_source?: number;
  create_time?: string;
  update_time?: string;
}

export interface SaveMenuDTO {
  id?: string | number;
  name?: string;
  url?: string;
  parent_id?: number;
  sort?: number;
  status?: string;
  create_time?: string;
  update_time?: string;
  children?: SaveMenuDTO[];
}

export interface DeleteDTO {
  ids?: (string | number)[];
}

export interface Role {
  id?: string | number;
  name: string;
  description: string;
  status: number;
  statusBoolean?: boolean;
}

export interface GetRoleListDTO {
  name?: string;
}

export interface GetMenuListDTO {
  name?: string;
}

export interface SaveRoleDTO {
  id?: string | number;
  name?: string;
  description?: string;
  status?: string;
  create_time?: string
  update_time?: string;
}

export interface GetUserRoleListDTO {
  user_id?: string | number;
}

export interface UserRoleItem {
  id : string | number;
  user_id? : string | number;
  role_id : string | number;
  role_name: string;
}

export interface AddUserRoleDTO {
  user_roles: {
    user_id: string | number;
    role_id: string | number;
  }[];
}

export interface GetRoleMenuListDTO {
  role_id?: string | number;
}
export interface RoleMenuItem {
  id : string | number;
  role_id? : string | number;
  menu_id : string | number;
  menu_name: string;
  children?: RoleMenuItem[];
}

export interface AddRoleMenuDTO {
  role_menus: {
    role_id: string | number;
    menu_id: string | number;
  }[];
}
