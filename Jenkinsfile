pipeline {
    environment {
        registry = 'kc-manage-storage.manascloud.com:34480/db-gpt'
        registryUser = 'a70825'
        registryPass = '4v%Dl!CJzS]/7nAfd-JD5kg[0pqY2<D0'
        imageName = 'manas-dbgpt-web'
    }
    agent {
        label 'agent-150'
    }
    stages {
        stage ("构建") {
            agent {
                docker {
                    image 'kc-manage-storage.manascloud.com:34480/manas-library/node:22-alpine'
                    reuseNode true
                }
            }
            steps{
                sh 'npm install -f --registry=https://registry.npmmirror.com/ && npm run build'
            }

        }
        stage ("创建镜像") {
            agent none
            steps {
                sh 'docker build -t ${registry}/${imageName}:${Version} .'
            }
        }
        stage ("推送镜像") {
            agent none
            steps {
                sh 'docker login https://kc-manage-storage.manascloud.com:34480 -u ${registryUser} -p ${registryPass} && docker push ${registry}/${imageName}:${Version}'
            }
        }
        stage ("清理本地镜像") {
            agent none
            steps {
                sh 'docker rmi ${registry}/${imageName}:${Version}'
            }
        }
    }
}