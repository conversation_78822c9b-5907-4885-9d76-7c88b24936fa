<template>
  <BaseChart 
    :chart-id="chartId" 
    :dashboard-id="dashboardId"
    @data-loaded="onDataLoaded"
    @error="onError"
  >
    <template #default>
      <div class="chart-wrapper" v-if="showChat">
        <v-chart :option="processedOptions" autoresize />
      </div>
    </template>
  </BaseChart>
</template>

<script setup lang="ts">
import { ref, markRaw } from 'vue';
import VChart from 'vue-echarts';
import BaseChart from './BaseChart.vue';

defineProps({
  chartId: {
    type: Number,
    required: true
  },
  dashboardId: {
    type: Number,
    required: true
  }
});

// 处理后的图表ECharts配置
const processedOptions = ref<any>({});
const showChat = ref<boolean>(false);

/**
 * 处理柱状图数据
 */
const processBarData = (rawData: any): any => {
  console.log("柱状图数据", rawData);
  
  const { config = {}, data, dataMapping } = rawData;
  
  // 如果没有数据，返回基础配置
  if (!data?.columns || !data?.values || data.values.length === 0) {
    return false;
  }

  const { columns, values } = data;
  
  // 使用映射逻辑或默认逻辑
  let xField = 0; // 默认第一列为x轴数据
  let yFields: number[] = []; // 需要映射到Y轴的列索引数组
  let seriesNames: string[] = []; // 系列名称数组
  
  // 如果提供了映射配置，使用映射配置
  if (dataMapping) {
    // 查找xField对应的列索引
    if (dataMapping.xField) {
      const xFieldIndex = columns.findIndex((col: string) => col === dataMapping.xField);
      if (xFieldIndex !== -1) {
        xField = xFieldIndex;
      }
    }
    
    // 查找yFields对应的列索引数组
    if (dataMapping.yFields && Array.isArray(dataMapping.yFields)) {
      yFields = dataMapping.yFields
        .map((field: string) => columns.findIndex((col: string) => col === field))
        .filter((index: number) => index !== -1);
    }
    
    // 使用提供的系列名称
    if (dataMapping.seriesNames && Array.isArray(dataMapping.seriesNames)) {
      seriesNames = dataMapping.seriesNames;
    }
  }
  
  // 如果没有指定yFields或yFields为空，默认使用除了xField以外的所有列
  if (yFields.length === 0) {
    for (let i = 0; i < columns.length; i++) {
      if (i !== xField) {
        yFields.push(i);
      }
    }
  }
  
  // 如果没有指定系列名称或名称不够，使用列名
  while (seriesNames.length < yFields.length) {
    const yFieldIndex = yFields[seriesNames.length];
    seriesNames.push(columns[yFieldIndex] || `系列${seriesNames.length + 1}`);
  }
  
  // 获取X轴数据并确保转换为字符串
  const categoryData = values.map((row: any[]) => String(row[xField]));
  
  // 构建系列数据
  const series = yFields.map((fieldIndex, index) => {
    const seriesData = values.map((row: any[]) => {
      // 确保数值类型转换
      const value = Number(row[fieldIndex]);
      return isNaN(value) ? 0 : value;
    });
    
    return {
      name: seriesNames[index] || `系列${index + 1}`,
      type: 'bar',
      data: seriesData
    };
  });
  
  // 构建返回配置
  return {
    ...config,
    title: {
      show: false
    },
    xAxis: {
      type: 'category',
      data: categoryData,
      axisLabel: {
        show: true,
        interval: 0,
        rotate: categoryData.length > 8 ? 45 : 0
      }
    },
    yAxis: {
      type: 'value'
    },
    series: series
  };
};

// 数据加载完成
const onDataLoaded = (rawData: any) => {
  try {
    // 直接处理原始数据
    const processedConfig = processBarData(rawData);
    if (!processedConfig) {
      showChat.value = false;
      return;
    }
    
    // 使用markRaw避免Vue对复杂对象进行递归响应式处理
    processedOptions.value = markRaw(processedConfig);
    showChat.value = true;
  } catch (error) {
    console.error('处理柱状图数据出错', error);
    processedOptions.value = {};
    showChat.value = false;
  }
};

// 数据加载错误
const onError = (error: string) => {
  console.error('图表数据加载错误', error);
};
</script>

<style scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;
}
</style> 